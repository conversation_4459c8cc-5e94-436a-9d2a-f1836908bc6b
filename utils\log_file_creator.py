#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
日志文件创建模块

提供异步创建TXT日志文件的功能，记录画布利用率、图片信息等重要数据
"""

import os
import time
from datetime import datetime
from typing import Dict, Any, Optional, Union

from PyQt6.QtCore import QThread, pyqtSignal
from utils.time_helper import get_timestamp


def format_cm_value(value: Union[int, float]) -> str:
    """格式化厘米值，当小数点后都是0时不显示小数点

    Args:
        value: 要格式化的厘米值

    Returns:
        格式化后的字符串
    """
    # 如果值是整数或小数点后都是0，则显示为整数
    if isinstance(value, (int, float)):
        if value == int(value):
            return str(int(value))
        else:
            # 否则保留小数点
            return str(value)
    return str(value)  # 如果不是数字，直接返回字符串形式


class LogFileCreator(QThread):
    """异步创建TXT日志文件的工作线程

    该线程专门用于异步创建TXT日志文件，不会阻塞主线程
    """

    # 定义信号
    log_signal = pyqtSignal(str)  # 日志信号
    finished_signal = pyqtSignal(bool, str)  # 完成信号，参数为成功标志和消息

    def __init__(self, tiff_path: str, layout_data: Dict[str, Any]):
        """初始化日志文件创建器

        Args:
            tiff_path: TIFF文件路径
            layout_data: 布局数据字典，包含创建日志文件所需的所有信息
        """
        super().__init__()
        self.tiff_path = tiff_path
        self.layout_data = layout_data

    def run(self):
        """执行日志文件创建任务"""
        try:
            # 检查TIFF路径是否有效
            if not self.tiff_path or not isinstance(self.tiff_path, str):
                error_msg = f"无效的TIFF文件路径: {self.tiff_path}"
                self.log_signal.emit(error_msg)
                self.finished_signal.emit(False, error_msg)
                return

            # 创建同名的TXT文件路径
            txt_path = os.path.splitext(self.tiff_path)[0] + ".txt"
            self.log_signal.emit(f"开始创建日志文件: {txt_path}")

            # 确保输出目录存在
            output_dir = os.path.dirname(txt_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
                self.log_signal.emit(f"创建输出目录: {output_dir}")

            # 从布局数据中提取必要的信息
            canvas_name = self.layout_data.get('canvas_name', '')
            material_name = self.layout_data.get('material_name', '')
            canvas_sequence = self.layout_data.get('canvas_sequence', 0)
            canvas_width_m = self.layout_data.get('canvas_width_m', 0)
            ppi = self.layout_data.get('ppi', 72)
            image_spacing_cm = self.layout_data.get('image_spacing_cm', 0)
            horizontal_expansion_cm = self.layout_data.get('horizontal_expansion_cm', 0)
            layout_info = self.layout_data.get('layout_info', {})
            start_time = self.layout_data.get('start_time', None)
            images_arranged = self.layout_data.get('images_arranged', 0)

            # 获取当前时间
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 准备日志内容
            log_content = []
            log_content.append(f"# {canvas_name} 画布信息")
            log_content.append(f"生成时间: {current_time}")
            log_content.append(f"材质名称: {material_name}")
            log_content.append(f"画布序号: {canvas_sequence}")
            log_content.append(f"TIFF文件: {os.path.basename(self.tiff_path)}")
            log_content.append("")

            # 添加画布设置信息
            log_content.append("## 画布设置")
            log_content.append(f"画布宽度: {canvas_width_m:.2f}米 ({int(canvas_width_m*100)}厘米)")
            log_content.append(f"分辨率: {ppi} PPI")
            log_content.append(f"图片间距: {image_spacing_cm} 厘米")
            log_content.append(f"水平扩展: {horizontal_expansion_cm} 厘米")
            log_content.append("")

            # 添加布局信息
            log_content.append("## 布局信息")

            # 画布尺寸
            # 计算原始宽幅（不包含水平拓展）
            original_width_cm = canvas_width_m * 100
            original_width_px = int(original_width_cm * ppi / 2.54)

            # 计算包含水平拓展的宽度（用于显示画布尺寸）
            canvas_width_cm = original_width_cm + horizontal_expansion_cm
            canvas_width_px = int(canvas_width_cm * ppi / 2.54)
            canvas_height_px = layout_info.get('max_height', 0)

            # 确保画布高度不为0
            if canvas_height_px <= 0:
                # 尝试从图片信息中计算最大高度
                max_y = 0
                images_info = self.layout_data.get('images_info', [])
                skyline_images = self.layout_data.get('skyline_images', [])

                # 检查增强版装箱器的图片
                for img in images_info:
                    if isinstance(img, (tuple, list)) and len(img) >= 5:
                        _, y, _, height, _ = img
                        max_y = max(max_y, y + height)
                    elif isinstance(img, dict):
                        y = img.get('y', 0)
                        height = img.get('height', 0)
                        max_y = max(max_y, y + height)

                # 检查天际线装箱器的图片
                for img in skyline_images:
                    if isinstance(img, dict):
                        y = img.get('y', 0)
                        height = img.get('height', 0)
                        max_y = max(max_y, y + height)

                # 如果仍然为0，设置一个最小值
                canvas_height_px = max(max_y, 1)
                self.log_signal.emit(f"警告: 画布高度为0，已重新计算为 {canvas_height_px} 像素")

            # 显示画布尺寸（包含水平拓展）
            log_content.append(f"画布尺寸: {canvas_width_px}x{canvas_height_px} 像素 (包含水平拓展{horizontal_expansion_cm}厘米)")
            # 显示原始画布尺寸（不包含水平拓展）
            log_content.append(f"原始尺寸: {original_width_px}x{canvas_height_px} 像素 (不包含水平拓展)")

            # 利用率 - 使用新公式：(图片面积+间距面积+水平拓展面积)/画布面积
            utilization = layout_info.get('utilization', 0) * 100

            # 如果利用率为0但有图片，重新计算利用率
            if utilization <= 0 and canvas_height_px > 0:
                # 尝试从图片信息中计算利用率
                # 使用包含水平拓展的画布宽度
                canvas_area = canvas_width_px * canvas_height_px
                self.log_signal.emit(f"计算利用率使用画布宽度: {canvas_width_px}像素（包含水平拓展）")

                # 计算图片面积
                image_area = 0
                all_images = []

                # 处理增强版装箱器的图片
                for img in self.layout_data.get('images_info', []):
                    if isinstance(img, (tuple, list)) and len(img) >= 5:
                        _, _, width, height, data = img
                        image_area += width * height
                        if data and isinstance(data, dict):
                            all_images.append(data)
                    elif isinstance(img, dict):
                        width = img.get('width', 0)
                        height = img.get('height', 0)
                        image_area += width * height
                        all_images.append(img)

                # 处理天际线/俄罗斯方块装箱器的图片
                for img in self.layout_data.get('skyline_images', []):
                    if isinstance(img, dict):
                        width = img.get('width', 0)
                        height = img.get('height', 0)
                        image_area += width * height
                        all_images.append(img)

                # 计算间距面积 - 优化计算：为每个图片增加间距面积
                spacing_cm = image_spacing_cm
                spacing_area = 0

                # 获取图片总数
                total_images_count = len(self.layout_data.get('images_info', [])) + len(self.layout_data.get('skyline_images', []))

                if total_images_count > 0 and spacing_cm > 0:
                    # 优化计算：为每个图片增加间距面积
                    # 简化计算：在图片的宽度和高度上各增加间距
                    spacing_px = int(spacing_cm * ppi / 2.54)
                    avg_width = image_area / total_images_count / (canvas_height_px / total_images_count) if total_images_count > 0 else 0
                    avg_height = canvas_height_px / total_images_count if total_images_count > 0 else 0

                    # 为每个图片增加间距面积
                    for _ in range(total_images_count):
                        width_with_spacing = avg_width + spacing_px
                        height_with_spacing = avg_height + spacing_px
                        spacing_area += width_with_spacing * height_with_spacing - avg_width * avg_height

                # 计算水平拓展面积
                expansion_area = horizontal_expansion_cm * ppi / 2.54 * canvas_height_px

                # 计算总占用面积 = 图片面积 + 间距面积 + 水平拓展面积
                total_used_area = image_area + spacing_area + expansion_area

                # 计算利用率
                if canvas_area > 0:
                    utilization = (total_used_area / canvas_area) * 100
                    self.log_signal.emit(f"警告: 利用率为0，已重新计算为 {utilization:.2f}%")
                    self.log_signal.emit(f"图片面积: {image_area}, 间距面积: {spacing_area:.0f}, 水平拓展面积: {expansion_area:.0f}")

                    # 保留两位小数，统一百分比格式
                    utilization = round(utilization * 100) / 100
                    self.log_signal.emit(f"利用率计算结果: {utilization:.2f}%")

            # 获取图片分类统计信息（仅用于日志显示，不影响利用率计算）
            class_a_count = 0
            class_b_count = 0
            class_c_count = 0

            # 从布局信息中获取图片分类统计
            if 'stats' in layout_info:
                total_images = layout_info.get('stats', {}).get('total_items', 0)
            self.log_signal.emit(f"图片统计: 共 {total_images} 个图片（统一处理）")

            # 显示优化后的利用率计算公式
            log_content.append(f"画布利用率: {utilization:.2f}% (图片面积+间距面积+水平拓展面积/画布面积)")

            # 图片数量
            total_images = layout_info.get('stats', {}).get('total_items', 0)

            # 如果从stats中获取的图片总数为0，但实际有图片，使用分类统计或all_images长度作为备选值
            if total_images <= 0:
                # 尝试从分类统计中获取
                if 'stats' in layout_info:
                    total_images = layout_info.get('stats', {}).get('total_items', 0)

                # 如果仍然为0，尝试从图片信息中获取
                if total_images <= 0:
                    # 获取图片信息
                    images_info = self.layout_data.get('images_info', [])
                    tetris_images = self.layout_data.get('tetris_images', [])

                    # 兼容旧版本 - 如果没有tetris_images但有skyline_images，使用skyline_images
                    if not tetris_images and 'skyline_images' in self.layout_data:
                        tetris_images = self.layout_data.get('skyline_images', [])

                    # 如果没有直接提供的images_info，尝试从布局信息中获取
                    if not images_info and 'images' in layout_info:
                        images_info = layout_info.get('images', [])

                    # 计算总图片数
                    total_images = len(images_info) + len(tetris_images)

                    # 更新layout_info中的stats
                    if 'stats' not in layout_info:
                        layout_info['stats'] = {}
                    layout_info['stats']['total_items'] = total_images
                    self.log_signal.emit(f"警告: 图片总数为0，已重新计算为 {total_images}")

            log_content.append(f"图片总数: {total_images}")

            # 旋转图片数量 - 从图片详情中重新计算，确保准确性
            rotated_count = 0

            # 获取图片信息
            all_images = []
            images_info = self.layout_data.get('images_info', [])
            tetris_images = self.layout_data.get('tetris_images', [])

            # 兼容旧版本 - 如果没有tetris_images但有skyline_images，使用skyline_images
            if not tetris_images and 'skyline_images' in self.layout_data:
                tetris_images = self.layout_data.get('skyline_images', [])

            # 合并所有图片信息
            for img in images_info:
                if isinstance(img, (tuple, list)) and len(img) >= 5:
                    _, _, _, _, data = img
                    if data and isinstance(data, dict):
                        all_images.append(data)
                elif isinstance(img, dict):
                    all_images.append(img)

            for img in tetris_images:
                if isinstance(img, dict):
                    all_images.append(img)

            # 计算实际旋转图片数量
            for img in all_images:
                if img.get('need_rotation', False):
                    rotated_count += 1

            # 更新layout_info中的旋转图片数量
            if 'stats' not in layout_info:
                layout_info['stats'] = {}
            layout_info['stats']['rotated_items'] = rotated_count

            # 计算旋转百分比
            rotated_images = rotated_count
            if total_images > 0:
                rotated_percent = (rotated_images / total_images) * 100
                log_content.append(f"旋转图片: {rotated_images}/{total_images} ({rotated_percent:.2f}%)")
                log_content.append(f"旋转率: {rotated_percent:.2f}%")  # 添加单独的旋转率行
            else:
                log_content.append(f"旋转图片: 0/0 (0.00%)")
                log_content.append(f"旋转率: 0.00%")

            # 不再计算和显示行利用率
            log_content.append("")

            # 添加处理时间信息
            if start_time:
                total_time = get_timestamp() - start_time
                log_content.append("## 处理时间")
                log_content.append(f"总处理时间: {total_time:.2f}秒")

                if images_arranged > 0:
                    avg_speed = total_time / images_arranged
                    log_content.append(f"平均排列速度: {avg_speed:.3f}秒/块")
                log_content.append("")

            # 添加图片详细信息
            try:
                # 获取图片信息
                images_info = self.layout_data.get('images_info', [])
                tetris_images = self.layout_data.get('tetris_images', [])

                # 兼容旧版本 - 如果没有tetris_images但有skyline_images，使用skyline_images
                if not tetris_images and 'skyline_images' in self.layout_data:
                    tetris_images = self.layout_data.get('skyline_images', [])

                # 如果没有直接提供的images_info，尝试从布局信息中获取
                if not images_info and 'images' in layout_info:
                    images_info = layout_info.get('images', [])

                # 合并增强版装箱器和天际线装箱器的图片信息
                all_images = []
                processed_paths = set()  # 用于跟踪已处理的图片路径，防止重复

                # 处理增强版装箱器的图片信息
                for img in images_info:
                    try:
                        # 检查是否是标准格式（元组或列表）
                        if isinstance(img, (tuple, list)) and len(img) >= 5:
                            x, y, width, height, data = img
                            if data and isinstance(data, dict):
                                # 获取图片路径和名称
                                path = data.get('path', '未知路径')
                                name = data.get('name', '未命名')

                                # 创建唯一标识符，防止重复
                                img_key = f"{path}_{x}_{y}"

                                # 如果已经处理过这个图片，跳过
                                if img_key in processed_paths:
                                    continue

                                processed_paths.add(img_key)

                                all_images.append({
                                    'x': x,
                                    'y': y,
                                    'width': width,
                                    'height': height,
                                    'name': name,
                                    'image_class': data.get('image_class', 'UNIFIED'),
                                    'need_rotation': data.get('need_rotation', False) or data.get('rotated', False),
                                    'path': path,
                                    'width_cm': data.get('original_width_cm', data.get('width_cm', 0)),
                                    'height_cm': data.get('original_height_cm', data.get('height_cm', 0)),
                                    'table_width_cm': data.get('table_width_cm', data.get('original_width_cm', data.get('width_cm', 0))),
                                    'table_height_cm': data.get('table_height_cm', data.get('original_height_cm', data.get('height_cm', 0)))
                                })
                        elif isinstance(img, dict):
                            # 如果是字典格式，直接使用
                            # 获取图片路径和名称
                            path = img.get('path', '未知路径')
                            name = img.get('name', '未命名')
                            x = img.get('x', 0)
                            y = img.get('y', 0)

                            # 创建唯一标识符，防止重复
                            img_key = f"{path}_{x}_{y}"

                            # 如果已经处理过这个图片，跳过
                            if img_key in processed_paths:
                                continue

                            processed_paths.add(img_key)

                            all_images.append({
                                'x': x,
                                'y': y,
                                'width': img.get('width', 0),
                                'height': img.get('height', 0),
                                'name': name,
                                # 移除image_class字段，统一处理
                                'need_rotation': img.get('need_rotation', False) or img.get('rotated', False),
                                'path': path,
                                'width_cm': img.get('original_width_cm', img.get('width_cm', 0)),
                                'height_cm': img.get('original_height_cm', img.get('height_cm', 0)),
                                'table_width_cm': img.get('table_width_cm', img.get('original_width_cm', img.get('width_cm', 0))),
                                'table_height_cm': img.get('table_height_cm', img.get('original_height_cm', img.get('height_cm', 0)))
                            })
                    except Exception as e:
                        self.log_signal.emit(f"处理图片信息失败: {str(e)}")
                        continue

                # 处理俄罗斯方块装箱器的图片信息
                for img in tetris_images:
                    try:
                        if isinstance(img, dict):
                            data = img.get('data', {})
                            if data and isinstance(data, dict):
                                # 获取图片路径和名称
                                path = data.get('path', '未知路径')
                                name = data.get('name', '未命名')
                                x = img.get('x', 0)
                                y = img.get('y', 0)

                                # 创建唯一标识符，防止重复
                                img_key = f"{path}_{x}_{y}"

                                # 如果已经处理过这个图片，跳过
                                if img_key in processed_paths:
                                    continue

                                processed_paths.add(img_key)

                                all_images.append({
                                    'x': x,
                                    'y': y,
                                    'width': img.get('width', 0),
                                    'height': img.get('height', 0),
                                    'name': name,
                                    'image_class': data.get('image_class', 'UNIFIED'),
                                    'need_rotation': data.get('need_rotation', False),
                                    'path': path,
                                    'is_gap_fill': img.get('is_gap_fill', False),  # 标记是否为行空隙填充
                                    'width_cm': data.get('original_width_cm', data.get('width_cm', 0)),
                                    'height_cm': data.get('original_height_cm', data.get('height_cm', 0)),
                                    'table_width_cm': data.get('table_width_cm', data.get('original_width_cm', data.get('width_cm', 0))),
                                    'table_height_cm': data.get('table_height_cm', data.get('original_height_cm', data.get('height_cm', 0)))
                                })
                    except Exception as e:
                        self.log_signal.emit(f"处理俄罗斯方块图片信息失败: {str(e)}")
                        continue

                # 按Y坐标排序，使得输出更有序
                all_images.sort(key=lambda img: (img['y'], img['x']))

                if all_images:
                    log_content.append("## 图片详细信息")
                    log_content.append(f"{'序号':<5}{'名称':<25}{'分类':<5}{'位置(x,y)':<15}{'尺寸(宽x高)':<15}{'厘米尺寸':<10}{'表格宽-高':<12}{'文件大小':<10}{'旋转':<5}{'空隙填充':<10}{'利用率':<10}")
                    log_content.append("-" * 140)  # 增加分隔线长度以适应文件大小列

                    # 计算画布宽度和高度（像素）
                    canvas_width_px = int((canvas_width_m * 100 + horizontal_expansion_cm) * ppi / 2.54)
                    canvas_height_px = layout_info.get('max_height', 0)

                    # 确保画布高度不为0
                    if canvas_height_px <= 0:
                        # 尝试从图片信息中计算最大高度
                        max_y = 0
                        for img in all_images:
                            y = img.get('y', 0)
                            height = img.get('height', 0)
                            max_y = max(max_y, y + height)
                        canvas_height_px = max(max_y, 1)

                    for i, img in enumerate(all_images):
                        name = img.get('name', '未命名')
                        # 移除分类信息，统一处理
                        x = img.get('x', 0)
                        y = img.get('y', 0)
                        width = img.get('width', 0)
                        height = img.get('height', 0)
                        # 获取厘米尺寸，优先使用width_cm和height_cm属性
                        width_cm = img.get('width_cm', 0)
                        height_cm = img.get('height_cm', 0)
                        # 添加厘米尺寸列，格式为"宽-高"，使用格式化函数处理小数点
                        cm_size = f"{format_cm_value(width_cm)}-{format_cm_value(height_cm)}"
                        # 添加旋转信息 - 从图片详情中获取准确的旋转状态
                        need_rotation = img.get('need_rotation', False)

                        # 旋转状态已在统一处理中确定，无需特殊处理

                        rotated = '是' if need_rotation else '否'
                        is_gap_fill = '是' if img.get('is_gap_fill', False) else '否'

                        # 计算图片对总体利用率的贡献
                        # 计算图片面积
                        img_area = width * height

                        # 计算图片间距面积 - 优化计算
                        spacing_cm = image_spacing_cm
                        img_spacing_area = 0
                        if spacing_cm > 0:
                            # 优化计算：在图片的宽度和高度上各增加间距
                            spacing_px = int(spacing_cm * ppi / 2.54)
                            width_with_spacing = width + spacing_px
                            height_with_spacing = height + spacing_px
                            img_spacing_area = width_with_spacing * height_with_spacing - width * height

                        # 计算该图片的水平拓展面积占比
                        img_expansion_area = 0
                        if horizontal_expansion_cm > 0:
                            # 按图片高度占总高度的比例分配水平拓展面积
                            img_expansion_area = horizontal_expansion_cm * ppi / 2.54 * height

                        # 计算该图片的总占用面积
                        img_total_area = img_area + img_spacing_area + img_expansion_area

                        # 计算画布面积
                        canvas_area = canvas_width_px * canvas_height_px

                        # 计算图片对总体利用率的贡献百分比
                        # 使用图片的总占用面积（图片面积+间距面积+水平拓展面积），确保所有图片利用率之和等于总体利用率
                        img_utilization = (img_total_area / canvas_area * 100) if canvas_area > 0 else 0

                        # 记录调试信息，帮助验证计算
                        self.log_signal.emit(f"图片 {i+1} ({name}): 面积={img_area}, 间距={img_spacing_area:.0f}, 拓展={img_expansion_area:.0f}, 总占用={img_total_area:.0f}, 利用率={img_utilization:.2f}%")

                        # 获取表格里的宽、高数据
                        table_width_cm = img.get('table_width_cm', width_cm)
                        table_height_cm = img.get('table_height_cm', height_cm)
                        # 添加表格宽-高列，格式为"宽-高"，使用格式化函数处理小数点
                        table_cm_size = f"{format_cm_value(table_width_cm)}-{format_cm_value(table_height_cm)}"

                        # 获取文件大小信息
                        file_size_mb = img.get('file_size_mb', '0M')
                        if not file_size_mb or file_size_mb == '0M':
                            # 尝试从路径获取文件大小
                            img_path = img.get('path', '')
                            if img_path and os.path.exists(img_path):
                                try:
                                    file_size = os.path.getsize(img_path)
                                    from utils.image_helper import format_file_size_mb
                                    file_size_mb = format_file_size_mb(file_size)
                                except:
                                    file_size_mb = '0M'

                        # 添加图片利用率列，包含文件大小信息
                        log_content.append(f"{i+1:<5}{name[:23]:<25}{'UNIFIED':<5}({x},{y}){'':>5}({width}x{height}){'':>3}{cm_size:<10}{table_cm_size:<12}{file_size_mb:<10}{rotated:<5}{is_gap_fill:<10}{img_utilization:.2f}%")
                    log_content.append("")

                    # 添加分类统计信息
                    # 统一处理，不再区分A/B/C类别
                    total_count = len(all_images)
                    gap_fill_count = sum(1 for img in all_images if img.get('is_gap_fill', False))

                    # 更新layout_info中的stats
                    if 'stats' not in layout_info:
                        layout_info['stats'] = {}
                    layout_info['stats']['total_items'] = total_count

                    log_content.append("## 图片统计信息")
                    log_content.append(f"统一处理图片: {total_count}个")
                    log_content.append(f"行空隙填充图片: {gap_fill_count}个")
                    log_content.append("")
                else:
                    log_content.append("## 图片详细信息")
                    log_content.append("未找到图片信息")
                    log_content.append("")
            except Exception as e:
                self.log_signal.emit(f"处理图片信息时出错: {str(e)}")
                log_content.append("## 图片详细信息")
                log_content.append(f"处理图片信息时出错: {str(e)}")
                log_content.append("")

            # 写入文件
            with open(txt_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(log_content))

            self.log_signal.emit(f"成功创建日志文件: {txt_path}")
            self.finished_signal.emit(True, f"成功创建日志文件: {txt_path}")

        except Exception as e:
            error_msg = f"创建日志文件失败: {str(e)}"
            self.log_signal.emit(error_msg)
            self.finished_signal.emit(False, error_msg)


def create_log_file_async(tiff_path: str, layout_data: Dict[str, Any], log_callback=None) -> Optional[LogFileCreator]:
    """为TIFF文件异步创建同名的TXT日志文件

    该函数创建一个新的线程来异步创建TXT日志文件，不会阻塞调用线程

    Args:
        tiff_path: TIFF文件路径
        layout_data: 布局数据字典，包含创建日志文件所需的所有信息
        log_callback: 日志回调函数，用于接收日志信息

    Returns:
        LogFileCreator实例，如果创建失败则返回None
    """
    try:
        # 检查参数
        if not tiff_path or not isinstance(tiff_path, str):
            if log_callback:
                log_callback(f"无效的TIFF文件路径: {tiff_path}")
            return None

        if not layout_data or not isinstance(layout_data, dict):
            if log_callback:
                log_callback(f"无效的布局数据: {type(layout_data)}")
            # 创建一个基本的布局数据字典
            layout_data = {
                'canvas_name': os.path.basename(os.path.splitext(tiff_path)[0]),
                'material_name': '',
                'canvas_sequence': 0,
                'canvas_width_m': 0,
                'ppi': 72,
                'image_spacing_cm': 0,
                'horizontal_expansion_cm': 0,
                'layout_info': {},
                'start_time': get_timestamp(),
                'images_arranged': 0,
                'images_info': [],
                'skyline_images': []
            }
            if log_callback:
                log_callback("已创建基本布局数据字典")

        # 创建并启动工作线程
        log_creator = LogFileCreator(tiff_path, layout_data)

        # 如果提供了日志回调函数，连接信号
        if log_callback:
            log_creator.log_signal.connect(log_callback)
            # 使用lambda函数连接信号，只传递消息
            log_creator.finished_signal.connect(lambda _, msg: log_callback(msg))  # 使用_表示忽略的参数

        # 启动线程
        log_creator.start()

        # 返回日志创建器实例，以便调用者可以连接其信号
        return log_creator

    except Exception as e:
        if log_callback:
            log_callback(f"创建日志文件失败: {str(e)}")

        # 尝试直接创建一个基本的TXT文件，确保至少有一些信息
        try:
            txt_path = os.path.splitext(tiff_path)[0] + ".txt"
            material_name = layout_data.get('material_name', os.path.basename(os.path.dirname(tiff_path)))
            canvas_sequence = layout_data.get('canvas_sequence', 1)

            with open(txt_path, 'w', encoding='utf-8') as f:
                f.write(f"# {material_name}-{canvas_sequence} 正式环境说明文档\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"材质名称: {material_name}\n")
                f.write(f"画布序号: {canvas_sequence}\n")
                f.write(f"TIFF文件: {os.path.basename(tiff_path)}\n")

                # 尝试从布局数据中获取画布宽度和水平拓展信息
                canvas_width_m = layout_data.get('canvas_width_m', 0)
                horizontal_expansion_cm = layout_data.get('horizontal_expansion_cm', 0)
                f.write(f"画布宽度: {canvas_width_m:.2f}米 ({int(canvas_width_m*100)}厘米)\n")
                f.write(f"水平拓展: {horizontal_expansion_cm} 厘米\n")

                # 尝试获取利用率信息
                layout_info = layout_data.get('layout_info', {})
                utilization = layout_info.get('utilization', 0) * 100

                # 保留两位小数，统一百分比格式
                utilization = round(utilization * 100) / 100

                # 获取图片分类统计信息（仅用于日志显示）
                # 如果需要在应急日志中添加分类统计信息，可以使用layout_info.get('stats', {})

                # 添加图片信息（如果有）
                images = layout_info.get('images', [])
                if images:
                    f.write(f"\n## 图片详细信息（简化版）\n")
                    f.write(f"{'序号':<5}{'分类':<5}{'尺寸(宽x高)':<15}{'厘米尺寸':<10}{'表格宽-高':<12}{'旋转':<5}{'利用率':<10}\n")
                    f.write("-" * 70 + "\n")

                    # 计算画布宽度和高度（像素）
                    ppi = layout_data.get('ppi', 72)
                    canvas_width_m = layout_data.get('canvas_width_m', 0)
                    horizontal_expansion_cm = layout_data.get('horizontal_expansion_cm', 0)
                    canvas_width_px = int((canvas_width_m * 100 + horizontal_expansion_cm) * ppi / 2.54)

                    # 尝试获取画布高度
                    canvas_height_px = layout_info.get('max_height', 0)
                    if canvas_height_px <= 0:
                        # 尝试从图片信息中计算最大高度
                        max_y = 0
                        for img in images:
                            y = img.get('y', 0)
                            height = img.get('height', 0)
                            max_y = max(max_y, y + height)
                        canvas_height_px = max(max_y, 1)

                    for i, img in enumerate(images):
                        width = img.get('width', 0)
                        height = img.get('height', 0)
                        # 移除分类信息，统一处理
                        # 获取厘米尺寸，优先使用width_cm和height_cm属性
                        width_cm = img.get('width_cm', img.get('original_width_cm', 0))
                        height_cm = img.get('height_cm', img.get('original_height_cm', 0))
                        # 添加厘米尺寸列，格式为"宽-高"，使用格式化函数处理小数点
                        cm_size = f"{format_cm_value(width_cm)}-{format_cm_value(height_cm)}"
                        # 添加旋转信息 - 从图片详情中获取准确的旋转状态
                        need_rotation = img.get('need_rotation', False)

                        # 旋转状态已在统一处理中确定，无需特殊处理

                        rotated = '是' if need_rotation else '否'

                        # 计算图片对总体利用率的贡献百分比
                        img_area = width * height
                        canvas_area = canvas_width_px * canvas_height_px

                        # 计算图片间距面积 - 优化计算
                        # 获取图片间距设置，默认为0
                        image_spacing_cm = layout_data.get('image_spacing_cm', 0)
                        img_spacing_area = 0
                        if image_spacing_cm > 0:
                            # 优化计算：在图片的宽度和高度上各增加间距
                            spacing_px = int(image_spacing_cm * ppi / 2.54)
                            width_with_spacing = width + spacing_px
                            height_with_spacing = height + spacing_px
                            img_spacing_area = width_with_spacing * height_with_spacing - width * height

                        # 计算该图片的水平拓展面积占比
                        img_expansion_area = 0
                        if horizontal_expansion_cm > 0:
                            # 按图片高度占总高度的比例分配水平拓展面积
                            img_expansion_area = horizontal_expansion_cm * ppi / 2.54 * height

                        # 计算该图片的总占用面积
                        img_total_area = img_area + img_spacing_area + img_expansion_area

                        # 使用与主要计算相同的方法 - 使用总占用面积
                        img_utilization = (img_total_area / canvas_area * 100) if canvas_area > 0 else 0

                        # 获取表格里的宽、长数据
                        table_width_cm = img.get('table_width_cm', width_cm)
                        table_height_cm = img.get('table_height_cm', height_cm)
                        # 添加表格宽-高列，格式为"宽-高"，使用格式化函数处理小数点
                        table_cm_size = f"{format_cm_value(table_width_cm)}-{format_cm_value(table_height_cm)}"

                        f.write(f"{i+1:<5}{'UNIFIED':<5}({width}x{height}){'':>3}{cm_size:<10}{table_cm_size:<12}{rotated:<5}{img_utilization:.2f}%\n")
                    f.write("\n")

                # 添加利用率信息
                f.write(f"画布利用率: {utilization:.2f}% (图片面积+间距面积+水平拓展面积/画布面积)\n")

                # 添加旋转率信息（如果有）
                rotated_images = layout_info.get('stats', {}).get('rotated_items', 0)
                total_images = layout_info.get('stats', {}).get('total_items', 0)
                if total_images > 0:
                    rotated_percent = (rotated_images / total_images) * 100
                    f.write(f"旋转图片: {rotated_images}/{total_images} ({rotated_percent:.2f}%)\n")
                    f.write(f"旋转率: {rotated_percent:.2f}%\n")

                # 不再显示行利用率信息

                f.write(f"\n注意: 此为应急创建的简化日志文件，因原始日志创建过程出错。\n")
                f.write(f"错误信息: {str(e)}\n")
            if log_callback:
                log_callback(f"已创建应急简化日志文件: {txt_path}")
        except Exception as e2:
            if log_callback:
                log_callback(f"创建应急日志文件也失败: {str(e2)}")
        return None
