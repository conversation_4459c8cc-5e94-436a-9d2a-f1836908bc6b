import os
import sys
import json
import logging
import shutil
import tempfile
import gc
import threading
import time
import re
from concurrent.futures import ThreadPoolExecutor
from typing import List, Dict, Tuple, Optional
from PyQt6.QtWidgets import (
    QApplication, QWidget, QPushButton, QVBoxLayout,
    QFileDialog, QMessageBox, QLabel, QMainWindow,
    QProgressBar, QTextEdit, QGroupBox, QHBoxLayout,
    QLineEdit, QCheckBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QUrl
from PyQt6.QtGui import QTextCursor, QDragEnterEvent, QDropEvent

# 导入主项目的登录模块
from ui.login_dialog import LoginDialog
from utils.supabase_helper import SupabaseHelper
from PIL import Image  # 用于图片处理
import warnings

# 导入自定义模块
try:
    from core.image_indexer_duckdb import ImageIndexerDuckDB
    HAS_IMAGE_INDEXER = True
except ImportError:
    HAS_IMAGE_INDEXER = False
    logging.warning("图库索引器模块未找到，索引功能将被禁用")

# 尝试导入psutil，如果没有则使用替代方案
try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False

# 使用统一的PIL配置管理
try:
    from utils.pil_config import PILConfigManager
    # 为图片工具配置PIL（使用较大的限制以处理大图片）
    PILConfigManager.configure_pil(
        max_image_pixels=1_500_000_000,  # 1.5G像素
        load_truncated_images=True,
        ignore_decompression_warning=True,
        force_reconfigure=True  # 强制重新配置以确保使用正确的参数
    )
except ImportError:
    # 如果统一配置模块不可用，使用原来的配置方式
    Image.MAX_IMAGE_PIXELS = 1_500_000_000
    warnings.filterwarnings("ignore", category=Image.DecompressionBombWarning)

# 设置日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()  # 输出到控制台
    ]
)
log = logging.getLogger(__name__)

# 使用主项目的Supabase配置
# 通过SupabaseHelper统一管理

# 应用名称 & 当前应用版本
ROBOT_SMART_NAME = "DeAI-图库管理工具"
ROBOT_CURRENT_VERSION = "0.0.6"

IMAGE_FINDER_BOT_TAG = 'deai_robot_smart'

# 宽高比比较容差
ASPECT_RATIO_TOLERANCE = 0.01

# 支持的图片格式
SUPPORTED_IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.tif', '.webp'}

# 处理相关常量
DEFAULT_BATCH_SIZE = 100
LARGE_IMAGE_PIXEL_THRESHOLD = 50_000_000  # 5000万像素
MAX_FILENAME_COUNTER = 100  # 文件名计数器最大值
MEMORY_CHECK_INTERVAL = 1000  # 内存检查间隔（文件数）

# 处理延迟时间常量（秒）
PROCESSING_DELAY_SHORT = 0.1  # 短延迟
PROCESSING_DELAY_MEDIUM = 0.2  # 中等延迟
PROCESSING_DELAY_NORMAL = 0.3  # 正常延迟
PROCESSING_DELAY_LONG = 0.5   # 长延迟
PROCESSING_DELAY_BATCH = 1.0  # 批次间延迟

# 用户会话由SupabaseHelper统一管理

# 全局变量，用于存储主窗口实例的引用
main_window_instance = None

# 设置主窗口实例的引用
def set_main_window_instance(instance):
    global main_window_instance
    main_window_instance = instance

# 路径规范化辅助函数
def normalize_path(path: str, for_display: bool = False) -> str:
    """统一的路径规范化函数
    
    Args:
        path: 要规范化的路径
        for_display: True表示用于显示（使用正斜杠），False表示用于处理（使用系统默认）
    
    Returns:
        规范化后的路径
    """
    if not path:
        return path
    
    try:
        path = str(path).strip()
        
        if for_display:
            # 显示用：统一使用正斜杠
            path = path.replace('\\', '/')
            # 处理连续的斜杠
            while '//' in path:
                path = path.replace('//', '/')
            # 移除路径末尾的斜杠（除非是根路径）
            if path.endswith('/') and len(path) > 1:
                path = path.rstrip('/')
        else:
            # 处理用：使用os.path.normpath进行标准化
            path = os.path.normpath(path)
        
        return path
    except Exception:
        return str(path) if path else ""

# 兼容性函数
def normalize_path_for_display(path):
    """兼容性函数：规范化路径用于显示"""
    return normalize_path(path, for_display=True)

def normalize_path_for_processing(path):
    """兼容性函数：规范化路径用于文件处理"""
    return normalize_path(path, for_display=False)

# 网络请求日志记录函数
def log_network_request(message):
    """记录网络请求日志"""
    logging.info(f"网络请求: {message}")
    # 如果主窗口实例存在，也在UI中显示日志
    if main_window_instance:
        try:
            main_window_instance.update_log(message)
        except Exception as e:
            logging.error(f"更新UI日志时出错: {e}")

# 使用主项目的SupabaseHelper统一管理认证客户端

# 使用主项目的LoginDialog类


# ---------------------------
# 系统资源监控器
# ---------------------------
class SystemResourceMonitor:
    """系统资源监控器 - 增强版本"""

    @staticmethod
    def get_memory_usage():
        """获取当前内存使用情况"""
        if HAS_PSUTIL:
            try:
                memory = psutil.virtual_memory()
                return {
                    'total': memory.total,
                    'available': memory.available,
                    'percent': memory.percent,
                    'used': memory.used
                }
            except Exception:
                pass

        # 如果没有psutil或出错，使用简单的gc统计
        return {
            'objects': len(gc.get_objects()),
            'collections': gc.get_count()
        }

    @staticmethod
    def is_memory_pressure():
        """检查是否存在内存压力"""
        if HAS_PSUTIL:
            try:
                memory = psutil.virtual_memory()
                return memory.percent > 85  # 内存使用超过85%
            except Exception:
                pass
        return False

    @staticmethod
    def get_cpu_count():
        """获取CPU核心数"""
        try:
            return os.cpu_count() or 4
        except Exception:
            return 4


# ---------------------------
# 多线程任务管理器
# ---------------------------
class TaskManager:
    """多线程任务管理器"""

    def __init__(self, max_workers=None):
        self.max_workers = max_workers or min(32, (os.cpu_count() or 1) + 4)
        self.executor = None
        self.futures = []
        self._stop_event = threading.Event()

    def __enter__(self):
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.shutdown()

    def submit_task(self, func, *args, **kwargs):
        """提交任务"""
        if self.executor and not self._stop_event.is_set():
            future = self.executor.submit(func, *args, **kwargs)
            self.futures.append(future)
            return future
        return None

    def stop(self):
        """停止所有任务"""
        self._stop_event.set()

    def is_stopped(self):
        """检查是否已停止"""
        return self._stop_event.is_set()

    def shutdown(self, wait=True):
        """关闭执行器"""
        if self.executor:
            self.executor.shutdown(wait=wait)
            self.executor = None
        self.futures.clear()

    def get_completed_tasks(self):
        """获取已完成的任务"""
        completed = []
        remaining = []

        for future in self.futures:
            if future.done():
                completed.append(future)
            else:
                remaining.append(future)

        self.futures = remaining
        return completed

# ---------------------------
# 文件名清洗相关的类已移除，使用原始文件名进行处理
# ---------------------------

# ---------------------------
# 图片文件名优化工具
# ---------------------------
class FilenameOptimizer:
    """图片文件名优化工具 - 处理文件名中的空格等字符"""

    @staticmethod
    def optimize_filename(filename: str) -> str:
        """
        优化图片文件名：将空格替换为'-'

        Args:
            filename: 原始文件名（包含扩展名）

        Returns:
            str: 优化后的文件名
        """
        if not filename:
            return filename

        try:
            # 分离文件名和扩展名
            name, ext = os.path.splitext(filename)

            # 优化文件名：将空格替换为'-'
            optimized_name = name.strip()  # 先去除首尾空格

            # 将一个或多个连续空格替换为单个'-'
            optimized_name = re.sub(r'\s+', '-', optimized_name)

            # 去除可能出现的连续'-'
            optimized_name = re.sub(r'-+', '-', optimized_name)

            # 去除开头和结尾的'-'
            optimized_name = optimized_name.strip('-')

            # 如果优化后名字为空，使用原始名字
            if not optimized_name:
                optimized_name = name.strip()

            return optimized_name + ext

        except Exception as e:
            logging.warning(f"文件名优化失败 {filename}: {str(e)}")
            return filename

    @staticmethod
    def is_filename_optimized(filename: str) -> bool:
        """
        检查文件名是否已经优化过（不包含空格）

        Args:
            filename: 文件名

        Returns:
            bool: True表示已优化，False表示需要优化
        """
        if not filename:
            return True

        name, _ = os.path.splitext(filename)
        return ' ' not in name

    @staticmethod
    def get_optimization_preview(filename: str) -> dict:
        """
        获取文件名优化预览

        Args:
            filename: 原始文件名

        Returns:
            dict: {
                'original': str,      # 原始文件名
                'optimized': str,     # 优化后文件名
                'needs_optimization': bool,  # 是否需要优化
                'changes': list       # 变更列表
            }
        """
        original = filename
        optimized = FilenameOptimizer.optimize_filename(filename)
        needs_optimization = original != optimized

        changes = []
        if needs_optimization:
            changes.append(f"空格替换为'-': {original} → {optimized}")

        return {
            'original': original,
            'optimized': optimized,
            'needs_optimization': needs_optimization,
            'changes': changes
        }


# ---------------------------
# 中文名字提取和分类工具
# ---------------------------
class ChineseNameExtractor:
    """中文名字提取和分类工具"""

    @staticmethod
    def extract_chinese_name(filename: str) -> str:
        """
        从文件名中提取中文名字
        优化：解析'-'分隔的第一个元素作为文件夹名字
        例如：'维也纳（带暗纹）-140-200-横版.jpg' → '维也纳（带暗纹）'

        Args:
            filename: 文件名（包含扩展名）

        Returns:
            str: 提取的中文名字，如果没有中文则返回空字符串
        """
        # 移除扩展名
        name_without_ext = os.path.splitext(filename)[0]

        # 按'-'分割，取第一个部分
        first_part = name_without_ext.split('-')[0].strip()

        # 从第一个部分提取中文字符
        chinese_match = re.search(r'[\u4e00-\u9fff（）()]+', first_part)

        if chinese_match:
            return chinese_match.group()
        else:
            # 如果第一个部分没有中文，回退到原来的逻辑（提取整个文件名中的中文字符）
            chinese_match = re.search(r'[\u4e00-\u9fff（）()]+', name_without_ext)
            return chinese_match.group() if chinese_match else ""

    @staticmethod
    def find_matching_folder(chinese_name: str, root_path: str) -> str:
        """
        在主目录中查找匹配的中文文件夹

        Args:
            chinese_name: 中文名字
            root_path: 主目录路径

        Returns:
            str: 匹配的文件夹路径，如果没有找到则返回空字符串
        """
        if not chinese_name or not os.path.exists(root_path):
            return ""

        try:
            # 遍历主目录中的所有文件夹
            for item in os.listdir(root_path):
                item_path = os.path.join(root_path, item)
                if os.path.isdir(item_path):
                    # 检查文件夹名是否包含中文名字
                    if chinese_name in item:
                        return item_path

            return ""
        except Exception as e:
            logging.warning(f"查找匹配文件夹时出错: {str(e)}")
            return ""

    @staticmethod
    def group_images_by_chinese_name(image_files: List) -> Dict[str, List]:
        """
        按中文名字对图片文件进行分组

        Args:
            image_files: 图片文件列表 [(path, filename), ...]

        Returns:
            dict: {chinese_name: [file_info_list], 'no_chinese': [file_info_list]}
        """
        groups = {}
        no_chinese_files = []

        for file_info in image_files:
            # 简化文件名提取逻辑
            filename = (
                file_info[1] if isinstance(file_info, tuple) and len(file_info) >= 2
                else os.path.basename(str(file_info))
            )

            chinese_name = ChineseNameExtractor.extract_chinese_name(filename)

            if chinese_name:
                groups.setdefault(chinese_name, []).append(file_info)
            else:
                no_chinese_files.append(file_info)

        if no_chinese_files:
            groups['no_chinese'] = no_chinese_files

        return groups

    @staticmethod
    def create_folder_if_needed(folder_path: str, log_callback=None) -> bool:
        """
        如果需要，创建文件夹

        Args:
            folder_path: 文件夹路径
            log_callback: 日志回调函数

        Returns:
            bool: 创建成功或已存在返回True，失败返回False
        """
        try:
            if not os.path.exists(folder_path):
                os.makedirs(folder_path, exist_ok=True)
                if log_callback:
                    folder_name = os.path.basename(folder_path)
                    log_callback(f"📁 创建文件夹: {folder_name}")
                return True
            return True
        except Exception as e:
            if log_callback:
                folder_name = os.path.basename(folder_path)
                log_callback(f"❌ 创建文件夹失败: {folder_name} -> {str(e)}")
            return False









# ---------------------------
# 清洗移动处理器
# ---------------------------
class CleanAndMoveProcessor:
    """清洗移动处理器 - 优化图片名字并按中文名字分类移动"""

    def __init__(self, max_workers=None):
        self._stop_requested = False
        self._lock = threading.Lock()
        self._max_workers = max_workers or min(4, SystemResourceMonitor.get_cpu_count())

    def request_stop(self):
        """请求停止处理"""
        with self._lock:
            self._stop_requested = True

    def is_stop_requested(self):
        """检查是否请求停止"""
        with self._lock:
            return self._stop_requested

    def process_library_clean_and_move(self, library_path, progress_callback=None, log_callback=None):
        """
        处理图库清洗移动：优化文件名 + 按中文名字分类

        Args:
            library_path: 图库路径
            progress_callback: 进度回调函数
            log_callback: 日志回调函数

        Returns:
            dict: 处理结果统计
        """
        if not os.path.exists(library_path):
            raise ValueError(f"图库路径不存在: {library_path}")

        # 重置停止标志
        self._stop_requested = False

        if log_callback:
            log_callback("🚀 开始图库清洗移动处理")
            log_callback(f"📁 图库路径: {library_path}")

        start_time = time.time()

        try:
            # 第一阶段：扫描所有图片文件
            if log_callback:
                log_callback("=" * 50)
                log_callback("📊 第一阶段：扫描图片文件")
                log_callback("=" * 50)

            image_files = self._scan_library_images(library_path, log_callback)

            if not image_files:
                if log_callback:
                    log_callback("✨ 图库中没有找到需要处理的图片文件")
                return {'processed': 0, 'optimized': 0, 'moved': 0, 'errors': 0}

            # 第二阶段：分析和处理文件
            if log_callback:
                log_callback("=" * 50)
                log_callback("📊 第二阶段：分析和处理文件")
                log_callback("=" * 50)

            results = self._process_files_clean_and_move(
                image_files, library_path, progress_callback, log_callback
            )

            # 输出最终结果
            total_time = time.time() - start_time
            if log_callback:
                log_callback("=" * 50)
                log_callback("🎉 图库清洗移动完成！")
                log_callback("=" * 50)
                log_callback(f"📊 处理结果统计：")
                log_callback(f"   扫描文件数: {len(image_files)}")
                log_callback(f"   处理文件数: {results['processed']}")
                log_callback(f"   优化文件名: {results['optimized']}")
                log_callback(f"   移动文件数: {results['moved']}")
                log_callback(f"   错误文件数: {results['errors']}")
                log_callback(f"   总耗时: {total_time:.2f} 秒")
                log_callback("=" * 50)

            return results

        except Exception as e:
            if log_callback:
                log_callback(f"❌ 清洗移动处理出错: {str(e)}")
            raise

    def _scan_library_images(self, library_path, log_callback=None):
        """扫描图库中的所有图片文件"""
        image_files = []
        supported_extensions = SUPPORTED_IMAGE_EXTENSIONS

        if log_callback:
            log_callback("🔍 开始扫描图库中的图片文件...")

        try:
            for root, dirs, files in os.walk(library_path):
                if self.is_stop_requested():
                    break

                for filename in files:
                    if self.is_stop_requested():
                        break

                    # 检查是否为图片文件
                    if os.path.splitext(filename.lower())[1] in supported_extensions:
                        file_path = os.path.join(root, filename)
                        relative_path = os.path.relpath(root, library_path)

                        image_files.append({
                            'full_path': file_path,
                            'filename': filename,
                            'relative_dir': relative_path if relative_path != '.' else '',
                            'needs_optimization': not FilenameOptimizer.is_filename_optimized(filename)
                        })

                        # 每扫描500个文件报告一次进度
                        if len(image_files) % 500 == 0 and log_callback:
                            log_callback(f"📁 已扫描到 {len(image_files)} 个图片文件...")

        except Exception as e:
            if log_callback:
                log_callback(f"❌ 扫描过程中出错: {str(e)}")
            raise

        if log_callback:
            needs_optimization = sum(1 for f in image_files if f['needs_optimization'])
            log_callback(f"✅ 扫描完成！找到 {len(image_files)} 个图片文件")
            log_callback(f"📝 其中 {needs_optimization} 个文件需要优化文件名")

        return image_files

    def _analyze_chinese_name_statistics(self, image_files, log_callback=None):
        """分析中文名字统计，确定哪些需要创建文件夹"""
        chinese_name_counts = {}

        if log_callback:
            log_callback("📊 分析中文名字统计...")

        for file_info in image_files:
            filename = file_info['filename']
            optimized_filename = FilenameOptimizer.optimize_filename(filename)
            chinese_name = ChineseNameExtractor.extract_chinese_name(optimized_filename)

            if chinese_name:
                chinese_name_counts[chinese_name] = chinese_name_counts.get(chinese_name, 0) + 1

        # 确定需要创建文件夹的中文名字（出现次数>=2）
        folder_chinese_names = {name for name, count in chinese_name_counts.items() if count >= 2}

        if log_callback:
            total_chinese = len(chinese_name_counts)
            folder_count = len(folder_chinese_names)
            single_count = total_chinese - folder_count

            log_callback(f"📈 统计结果: 发现 {total_chinese} 个不同的中文名字")
            log_callback(f"📁 需要创建文件夹: {folder_count} 个 (出现次数>=2)")
            log_callback(f"📄 保留在主目录: {single_count} 个 (出现次数=1)")

            if folder_chinese_names:
                log_callback("📂 将创建以下文件夹:")
                for name in sorted(folder_chinese_names):
                    count = chinese_name_counts[name]
                    log_callback(f"   └── {name}/ ({count} 个文件)")

        return folder_chinese_names

    def _process_files_clean_and_move(self, image_files, library_path, progress_callback=None, log_callback=None):
        """处理文件清洗和移动"""
        results = {'processed': 0, 'optimized': 0, 'moved': 0, 'errors': 0}
        total_files = len(image_files)

        # 第一步：分析中文名字统计
        folder_chinese_names = self._analyze_chinese_name_statistics(image_files, log_callback)

        if log_callback:
            log_callback("🔄 开始处理文件...")

        for i, file_info in enumerate(image_files):
            if self.is_stop_requested():
                break

            try:
                success = self._process_single_file(file_info, library_path, folder_chinese_names, log_callback)

                if success['processed']:
                    results['processed'] += 1
                if success['optimized']:
                    results['optimized'] += 1
                if success['moved']:
                    results['moved'] += 1
                if success['error']:
                    results['errors'] += 1

                # 更新进度
                if progress_callback:
                    progress = int((i + 1) / total_files * 100)
                    progress_callback(progress)

                # 每处理100个文件报告一次进度
                if (i + 1) % 100 == 0 and log_callback:
                    log_callback(f"📊 已处理 {i + 1}/{total_files} 个文件...")

            except Exception as e:
                results['errors'] += 1
                if log_callback:
                    log_callback(f"❌ 处理文件出错 {file_info['filename']}: {str(e)}")

        return results

    def _process_single_file(self, file_info, library_path, folder_chinese_names, log_callback=None):
        """处理单个文件"""
        result = {'processed': False, 'optimized': False, 'moved': False, 'error': False}

        try:
            original_path = file_info['full_path']
            original_filename = file_info['filename']

            # 优化文件名
            optimized_filename = FilenameOptimizer.optimize_filename(original_filename)
            filename_changed = original_filename != optimized_filename

            if filename_changed:
                result['optimized'] = True
                if log_callback:
                    log_callback(f"📝 优化文件名: {original_filename} → {optimized_filename}")

            # 确定目标路径（基于中文名字和统计结果）
            chinese_name = ChineseNameExtractor.extract_chinese_name(optimized_filename)
            target_dir = library_path

            if chinese_name and chinese_name in folder_chinese_names:
                # 只有当中文名字出现次数>=2时才创建/使用文件夹
                matching_folder = ChineseNameExtractor.find_matching_folder(chinese_name, library_path)
                if matching_folder:
                    target_dir = matching_folder
                    if log_callback:
                        log_callback(f"📂 使用现有文件夹: {chinese_name}/")
                else:
                    # 创建新的中文名字文件夹
                    target_dir = os.path.join(library_path, chinese_name)
                    if ChineseNameExtractor.create_folder_if_needed(target_dir, log_callback):
                        if log_callback:
                            log_callback(f"📁 创建中文分类文件夹: {chinese_name}/")
            else:
                # 中文名字唯一或无中文名字，保留在主目录
                if chinese_name and log_callback:
                    log_callback(f"📄 保留在主目录: {optimized_filename} (中文名'{chinese_name}'唯一)")
                elif log_callback:
                    log_callback(f"📄 保留在主目录: {optimized_filename} (无中文名)")

            target_path = os.path.join(target_dir, optimized_filename)

            # 检查是否需要移动文件
            needs_move = (original_path != target_path)

            if needs_move or filename_changed:
                # 执行文件移动/重命名
                success = self._safe_move_file(original_path, target_path, log_callback)
                if success:
                    result['moved'] = True
                    if log_callback:
                        if needs_move:
                            relative_target = os.path.relpath(target_path, library_path)
                            log_callback(f"✅ 移动文件: {original_filename} → {relative_target}")
                        else:
                            log_callback(f"✅ 重命名文件: {original_filename} → {optimized_filename}")
                else:
                    result['error'] = True
            else:
                if log_callback:
                    log_callback(f"⏭️ 跳过: {original_filename} (无需处理)")

            result['processed'] = True

        except Exception as e:
            result['error'] = True
            if log_callback:
                log_callback(f"❌ 处理文件失败 {file_info['filename']}: {str(e)}")

        return result

    def _safe_move_file(self, source_path, target_path, log_callback=None, max_retries=3):
        """安全的文件移动"""
        for retry_count in range(max_retries):
            try:
                # 确保目标目录存在
                target_dir = os.path.dirname(target_path)
                if not os.path.exists(target_dir):
                    os.makedirs(target_dir, exist_ok=True)

                # 如果目标文件已存在且与源文件相同，直接删除源文件
                if os.path.exists(target_path) and source_path != target_path:
                    if os.path.samefile(source_path, target_path):
                        return True  # 文件已经在正确位置
                    # 如果是不同文件但同名，覆盖
                    os.remove(target_path)

                # 移动文件
                shutil.move(source_path, target_path)
                return True

            except (OSError, PermissionError, shutil.Error) as e:
                if retry_count < max_retries - 1:
                    time.sleep(0.1)  # 短暂等待后重试
                else:
                    if log_callback:
                        log_callback(f"❌ 文件移动失败: {os.path.basename(source_path)} -> {str(e)}")
                    return False
        return False


# ---------------------------
# 超高性能多线程图片方向处理器
# ---------------------------
class UltraHighPerformanceImageOrientationProcessor:
    """超高性能多线程图片方向处理器"""

    def __init__(self, max_workers=None):
        self._stop_requested = False
        self._lock = threading.Lock()
        self._max_workers = max_workers or min(4, SystemResourceMonitor.get_cpu_count())  # 图片处理用较少线程
        self._task_manager = None

    def request_stop(self):
        """请求停止处理"""
        with self._lock:
            self._stop_requested = True
            if self._task_manager:
                self._task_manager.stop()

    def is_stop_requested(self):
        """检查是否请求停止"""
        with self._lock:
            return self._stop_requested

    @staticmethod
    def get_image_dimensions(image_path: str) -> Optional[Tuple[int, int]]:
        """获取图片的真实宽高（优化版本）"""
        try:
            with Image.open(image_path) as img:
                width, height = img.size
                total_pixels = width * height

                # 对于大图片进行警告和记录
                if total_pixels > LARGE_IMAGE_PIXEL_THRESHOLD:
                    size_mb = (total_pixels * 3) / (1024 * 1024)  # 估算RGB内存占用
                    logging.info(f"处理大图片: {os.path.basename(image_path)}, 尺寸: {width}x{height}, 像素: {total_pixels:,}, 估算内存: {size_mb:.1f}MB")

                # 处理EXIF旋转信息
                try:
                    exif_dict = img.getexif()
                    if exif_dict:
                        orientation = exif_dict.get(274)  # Orientation标签
                        # 根据EXIF方向调整尺寸
                        rotation_map = {3: 180, 6: 270, 8: 90}
                        if orientation in rotation_map:
                            img = img.rotate(rotation_map[orientation], expand=True)
                            width, height = img.size
                except (AttributeError, KeyError, TypeError, OSError):
                    pass  # 静默处理EXIF错误

                return (width, height)
        except Exception as e:
            logging.error(f"获取图片尺寸失败 {image_path}: {str(e)}")
            return None

    @staticmethod
    def is_portrait(image_path: str) -> bool:
        """判断图片是否为竖向（高 > 宽）"""
        dimensions = UltraHighPerformanceImageOrientationProcessor.get_image_dimensions(image_path)
        return dimensions and dimensions[1] > dimensions[0]

    @staticmethod
    def rotate_image_to_landscape(image_path: str, output_path: Optional[str] = None) -> bool:
        """将竖向图片旋转90度变为横向"""
        try:
            with Image.open(image_path) as img:
                width, height = img.size
                total_pixels = width * height
                
                # 记录大图片信息
                if total_pixels > LARGE_IMAGE_PIXEL_THRESHOLD:
                    size_mb = (total_pixels * 3) / (1024 * 1024)
                    logging.info(f"正在旋转大图片: {os.path.basename(image_path)}, 尺寸: {width}x{height}, 估算内存: {size_mb:.1f}MB")

                # 只处理竖向图片
                if height <= width:
                    return False  # 已经是横向，无需旋转
                
                logging.info(f"旋转竖向图片: {os.path.basename(image_path)} ({width}x{height} → {height}x{width})")
                
                # 顺时针旋转90度
                rotated_img = img.rotate(-90, expand=True)
                save_path = output_path or image_path
                
                # 根据图片大小调整保存质量
                quality_map = {
                    100_000_000: 75,  # 1亿像素以上
                    LARGE_IMAGE_PIXEL_THRESHOLD: 85,  # 大图片
                }
                
                quality = 95  # 默认质量
                for threshold, q in quality_map.items():
                    if total_pixels > threshold:
                        quality = q
                        break
                
                save_kwargs = {'quality': quality, 'optimize': True}
                if quality < 95:
                    save_kwargs['progressive'] = True
                    
                rotated_img.save(save_path, **save_kwargs)
                
                if quality == 75:
                    logging.info("超大图片保存为较低质量(75%)以节省空间")
                
                return True

        except Exception as e:
            logging.error(f"旋转图片失败 {image_path}: {str(e)}")
            return False
        finally:
            # 大图片处理后进行内存清理
            if 'total_pixels' in locals() and total_pixels > LARGE_IMAGE_PIXEL_THRESHOLD:
                gc.collect()
                logging.debug("大图片处理完成，已清理内存")

    def _process_image_batch(self, batch_info):
        """
        处理单个图片批次 - 多线程工作函数
        """
        batch_files, folder_path, batch_num = batch_info

        results = {
            'batch_num': batch_num,
            'processed': 0,
            'rotated': 0,
            'errors': 0,
            'logs': []
        }

        try:
            for filename in batch_files:
                if self.is_stop_requested():
                    break

                try:
                    image_path = os.path.join(folder_path, filename)

                    # 检查文件是否存在
                    if not os.path.exists(image_path):
                        results['logs'].append(f"⚠️ 跳过: {filename} (文件不存在)")
                        continue

                    # 检查是否为竖向图片
                    is_portrait = False
                    try:
                        is_portrait = self.is_portrait(image_path)
                    except Exception as check_error:
                        results['logs'].append(f"❌ 检查图片方向失败: {filename} -> {str(check_error)}")
                        results['errors'] += 1
                        continue

                    if is_portrait:
                        # 旋转图片
                        success = self.rotate_image_to_landscape(image_path)

                        if success:
                            results['rotated'] += 1
                            results['logs'].append(f"✅ 旋转: {filename} (竖向 -> 横向)")
                        else:
                            results['errors'] += 1
                            results['logs'].append(f"❌ 旋转失败: {filename}")
                    else:
                        results['logs'].append(f"⏭️ 跳过: {filename} (已是横向)")

                    results['processed'] += 1

                except Exception as e:
                    results['errors'] += 1
                    results['logs'].append(f"❌ 处理文件 {filename} 时出错: {str(e)}")

        except Exception as batch_error:
            results['logs'].append(f"❌ 批次 {batch_num} 处理出错: {str(batch_error)}")

        return results


# 兼容性类（简化版本）
class ImageOrientationProcessor(UltraHighPerformanceImageOrientationProcessor):
    """兼容性类，继承超高性能处理器，提供向后兼容性"""
    pass  # 所有方法都从父类继承，无需重复定义

    @staticmethod
    def process_folder(folder_path, progress_callback=None, log_callback=None):
        """
        处理文件夹中的所有图片，将竖向图片转为横向
        增强版本：支持分批处理、内存优化和错误恢复
        """
        if not os.path.exists(folder_path):
            raise ValueError(f"文件夹不存在: {folder_path}")

        # 支持的图片格式
        image_extensions = SUPPORTED_IMAGE_EXTENSIONS

        # 分批获取图片文件
        if log_callback:
            log_callback("正在扫描图片文件...")

        image_files = []
        try:
            for filename in os.listdir(folder_path):
                if os.path.splitext(filename.lower())[1] in image_extensions:
                    image_files.append(filename)

                # 每扫描一定数量文件进行一次内存清理
                if len(image_files) % DEFAULT_BATCH_SIZE == 0:
                    gc.collect()
                    if log_callback:
                        log_callback(f"已扫描到 {len(image_files)} 个图片文件...")
        except Exception as e:
            if log_callback:
                log_callback(f"扫描文件时出错: {str(e)}")
            raise

        if not image_files:
            if log_callback:
                log_callback("文件夹中没有找到图片文件")
            return 0

        total_files = len(image_files)
        processed_count = 0
        rotated_count = 0
        error_count = 0

        if log_callback:
            log_callback(f"找到 {total_files} 个图片文件，开始检查和旋转...")

        # 分批处理文件，每批20个文件（图片处理更消耗内存，批次更小）
        batch_size = 20
        total_batches = (total_files + batch_size - 1) // batch_size

        if log_callback:
            log_callback(f"将分 {total_batches} 批处理，每批最多 {batch_size} 个文件")

        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, total_files)
            batch_files = image_files[start_idx:end_idx]

            if log_callback:
                log_callback(f"正在处理第 {batch_num + 1}/{total_batches} 批 ({len(batch_files)} 个文件)...")

            for i, filename in enumerate(batch_files):
                try:
                    image_path = os.path.join(folder_path, filename)

                    # 检查文件是否仍然存在
                    if not os.path.exists(image_path):
                        if log_callback:
                            log_callback(f"跳过: {filename} (文件不存在)")
                        continue

                    # 检查是否为竖向图片
                    is_portrait = False
                    try:
                        is_portrait = ImageOrientationProcessor.is_portrait(image_path)
                    except Exception as check_error:
                        if log_callback:
                            log_callback(f"检查图片方向失败: {filename} -> {str(check_error)}")
                        error_count += 1
                        continue

                    if is_portrait:
                        # 旋转图片，添加重试机制
                        retry_count = 0
                        max_retries = 2
                        rotation_success = False

                        while retry_count < max_retries and not rotation_success:
                            try:
                                if ImageOrientationProcessor.rotate_image_to_landscape(image_path):
                                    rotated_count += 1
                                    rotation_success = True
                                    if log_callback:
                                        log_callback(f"旋转: {filename} (竖向 -> 横向)")
                                else:
                                    if log_callback:
                                        log_callback(f"旋转失败: {filename}")
                                    break
                            except Exception as rotation_error:
                                retry_count += 1
                                if retry_count < max_retries:
                                    if log_callback:
                                        log_callback(f"旋转失败，重试 {retry_count}/{max_retries}: {filename}")
                                    import time
                                    time.sleep(PROCESSING_DELAY_MEDIUM)  # 短暂等待后重试
                                else:
                                    if log_callback:
                                        log_callback(f"旋转失败(已重试{max_retries}次): {filename} -> {str(rotation_error)}")
                                    error_count += 1
                    else:
                        if log_callback:
                            log_callback(f"跳过: {filename} (已是横向)")

                    processed_count += 1

                    # 更新进度
                    if progress_callback:
                        overall_progress = int((start_idx + i + 1) / total_files * 100)
                        progress_callback(overall_progress)

                except Exception as e:
                    error_count += 1
                    if log_callback:
                        log_callback(f"处理文件 {filename} 时出错: {str(e)}")

            # 每批处理完成后进行内存清理
            gc.collect()
            if log_callback:
                log_callback(f"第 {batch_num + 1} 批处理完成，已清理内存")

        if log_callback:
            log_callback("=" * 50)
            log_callback("图片方向处理完成！处理结果统计：")
            log_callback(f"总文件数: {total_files}")
            log_callback(f"处理文件数: {processed_count}")
            log_callback(f"旋转文件数: {rotated_count}")
            log_callback(f"错误文件数: {error_count}")
            log_callback("=" * 50)

        return rotated_count


# ---------------------------
# 超高性能多线程图片移动器
# ---------------------------
class UltraHighPerformanceImageMover:
    """超高性能多线程图片移动器 - 将子文件夹中的图片移动到主目录"""

    def __init__(self, max_workers=None):
        self._stop_requested = False
        self._lock = threading.Lock()
        self._max_workers = max_workers or min(8, SystemResourceMonitor.get_cpu_count())
        self._task_manager = None

    def request_stop(self):
        """请求停止处理"""
        with self._lock:
            self._stop_requested = True
            if self._task_manager:
                self._task_manager.stop()

    def is_stop_requested(self):
        """检查是否请求停止"""
        with self._lock:
            return self._stop_requested

    @staticmethod
    def get_supported_image_extensions():
        """获取支持的图片格式"""
        return SUPPORTED_IMAGE_EXTENSIONS

    def scan_images_in_subfolders(self, root_path, progress_callback=None, log_callback=None):
        """
        扫描所有子文件夹中的图片文件
        返回: (image_files_list, subfolder_list)
        """
        if not os.path.exists(root_path):
            raise ValueError(f"根目录不存在: {root_path}")

        image_extensions = self.get_supported_image_extensions()
        image_files = []  # [(source_path, relative_path, filename), ...]
        subfolders = set()  # 记录所有子文件夹

        if log_callback:
            log_callback("🔍 开始扫描子文件夹中的图片文件...")

        scan_count = 0

        try:
            # 递归遍历所有子目录
            for current_root, dirs, files in os.walk(root_path):
                if self.is_stop_requested():
                    break

                # 跳过根目录本身
                if current_root == root_path:
                    continue

                # 记录子文件夹
                subfolders.add(current_root)

                # 扫描当前目录中的图片文件
                for filename in files:
                    if self.is_stop_requested():
                        break

                    # 检查是否为图片文件
                    if os.path.splitext(filename.lower())[1] in image_extensions:
                        source_path = os.path.join(current_root, filename)
                        relative_path = normalize_path_for_display(os.path.relpath(current_root, root_path))

                        image_files.append((source_path, relative_path, filename))
                        scan_count += 1

                        # 每扫描500个文件报告一次进度
                        if scan_count % 500 == 0 and log_callback:
                            log_callback(f"📁 已扫描到 {scan_count} 个图片文件...")

                        # 内存压力检查
                        if scan_count % MEMORY_CHECK_INTERVAL == 0 and SystemResourceMonitor.is_memory_pressure():
                            gc.collect()
                            if log_callback:
                                log_callback("🧹 检测到内存压力，已清理内存")

        except Exception as e:
            if log_callback:
                log_callback(f"❌ 扫描过程中出错: {str(e)}")
            raise

        if log_callback:
            log_callback(f"✅ 扫描完成！找到 {len(image_files)} 个图片文件在 {len(subfolders)} 个子文件夹中")

        return image_files, list(subfolders)

    def _move_file_batch(self, batch_info):
        """
        移动单个文件批次 - 多线程工作函数，支持基于中文名字的智能分类
        """
        batch_files, root_path, batch_num = batch_info

        results = {
            'batch_num': batch_num,
            'processed': 0,
            'moved': 0,
            'overwritten': 0,
            'errors': 0,
            'logs': []
        }

        try:
            for source_path, relative_path, filename in batch_files:
                if self.is_stop_requested():
                    break

                try:
                    # 检查源文件是否存在
                    if not os.path.exists(source_path):
                        results['logs'].append(f"⚠️ 跳过: {filename} (源文件不存在)")
                        continue

                    # 提取中文名字
                    chinese_name = ChineseNameExtractor.extract_chinese_name(filename)

                    # 确定目标路径
                    if chinese_name:
                        # 查找匹配的文件夹
                        matching_folder = ChineseNameExtractor.find_matching_folder(chinese_name, root_path)
                        if matching_folder:
                            # 移动到匹配的文件夹
                            target_path = os.path.join(matching_folder, filename)
                            target_display = f"{os.path.basename(matching_folder)}/{filename}"
                        else:
                            # 移动到主目录
                            target_path = os.path.join(root_path, filename)
                            target_display = filename
                    else:
                        # 没有中文名字，移动到主目录
                        target_path = os.path.join(root_path, filename)
                        target_display = filename

                    # 检查是否需要覆盖
                    will_overwrite = os.path.exists(target_path)

                    # 执行移动操作
                    success = self._safe_move_with_retry(source_path, target_path, filename)

                    if success:
                        results['moved'] += 1
                        if will_overwrite:
                            results['overwritten'] += 1
                            results['logs'].append(f"✅ 移动(覆盖): {relative_path}/{filename} → {target_display}")
                        else:
                            results['logs'].append(f"✅ 移动: {relative_path}/{filename} → {target_display}")
                    else:
                        results['errors'] += 1
                        results['logs'].append(f"❌ 移动失败: {relative_path}/{filename}")

                    results['processed'] += 1

                except Exception as e:
                    results['errors'] += 1
                    results['logs'].append(f"❌ 处理文件 {filename} 时出错: {str(e)}")

        except Exception as batch_error:
            results['logs'].append(f"❌ 批次 {batch_num} 处理出错: {str(batch_error)}")

        return results

    def _safe_move_with_retry(self, source_path, target_path, filename, max_retries=3):
        """安全的文件移动，带重试机制"""
        for retry_count in range(max_retries):
            try:
                # 如果目标文件存在，先删除（实现覆盖）
                if os.path.exists(target_path):
                    os.remove(target_path)

                # 移动文件
                shutil.move(source_path, target_path)
                return True
            except (OSError, PermissionError, shutil.Error) as e:
                if retry_count < max_retries - 1:
                    time.sleep(PROCESSING_DELAY_SHORT)  # 短暂等待后重试
                else:
                    logging.error(f"移动文件失败 {filename}: {str(e)}")
                    return False
        return False

    @staticmethod
    def _safe_copy_with_retry(source_path, target_path, filename, max_retries=3, log_callback=None):
        """安全的文件复制，带重试机制和覆盖验证"""
        for retry_count in range(max_retries):
            try:
                # 记录覆盖操作
                will_overwrite = os.path.exists(target_path)
                if will_overwrite and log_callback:
                    log_callback(f"🔄 检测到目标文件已存在，准备覆盖: {filename}")

                # 如果目标文件存在，先删除（确保覆盖）
                if os.path.exists(target_path):
                    try:
                        os.remove(target_path)
                        if log_callback:
                            log_callback(f"🗑️ 已删除原有文件: {filename}")
                    except Exception as delete_error:
                        if log_callback:
                            log_callback(f"⚠️ 删除原有文件失败: {filename} -> {str(delete_error)}")
                        raise delete_error

                # 复制文件
                shutil.copy2(source_path, target_path)

                # 验证复制是否成功
                if not os.path.exists(target_path):
                    raise FileNotFoundError(f"复制后目标文件不存在: {target_path}")

                # 验证文件大小是否一致
                source_size = os.path.getsize(source_path)
                target_size = os.path.getsize(target_path)
                if source_size != target_size:
                    raise ValueError(f"文件大小不匹配: 源文件 {source_size} 字节, 目标文件 {target_size} 字节")

                if log_callback:
                    if will_overwrite:
                        log_callback(f"✅ 文件覆盖成功: {filename} (大小: {target_size} 字节)")
                    else:
                        log_callback(f"✅ 文件复制成功: {filename} (大小: {target_size} 字节)")

                return True, will_overwrite

            except (OSError, PermissionError, shutil.Error, FileNotFoundError, ValueError) as e:
                if retry_count < max_retries - 1:
                    if log_callback:
                        log_callback(f"⚠️ 文件复制失败，重试 {retry_count + 1}/{max_retries}: {filename} -> {str(e)}")
                    time.sleep(PROCESSING_DELAY_SHORT)  # 短暂等待后重试
                else:
                    error_msg = f"文件复制失败 {filename}: {str(e)}"
                    logging.error(error_msg)
                    if log_callback:
                        log_callback(f"❌ {error_msg}")
                    return False, False
        return False, False

    def remove_empty_folders(self, root_path, subfolders, log_callback=None):
        """
        删除空文件夹
        """
        if log_callback:
            log_callback("🗑️ 开始清理空文件夹...")

        removed_count = 0

        # 按深度排序，从最深的文件夹开始删除
        sorted_folders = sorted(subfolders, key=lambda x: x.count(os.sep), reverse=True)

        for folder_path in sorted_folders:
            if self.is_stop_requested():
                break

            try:
                # 检查文件夹是否存在且为空
                if os.path.exists(folder_path) and not os.listdir(folder_path):
                    os.rmdir(folder_path)
                    removed_count += 1
                    relative_path = normalize_path_for_display(os.path.relpath(folder_path, root_path))
                    if log_callback:
                        log_callback(f"🗑️ 删除空文件夹: {relative_path}")
                elif os.path.exists(folder_path):
                    # 文件夹不为空，记录日志
                    relative_path = normalize_path_for_display(os.path.relpath(folder_path, root_path))
                    if log_callback:
                        log_callback(f"⚠️ 跳过非空文件夹: {relative_path}")

            except Exception as e:
                relative_path = normalize_path_for_display(os.path.relpath(folder_path, root_path))
                if log_callback:
                    log_callback(f"❌ 删除文件夹失败: {relative_path} -> {str(e)}")

        if log_callback:
            log_callback(f"✅ 清理完成！删除了 {removed_count} 个空文件夹")

        return removed_count

    def process_folder_move_images(self, root_path, progress_callback=None, log_callback=None):
        """
        超高性能多线程图片移动处理 - 主要处理方法
        将所有子文件夹中的图片移动到主目录，并删除空文件夹
        """
        if not os.path.exists(root_path):
            raise ValueError(f"根目录不存在: {root_path}")

        # 重置停止标志
        self._stop_requested = False

        if log_callback:
            log_callback("🚀 启动超高性能多线程图片移动模式")
            log_callback(f"💻 使用 {self._max_workers} 个工作线程")
            log_callback(f"📁 根目录: {root_path}")

        start_time = time.time()

        try:
            # 第一阶段：扫描所有子文件夹中的图片
            if log_callback:
                log_callback("=" * 60)
                log_callback("📊 第一阶段：扫描子文件夹中的图片文件")
                log_callback("=" * 60)

            image_files, subfolders = self.scan_images_in_subfolders(
                root_path, progress_callback, log_callback
            )

            if not image_files:
                if log_callback:
                    log_callback("✨ 子文件夹中没有找到图片文件，无需移动")
                return 0

            # 第二阶段：多线程并行移动图片
            if log_callback:
                log_callback("=" * 60)
                log_callback("📊 第二阶段：多线程并行移动图片文件")
                log_callback("=" * 60)

            moved_count = self._process_move_images_parallel(
                image_files, root_path, progress_callback, log_callback
            )

            # 第三阶段：清理空文件夹
            if log_callback:
                log_callback("=" * 60)
                log_callback("📊 第三阶段：清理空文件夹")
                log_callback("=" * 60)

            removed_folders = self.remove_empty_folders(root_path, subfolders, log_callback)

            # 输出最终结果
            total_time = time.time() - start_time
            if log_callback:
                log_callback("=" * 60)
                log_callback("🎉 超高性能多线程图片移动完成！")
                log_callback("=" * 60)
                log_callback(f"📊 处理结果统计：")
                log_callback(f"   扫描到的图片文件: {len(image_files)}")
                log_callback(f"   成功移动的文件: {moved_count}")
                log_callback(f"   删除的空文件夹: {removed_folders}")
                log_callback(f"   总耗时: {total_time:.2f} 秒")
                if total_time > 0:
                    log_callback(f"   平均速度: {len(image_files)/total_time:.1f} 文件/秒")
                log_callback(f"   使用线程数: {self._max_workers}")
                log_callback("=" * 60)

            # 确保最终进度为100%
            if progress_callback:
                progress_callback(100)

            return moved_count

        except Exception as e:
            if log_callback:
                log_callback(f"❌ 图片移动过程中出错: {str(e)}")
            raise

    def _process_move_images_parallel(self, image_files, root_path, progress_callback=None, log_callback=None):
        """
        多线程并行处理图片移动
        """
        total_files = len(image_files)

        # 动态调整批次大小
        if total_files <= 50:
            batch_size = 10
        elif total_files <= 500:
            batch_size = 25
        elif total_files <= 2000:
            batch_size = 50
        else:
            batch_size = DEFAULT_BATCH_SIZE

        # 创建批次
        batches = []
        for i in range(0, total_files, batch_size):
            batch_files = image_files[i:i + batch_size]
            batches.append((batch_files, root_path, i // batch_size + 1))

        total_batches = len(batches)

        if log_callback:
            log_callback(f"⚡ 启动多线程并行移动：{total_batches} 批，每批最多 {batch_size} 个文件")
            log_callback(f"🔧 使用 {self._max_workers} 个工作线程并行处理")

        # 处理统计
        total_processed = 0
        total_moved = 0
        total_overwritten = 0
        total_errors = 0

        processing_start_time = time.time()

        # 使用多线程处理
        try:
            with TaskManager(max_workers=self._max_workers) as task_manager:
                self._task_manager = task_manager

                # 提交所有批次任务
                for batch_info in batches:
                    if self.is_stop_requested():
                        break
                    task_manager.submit_task(self._move_file_batch, batch_info)

                # 收集结果
                completed_batches = 0
                while completed_batches < total_batches and not self.is_stop_requested():
                    # 获取已完成的任务
                    completed_tasks = task_manager.get_completed_tasks()

                    for future in completed_tasks:
                        try:
                            result = future.result()
                            completed_batches += 1

                            # 累计统计
                            total_processed += result['processed']
                            total_moved += result['moved']
                            total_overwritten += result['overwritten']
                            total_errors += result['errors']

                            # 输出批次日志
                            if log_callback:
                                batch_num = result['batch_num']
                                log_callback(f"✅ 第 {batch_num} 批完成：处理 {result['processed']} 个，移动 {result['moved']} 个，覆盖 {result['overwritten']} 个")

                                # 输出详细日志（限制数量）
                                for log_msg in result['logs'][:3]:  # 只显示前3条
                                    log_callback(f"   {log_msg}")

                                if len(result['logs']) > 3:
                                    log_callback(f"   ... 还有 {len(result['logs']) - 3} 条日志")

                            # 更新进度
                            if progress_callback:
                                progress = min(90, int(completed_batches / total_batches * 90))  # 移动阶段占90%
                                progress_callback(progress)

                            # 每10批报告一次总进度
                            if completed_batches % 10 == 0 or completed_batches == total_batches:
                                if log_callback:
                                    progress_percent = completed_batches / total_batches * 100
                                    log_callback(f"📊 移动进度: {progress_percent:.1f}% ({completed_batches}/{total_batches} 批)")

                        except Exception as task_error:
                            if log_callback:
                                log_callback(f"❌ 任务执行出错: {str(task_error)}")
                            total_errors += 1

                    # 短暂休息，避免CPU占用过高
                    if completed_batches < total_batches:
                        time.sleep(PROCESSING_DELAY_SHORT)

                    # 定期内存清理
                    if completed_batches % 20 == 0:
                        gc.collect()

        except Exception as processing_error:
            if log_callback:
                log_callback(f"❌ 多线程移动处理出错: {str(processing_error)}")
            raise
        finally:
            self._task_manager = None

        processing_time = time.time() - processing_start_time

        if log_callback:
            log_callback("=" * 50)
            log_callback("📊 移动阶段统计：")
            log_callback(f"   处理文件数: {total_processed}")
            log_callback(f"   成功移动数: {total_moved}")
            log_callback(f"   覆盖文件数: {total_overwritten}")
            log_callback(f"   错误文件数: {total_errors}")
            log_callback(f"   移动耗时: {processing_time:.2f} 秒")
            log_callback("=" * 50)

        return total_moved


# ---------------------------
# 拖拽删除处理模块
# ---------------------------
class DragDropDeleteProcessor:
    """
    拖拽删除处理器
    负责处理拖拽删除操作，包括路径验证、文件删除和索引更新
    """

    @staticmethod
    def validate_paths_in_library(paths: List[str], library_path: str) -> dict:
        """
        验证所有拖拽路径都在图库内

        Args:
            paths: 拖拽的文件/文件夹路径列表
            library_path: 图库路径

        Returns:
            dict: {
                'valid': bool,           # 是否所有路径都有效
                'valid_paths': list,     # 有效的路径列表
                'invalid_paths': list,   # 无效的路径列表
                'files': list,          # 文件路径列表
                'folders': list         # 文件夹路径列表
            }
        """
        library_path = normalize_path_for_processing(library_path)
        valid_paths = []
        invalid_paths = []
        files = []
        folders = []

        for path in paths:
            normalized_path = normalize_path_for_processing(path)

            # 检查路径是否存在
            if not os.path.exists(normalized_path):
                invalid_paths.append(path)
                continue

            # 检查路径是否在图库内
            try:
                # 使用 os.path.commonpath 检查是否在图库内
                common_path = os.path.commonpath([library_path, normalized_path])
                if normalize_path_for_processing(common_path) != library_path:
                    invalid_paths.append(path)
                    continue
            except ValueError:
                # 不同驱动器的路径会抛出 ValueError
                invalid_paths.append(path)
                continue

            # 路径有效，分类为文件或文件夹
            valid_paths.append(normalized_path)
            if os.path.isfile(normalized_path):
                files.append(normalized_path)
            elif os.path.isdir(normalized_path):
                folders.append(normalized_path)

        return {
            'valid': len(invalid_paths) == 0,
            'valid_paths': valid_paths,
            'invalid_paths': invalid_paths,
            'files': files,
            'folders': folders
        }

    @staticmethod
    def collect_deletion_info(files: List[str], folders: List[str]) -> dict:
        """
        收集删除信息统计

        Args:
            files: 要删除的文件列表
            folders: 要删除的文件夹列表

        Returns:
            dict: 删除信息统计
        """
        deletion_info = {
            'total_files': len(files),
            'total_folders': len(folders),
            'folder_contents': {},  # 文件夹内容统计
            'image_files': [],      # 图片文件列表
            'other_files': []       # 其他文件列表
        }

        # 分析文件
        image_extensions = SUPPORTED_IMAGE_EXTENSIONS
        for file_path in files:
            filename = os.path.basename(file_path)
            ext = os.path.splitext(filename.lower())[1]
            if ext in image_extensions:
                deletion_info['image_files'].append(file_path)
            else:
                deletion_info['other_files'].append(file_path)

        # 分析文件夹内容
        for folder_path in folders:
            try:
                folder_name = os.path.basename(folder_path)
                folder_files = []
                folder_image_count = 0
                folder_other_count = 0

                for root, dirs, files in os.walk(folder_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        folder_files.append(file_path)
                        ext = os.path.splitext(file.lower())[1]
                        if ext in image_extensions:
                            folder_image_count += 1
                        else:
                            folder_other_count += 1

                deletion_info['folder_contents'][folder_name] = {
                    'path': folder_path,
                    'total_files': len(folder_files),
                    'image_files': folder_image_count,
                    'other_files': folder_other_count,
                    'file_list': folder_files
                }

            except Exception as e:
                logging.warning(f"分析文件夹内容时出错 {folder_path}: {str(e)}")

        return deletion_info

    @staticmethod
    def delete_files_and_folders(files: List[str], folders: List[str], log_callback=None) -> dict:
        """
        执行删除操作

        Args:
            files: 要删除的文件列表
            folders: 要删除的文件夹列表
            log_callback: 日志回调函数

        Returns:
            dict: 删除结果统计
        """
        results = {
            'deleted_files': 0,
            'deleted_folders': 0,
            'failed_files': 0,
            'failed_folders': 0,
            'deleted_file_list': [],
            'deleted_folder_list': [],
            'failed_operations': []
        }

        # 删除文件
        for file_path in files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    results['deleted_files'] += 1
                    results['deleted_file_list'].append(file_path)
                    if log_callback:
                        log_callback(f"✅ 删除文件: {os.path.basename(file_path)}")
                else:
                    if log_callback:
                        log_callback(f"⚠️ 文件不存在，跳过: {os.path.basename(file_path)}")
            except Exception as e:
                results['failed_files'] += 1
                error_msg = f"删除文件失败: {os.path.basename(file_path)} -> {str(e)}"
                results['failed_operations'].append(error_msg)
                if log_callback:
                    log_callback(f"❌ {error_msg}")

        # 删除文件夹
        for folder_path in folders:
            try:
                if os.path.exists(folder_path):
                    import shutil
                    folder_name = os.path.basename(folder_path)

                    # 统计文件夹内容
                    total_files = 0
                    total_dirs = 0
                    try:
                        for root, dirs, files in os.walk(folder_path):
                            total_files += len(files)
                            total_dirs += len(dirs)
                        if log_callback:
                            log_callback(f"📁 正在删除文件夹 '{folder_name}' (包含 {total_files} 个文件, {total_dirs} 个子文件夹)")
                    except Exception:
                        if log_callback:
                            log_callback(f"📁 正在删除文件夹 '{folder_name}'")

                    # 删除整个文件夹及其所有内容
                    shutil.rmtree(folder_path)
                    results['deleted_folders'] += 1
                    results['deleted_folder_list'].append(folder_path)

                    if log_callback:
                        log_callback(f"✅ 成功删除文件夹: {folder_name} 及其所有内容")

                    # 验证删除是否成功
                    if os.path.exists(folder_path):
                        error_msg = f"文件夹删除后仍然存在: {folder_name}"
                        results['failed_operations'].append(error_msg)
                        if log_callback:
                            log_callback(f"⚠️ {error_msg}")
                else:
                    if log_callback:
                        log_callback(f"⚠️ 文件夹不存在，跳过: {os.path.basename(folder_path)}")
            except PermissionError as e:
                results['failed_folders'] += 1
                error_msg = f"删除文件夹失败(权限不足): {os.path.basename(folder_path)} -> {str(e)}"
                results['failed_operations'].append(error_msg)
                if log_callback:
                    log_callback(f"❌ {error_msg}")
            except OSError as e:
                results['failed_folders'] += 1
                error_msg = f"删除文件夹失败(系统错误): {os.path.basename(folder_path)} -> {str(e)}"
                results['failed_operations'].append(error_msg)
                if log_callback:
                    log_callback(f"❌ {error_msg}")
            except Exception as e:
                results['failed_folders'] += 1
                error_msg = f"删除文件夹失败: {os.path.basename(folder_path)} -> {str(e)}"
                results['failed_operations'].append(error_msg)
                if log_callback:
                    log_callback(f"❌ {error_msg}")

        return results


# ---------------------------
# 拖拽图片处理模块
# ---------------------------
class DragDropImageProcessor:
    """
    拖拽图片处理器
    负责处理拖拽的图片文件，执行旋转、复制和重复检测操作
    """

    @staticmethod
    def get_supported_image_extensions():
        """获取支持的图片格式"""
        return SUPPORTED_IMAGE_EXTENSIONS

    @staticmethod
    def is_image_file(file_path):
        """判断文件是否为支持的图片格式"""
        if not os.path.isfile(file_path):
            return False
        ext = os.path.splitext(file_path.lower())[1]
        return ext in DragDropImageProcessor.get_supported_image_extensions()

    @staticmethod
    def extract_image_files_from_urls(urls: List[QUrl]) -> List[str]:
        """从URL列表中提取图片文件路径，支持文件夹拖拽"""
        image_files = []
        for url in urls:
            if url.isLocalFile():
                file_path = url.toLocalFile()
                if os.path.isfile(file_path):
                    # 处理单个文件
                    if DragDropImageProcessor.is_image_file(file_path):
                        image_files.append(file_path)
                elif os.path.isdir(file_path):
                    # 处理文件夹，递归提取所有图片文件
                    folder_images = DragDropImageProcessor.extract_images_from_folder(file_path)
                    image_files.extend(folder_images)
        return image_files

    @staticmethod
    def extract_paths_from_urls(urls: List[QUrl]) -> List[str]:
        """从URL列表中提取原始文件和文件夹路径（用于删除操作）"""
        paths = []
        for url in urls:
            if url.isLocalFile():
                file_path = url.toLocalFile()
                if os.path.exists(file_path):  # 文件或文件夹都接受
                    paths.append(file_path)
        return paths

    @staticmethod
    def extract_images_from_folder(folder_path: str) -> List[str]:
        """从文件夹中递归提取所有图片文件"""
        image_files = []
        supported_extensions = DragDropImageProcessor.get_supported_image_extensions()

        try:
            # 递归遍历文件夹
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    # 检查文件扩展名
                    if os.path.splitext(file.lower())[1] in supported_extensions:
                        image_files.append(file_path)
        except Exception as e:
            logging.warning(f"扫描文件夹时出错 {folder_path}: {str(e)}")

        return image_files

    @staticmethod
    def check_duplicate_in_library_enhanced(image_path: str, library_path: str) -> dict:
        """
        增强版重复检测方法 - 不再进行文件名清洗
        检查图片是否在图库中已存在，基于原始文件名进行检测

        返回: {
            'has_same_name': bool,           # 是否有同名文件
            'is_same_aspect_ratio': bool,    # 是否有同名同宽高比文件
            'existing_path': str,            # 已存在的文件路径
            'clean_filename': str,           # 原始文件名（不再清洗）
            'suggested_filename': str        # 建议的文件名
        }
        """
        try:
            original_filename = os.path.basename(image_path)
            clean_filename = original_filename  # 不再清洗，直接使用原始文件名
            name, ext = os.path.splitext(clean_filename)

            # 获取当前图片的宽高比
            try:
                with Image.open(image_path) as current_img:
                    current_width, current_height = current_img.size
                    current_aspect_ratio = current_width / current_height if current_height > 0 else 0
            except Exception as e:
                logging.warning(f"无法读取当前图片宽高比: {str(e)}")
                return {
                    'has_same_name': False,
                    'is_same_aspect_ratio': False,
                    'existing_path': "",
                    'clean_filename': clean_filename,
                    'suggested_filename': clean_filename
                }

            # 检查所有可能的序号变体文件
            candidate_files = []

            # 1. 检查基础文件名
            base_path = os.path.join(library_path, clean_filename)
            if os.path.exists(base_path):
                candidate_files.append((base_path, 0))  # (路径, 序号)

            # 2. 检查序号变体文件（_2, _3, _4等）
            # 优化：检查更多可能的序号，避免遗漏
            consecutive_missing_count = 0
            max_consecutive_missing = 3  # 减少到3个连续缺失，提高检测效率

            for i in range(2, min(MAX_FILENAME_COUNTER, 20)):  # 限制检查范围到20，提高性能
                numbered_filename = f"{name}_{i}{ext}"
                numbered_path = os.path.join(library_path, numbered_filename)
                if os.path.exists(numbered_path):
                    candidate_files.append((numbered_path, i))
                    consecutive_missing_count = 0  # 重置连续缺失计数
                else:
                    consecutive_missing_count += 1
                    # 只有连续缺失超过阈值时才停止检查
                    if consecutive_missing_count >= max_consecutive_missing:
                        break

            # 如果没有找到任何候选文件，肯定不重复
            if not candidate_files:
                return {
                    'has_same_name': False,
                    'is_same_aspect_ratio': False,
                    'existing_path': "",
                    'clean_filename': clean_filename,
                    'suggested_filename': clean_filename
                }

            # 检查每个候选文件的宽高比
            tolerance = ASPECT_RATIO_TOLERANCE
            same_aspect_ratio_files = []
            different_aspect_ratio_files = []

            for candidate_path, sequence_num in candidate_files:
                try:
                    with Image.open(candidate_path) as existing_img:
                        existing_width, existing_height = existing_img.size
                        existing_aspect_ratio = existing_width / existing_height if existing_height > 0 else 0

                    # 比较宽高比
                    aspect_ratio_diff = abs(current_aspect_ratio - existing_aspect_ratio)

                    if aspect_ratio_diff < tolerance:
                        same_aspect_ratio_files.append((candidate_path, sequence_num, existing_aspect_ratio))
                    else:
                        different_aspect_ratio_files.append((candidate_path, sequence_num, existing_aspect_ratio))

                except Exception as e:
                    logging.warning(f"无法读取候选文件宽高比 {candidate_path}: {str(e)}")
                    continue

            # 有同名文件
            has_same_name = True

            if same_aspect_ratio_files:
                # 有同名同宽高比文件
                return {
                    'has_same_name': True,
                    'is_same_aspect_ratio': True,
                    'existing_path': same_aspect_ratio_files[0][0],
                    'clean_filename': clean_filename,
                    'suggested_filename': clean_filename
                }
            else:
                # 只有同名不同宽高比文件，需要智能生成新的文件名
                # 按宽高比倒序排列现有文件，然后确定当前文件的位置
                different_aspect_ratio_files.sort(key=lambda x: x[2], reverse=True)  # 按宽高比倒序

                # 优化的文件名分配策略：
                # 1. 宽高比最大的使用基础文件名
                # 2. 宽高比较小的使用_2序号
                # 3. 如果当前图片宽高比最大，需要重命名现有文件
                
                if not different_aspect_ratio_files:
                    # 理论上不应该到这里，但作为保险
                    suggested_filename = clean_filename
                else:
                    # 找到当前文件在宽高比排序中的位置
                    insert_position = 0
                    for idx, (file_path, seq_num, aspect_ratio) in enumerate(different_aspect_ratio_files):
                        if current_aspect_ratio > aspect_ratio:
                            insert_position = idx
                            break
                        insert_position = idx + 1

                    # 智能文件名分配
                    if insert_position == 0:
                        # 当前图片宽高比最大，应该使用基础文件名
                        # 但现有文件已经占用了基础文件名，所以当前图片使用基础文件名
                        # 现有的文件在实际处理时会被重命名为_2
                        suggested_filename = clean_filename
                    else:
                        # 当前图片宽高比较小，使用_2序号
                        suggested_filename = f"{name}_2{ext}"

                return {
                    'has_same_name': True,
                    'is_same_aspect_ratio': False,
                    'existing_path': different_aspect_ratio_files[0][0] if different_aspect_ratio_files else "",
                    'clean_filename': clean_filename,
                    'suggested_filename': suggested_filename
                }

        except Exception as e:
            # 如果检查失败，假设不重复
            logging.warning(f"增强重复检查失败: {str(e)}")
            original_filename = os.path.basename(image_path)
            clean_filename = original_filename  # 不再清洗，直接使用原始文件名
            return {
                'has_same_name': False,
                'is_same_aspect_ratio': False,
                'existing_path': "",
                'clean_filename': clean_filename,
                'suggested_filename': clean_filename
            }

    @staticmethod
    def check_duplicate_in_library(image_path: str, library_path: str) -> tuple:
        """
        检查图片是否在图库中已存在（基于宽高比的智能重复检测）
        检查所有可能的序号变体文件（如图片名字.jpg、图片名字_2.jpg等）
        只有当文件名相同且宽高比也相同时才认为是真正的重复
        返回: (is_duplicate: bool, existing_path: str, original_filename: str)
        """
        try:
            original_filename = os.path.basename(image_path)
            clean_filename = original_filename  # 不再清洗，直接使用原始文件名
            name, ext = os.path.splitext(clean_filename)
            
            # 获取当前图片的宽高比
            try:
                with Image.open(image_path) as current_img:
                    current_width, current_height = current_img.size
                    current_aspect_ratio = current_width / current_height if current_height > 0 else 0
            except Exception as e:
                logging.warning(f"无法读取当前图片宽高比: {str(e)}")
                return False, "", clean_filename
            
            # 检查所有可能的序号变体文件
            candidate_files = []
            
            # 1. 检查基础文件名
            base_path = os.path.join(library_path, clean_filename)
            if os.path.exists(base_path):
                candidate_files.append(base_path)
            
            # 2. 检查序号变体文件（_2, _3, _4等）
            # 修复：不要在遇到第一个不存在的序号时就停止，要检查所有可能的序号
            # 因为可能存在不连续的序号文件（如 mf0159.jpg 和 mf0159_3.jpg，但没有 mf0159_2.jpg）
            consecutive_missing_count = 0
            max_consecutive_missing = 5  # 允许最多5个连续缺失的序号

            for i in range(2, MAX_FILENAME_COUNTER):  # 检查到最大计数器为止
                numbered_filename = f"{name}_{i}{ext}"
                numbered_path = os.path.join(library_path, numbered_filename)
                if os.path.exists(numbered_path):
                    candidate_files.append(numbered_path)
                    consecutive_missing_count = 0  # 重置连续缺失计数
                else:
                    consecutive_missing_count += 1
                    # 只有连续缺失超过阈值时才停止检查
                    if consecutive_missing_count >= max_consecutive_missing:
                        break
            
            # 如果没有找到任何候选文件，肯定不重复
            if not candidate_files:
                return False, "", clean_filename
            
            # 检查每个候选文件的宽高比
            tolerance = ASPECT_RATIO_TOLERANCE
            for candidate_path in candidate_files:
                try:
                    with Image.open(candidate_path) as existing_img:
                        existing_width, existing_height = existing_img.size
                        existing_aspect_ratio = existing_width / existing_height if existing_height > 0 else 0
                    
                    # 比较宽高比
                    aspect_ratio_diff = abs(current_aspect_ratio - existing_aspect_ratio)
                    
                    if aspect_ratio_diff < tolerance:
                        # 找到宽高比相同的文件，认为是真正的重复
                        return True, candidate_path, clean_filename
                        
                except Exception as e:
                    logging.warning(f"无法读取候选文件宽高比 {candidate_path}: {str(e)}")
                    continue
            
            # 所有候选文件的宽高比都不同，不是重复
            return False, "", clean_filename

        except Exception as e:
            # 如果检查失败，假设不重复
            logging.warning(f"重复检查失败: {str(e)}")
            original_filename = os.path.basename(image_path)
            clean_filename = original_filename  # 不再清洗，直接使用原始文件名
            return False, "", clean_filename

    @staticmethod
    def _generate_smart_filename(output_dir: str, clean_filename: str, current_aspect_ratio: float, log_callback=None) -> str:
        """
        基于宽高比的智能文件名生成逻辑（简化版，避免复杂的文件重命名）

        Args:
            output_dir: 输出目录
            clean_filename: 原始文件名
            current_aspect_ratio: 当前图片的宽高比
            log_callback: 日志回调函数

        Returns:
            str: 最终的文件名
        """
        try:
            name, ext = os.path.splitext(clean_filename)
            target_path = os.path.join(output_dir, clean_filename)
            
            # 如果文件不存在，直接返回原文件名
            if not os.path.exists(target_path):
                if log_callback:
                    log_callback(f"📁 文件名可用: {clean_filename}")
                return clean_filename
            
            # 文件已存在，检查宽高比
            if log_callback:
                log_callback(f"📁 检测到同名文件，分析宽高比...")
            
            # 获取现有文件的宽高比
            existing_aspect_ratio = None
            try:
                with Image.open(target_path) as img:
                    width, height = img.size
                    existing_aspect_ratio = width / height if height > 0 else 0
                    if log_callback:
                        log_callback(f"📐 现有文件宽高比: {existing_aspect_ratio:.3f} ({width}x{height})")
            except Exception as e:
                if log_callback:
                    log_callback(f"⚠️ 无法获取现有文件宽高比: {str(e)}")
                existing_aspect_ratio = 0
            
            # 比较宽高比（使用统一容差）
            if abs(current_aspect_ratio - existing_aspect_ratio) < ASPECT_RATIO_TOLERANCE:
                # 宽高比相同，替换现有文件
                if log_callback:
                    log_callback(f"🔄 宽高比相同(差值: {abs(current_aspect_ratio - existing_aspect_ratio):.4f} < {ASPECT_RATIO_TOLERANCE})，将替换现有文件")
                return clean_filename
            
            # 宽高比不同，生成序号文件名（简化逻辑，不重排现有文件）
            if log_callback:
                log_callback(f"📊 宽高比不同(差值: {abs(current_aspect_ratio - existing_aspect_ratio):.4f} >= {ASPECT_RATIO_TOLERANCE})，生成序号文件名")
            
            # 收集所有同名文件及其宽高比，但不重排现有文件
            existing_files_info = []
            
            # 检查基础文件
            existing_files_info.append((clean_filename, existing_aspect_ratio))
            
            # 检查所有序号文件
            counter = 2
            while counter <= MAX_FILENAME_COUNTER:  # 限制检查范围，避免无限循环
                numbered_filename = f"{name}_{counter}{ext}"
                numbered_path = os.path.join(output_dir, numbered_filename)
                
                if os.path.exists(numbered_path):
                    try:
                        with Image.open(numbered_path) as img:
                            width, height = img.size
                            aspect_ratio = width / height if height > 0 else 0
                            existing_files_info.append((numbered_filename, aspect_ratio))
                            if log_callback:
                                log_callback(f"📐 发现序号文件 {numbered_filename}: 宽高比 {aspect_ratio:.3f}")
                    except Exception:
                        existing_files_info.append((numbered_filename, 0))
                
                counter += 1
            
            # 按宽高比倒序排序现有文件
            existing_files_info.sort(key=lambda x: x[1], reverse=True)
            
            # 找到当前图片在排序中的位置
            insert_position = 0
            for i, (filename, aspect_ratio) in enumerate(existing_files_info):
                if current_aspect_ratio > aspect_ratio:
                    insert_position = i
                    break
                insert_position = i + 1
            
            if log_callback:
                log_callback(f"📍 当前图片按宽高比应排在第 {insert_position + 1} 位")
            
            # 优化策略：确保宽高比倒序排列（大的在前，小的在后）
            # 如果当前图片宽高比最大，使用基础文件名（覆盖）
            if insert_position == 0:
                if log_callback:
                    log_callback(f"🔄 当前图片宽高比最大({current_aspect_ratio:.3f})，将使用基础文件名")
                return clean_filename
            else:
                # 当前图片不是宽高比最大的，使用_2序号
                # 按照用户要求，只保留两个版本：基础名和_2
                candidate_filename = f"{name}_2{ext}"
                if log_callback:
                    log_callback(f"📁 当前图片宽高比较小({current_aspect_ratio:.3f})，使用序号文件名: {candidate_filename}")
                return candidate_filename
            
        except Exception as e:
            if log_callback:
                log_callback(f"⚠️ 智能文件名生成失败: {str(e)}，使用备用方案")
            
            # 备用方案：使用传统的序号逻辑
            name, ext = os.path.splitext(clean_filename)
            counter = 1
            while True:
                if counter == 1:
                    new_filename = clean_filename
                else:
                    new_filename = f"{name}_{counter}{ext}"
                
                new_path = os.path.join(output_dir, new_filename)
                if not os.path.exists(new_path):
                    return new_filename
                counter += 1
                
                if counter > MAX_FILENAME_COUNTER:  # 防止无限循环
                    import time
                    timestamp = int(time.time() * 1000) % 100000
                    return f"{name}_{timestamp}{ext}"

    @staticmethod
    def _process_image_core(image_path: str, output_dir: str, final_filename: str, 
                           use_smart_naming: bool = False, log_callback=None) -> tuple:
        """
        图片处理核心逻辑：横向放置 + 复制到目标文件夹
        
        Args:
            image_path: 源图片路径
            output_dir: 输出目录
            final_filename: 最终文件名
            use_smart_naming: 是否使用智能命名（基于宽高比）
            log_callback: 日志回调函数
            
        Returns:
            tuple: (success: bool, processed_filename: str, message: str)
        """
        try:
            # 基于中文名字确定目标目录
            chinese_name = ChineseNameExtractor.extract_chinese_name(final_filename)
            actual_output_dir = output_dir

            if chinese_name:
                matching_folder = ChineseNameExtractor.find_matching_folder(chinese_name, output_dir)
                if matching_folder:
                    actual_output_dir = matching_folder
                    if log_callback:
                        folder_name = os.path.basename(matching_folder)
                        log_callback(f"📂 找到匹配文件夹: {folder_name}")
                else:
                    if log_callback:
                        log_callback(f"📂 未找到匹配文件夹，使用主目录")

            # 使用临时文件进行处理
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_file_path = os.path.join(temp_dir, final_filename)
                if log_callback:
                    log_callback(f"📂 创建临时文件: {temp_file_path}")

                # 复制原文件到临时位置
                shutil.copy2(image_path, temp_file_path)
                if log_callback:
                    log_callback(f"📋 文件复制到临时位置完成")

                # 检查并旋转图片（如果需要）
                try:
                    if log_callback:
                        log_callback(f"🔄 检查图片方向...")

                    is_portrait = ImageOrientationProcessor.is_portrait(temp_file_path)
                    if log_callback:
                        orientation = "竖向" if is_portrait else "横向"
                        log_callback(f"📐 图片方向: {orientation}")

                    if is_portrait:
                        if log_callback:
                            log_callback(f"🔄 开始旋转图片...")

                        rotation_success = ImageOrientationProcessor.rotate_image_to_landscape(temp_file_path)
                        if rotation_success:
                            if log_callback:
                                log_callback(f"✅ 图片旋转成功: {final_filename} (竖向 → 横向)")
                        else:
                            if log_callback:
                                log_callback(f"⚠️ 图片旋转失败: {final_filename}")
                    else:
                        if log_callback:
                            log_callback(f"✅ 图片无需旋转: {final_filename} (已是横向)")
                except Exception as rotation_error:
                    if log_callback:
                        log_callback(f"⚠️ 图片旋转处理失败: {final_filename}, 错误: {str(rotation_error)}")
                    # 继续处理，不因为旋转失败而停止整个流程

                # 确定最终文件名（智能命名或直接使用）
                if use_smart_naming:
                    if log_callback:
                        log_callback(f"📝 基于宽高比生成智能文件名...")

                    # 获取当前图片的宽高比
                    current_aspect_ratio = 0
                    try:
                        with Image.open(temp_file_path) as img:
                            width, height = img.size
                            current_aspect_ratio = width / height if height > 0 else 0
                            if log_callback:
                                log_callback(f"📐 当前图片宽高比: {current_aspect_ratio:.3f} ({width}x{height})")
                    except Exception as e:
                        if log_callback:
                            log_callback(f"⚠️ 无法获取图片宽高比: {str(e)}")

                    # 使用智能文件名生成逻辑
                    unique_filename = DragDropImageProcessor._generate_smart_filename(
                        actual_output_dir, final_filename, current_aspect_ratio, log_callback
                    )
                    final_filename = unique_filename

                # 复制到目标文件夹
                if not os.path.exists(actual_output_dir):
                    os.makedirs(actual_output_dir, exist_ok=True)
                    if log_callback:
                        log_callback(f"📁 创建目标目录: {actual_output_dir}")

                final_output_path = os.path.join(actual_output_dir, final_filename)

                if log_callback:
                    log_callback(f"📁 最终文件名: {final_filename}")
                    log_callback(f"📍 目标路径: {final_output_path}")

                # 使用安全的文件复制方法，支持覆盖验证
                copy_success, was_overwritten = UltraHighPerformanceImageMover._safe_copy_with_retry(
                    temp_file_path, final_output_path, final_filename, max_retries=3, log_callback=log_callback
                )

                if copy_success:
                    if log_callback:
                        if was_overwritten:
                            log_callback(f"✅ 文件已成功覆盖到: {final_output_path}")
                        else:
                            log_callback(f"✅ 文件已成功复制到: {final_output_path}")
                    return True, final_filename, "处理成功"
                else:
                    error_msg = "文件复制失败"
                    if log_callback:
                        log_callback(f"❌ {error_msg}: {final_output_path}")
                    return False, "", error_msg

        except Exception as e:
            error_msg = f"图片处理失败: {str(e)}"
            if log_callback:
                log_callback(f"❌ {error_msg}")
            return False, "", error_msg

    @staticmethod
    def process_single_image_with_suggested_name(image_path: str, output_dir: str, 
                                               suggested_filename: str = None, log_callback=None) -> tuple:
        """
        处理单个图片文件：横向放置 + 复制到目标文件夹（支持建议文件名）
        返回: (success: bool, processed_filename: str, message: str)
        """
        original_filename = ""
        try:
            # 规范化路径格式
            image_path = normalize_path_for_processing(image_path)
            output_dir = normalize_path_for_processing(output_dir)

            if log_callback:
                log_callback(f"🔍 开始处理图片: {image_path}")

            # 基础验证
            if not os.path.exists(image_path):
                error_msg = f"文件不存在: {image_path}"
                if log_callback:
                    log_callback(f"❌ {error_msg}")
                return False, "", error_msg

            if not os.path.exists(output_dir):
                try:
                    os.makedirs(output_dir, exist_ok=True)
                    if log_callback:
                        log_callback(f"📁 创建目标文件夹: {output_dir}")
                except Exception as e:
                    error_msg = f"无法创建目标文件夹 {output_dir}: {str(e)}"
                    if log_callback:
                        log_callback(f"❌ {error_msg}")
                    return False, "", error_msg

            if not DragDropImageProcessor.is_image_file(image_path):
                error_msg = f"不支持的文件格式: {image_path}"
                if log_callback:
                    log_callback(f"❌ {error_msg}")
                return False, "", error_msg

            # 确定文件名
            original_filename = os.path.basename(image_path)
            if log_callback:
                log_callback(f"📁 原始文件名: {original_filename}")

            # 应用文件名优化
            base_filename = suggested_filename if suggested_filename else original_filename
            final_filename = FilenameOptimizer.optimize_filename(base_filename)

            if log_callback:
                if suggested_filename:
                    log_callback(f"📝 使用建议文件名: {suggested_filename}")
                if final_filename != base_filename:
                    log_callback(f"📝 优化文件名: {base_filename} → {final_filename}")
                else:
                    log_callback(f"✅ 最终文件名: {final_filename}")

            # 调用核心处理逻辑
            return DragDropImageProcessor._process_image_core(
                image_path, output_dir, final_filename, use_smart_naming=False, log_callback=log_callback
            )

        except Exception as e:
            error_msg = f"处理文件 {original_filename or image_path} 时出错: {str(e)}"
            if log_callback:
                log_callback(f"❌ {error_msg}")
                import traceback
                log_callback(f"🔍 详细错误信息: {traceback.format_exc()}")
            return False, "", error_msg
        finally:
            try:
                gc.collect()
            except:
                pass

    @staticmethod
    def process_single_image(image_path: str, output_dir: str, log_callback=None) -> tuple:
        """
        处理单个图片文件：横向放置 + 复制到目标文件夹（使用智能命名）
        返回: (success: bool, processed_filename: str, message: str)
        """
        original_filename = ""
        try:
            # 规范化路径格式
            image_path = normalize_path_for_processing(image_path)
            output_dir = normalize_path_for_processing(output_dir)

            if log_callback:
                log_callback(f"🔍 开始处理图片: {image_path}")

            # 基础验证
            if not os.path.exists(image_path):
                error_msg = f"文件不存在: {image_path}"
                if log_callback:
                    log_callback(f"❌ {error_msg}")
                return False, "", error_msg

            if not os.path.exists(output_dir):
                try:
                    os.makedirs(output_dir, exist_ok=True)
                    if log_callback:
                        log_callback(f"📁 创建目标文件夹: {output_dir}")
                except Exception as e:
                    error_msg = f"无法创建目标文件夹 {output_dir}: {str(e)}"
                    if log_callback:
                        log_callback(f"❌ {error_msg}")
                    return False, "", error_msg

            if not DragDropImageProcessor.is_image_file(image_path):
                error_msg = f"不支持的文件格式: {image_path}"
                if log_callback:
                    log_callback(f"❌ {error_msg}")
                return False, "", error_msg

            # 获取原始文件名
            original_filename = os.path.basename(image_path)
            if log_callback:
                log_callback(f"📁 原始文件名: {original_filename}")

            # 调用核心处理逻辑（使用智能命名）
            return DragDropImageProcessor._process_image_core(
                image_path, output_dir, original_filename, use_smart_naming=True, log_callback=log_callback
            )

        except Exception as e:
            error_msg = f"处理文件 {original_filename or image_path} 时出错: {str(e)}"
            if log_callback:
                log_callback(f"❌ {error_msg}")
                import traceback
                log_callback(f"🔍 详细错误信息: {traceback.format_exc()}")
            return False, "", error_msg
        finally:
            try:
                gc.collect()
            except:
                pass


class DragDropArea(QLabel):
    """
    支持拖拽的区域组件
    """
    files_dropped = pyqtSignal(list)  # 发送拖拽的文件列表

    def __init__(self, text=""):
        super().__init__(text)
        self.setAcceptDrops(True)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setMinimumHeight(120)
        self.setWordWrap(True)

        # 设置样式
        self.setStyleSheet("""
            QLabel {
                border: 2px dashed #cccccc;
                border-radius: 8px;
                background-color: #f9f9f9;
                color: #666666;
                font-size: 14px;
                padding: 20px;
            }
            QLabel:hover {
                border-color: #4CAF50;
                background-color: #f0f8f0;
                color: #4CAF50;
            }
        """)

        # 设置默认文本
        if not text:
            self.setText("""
🖼️ 拖拽图片或文件夹到此区域

支持单个或多个图片文件，以及包含图片的文件夹
自动执行：横向放置 + 复制到选择的文件夹

支持格式：JPG, PNG, BMP, GIF, TIFF, WEBP
            """)

    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            # 检查是否包含图片文件或文件夹
            urls = event.mimeData().urls()

            # 统计文件和文件夹数量
            file_count = 0
            folder_count = 0
            for url in urls:
                if url.isLocalFile():
                    path = url.toLocalFile()
                    if os.path.isfile(path):
                        file_count += 1
                    elif os.path.isdir(path):
                        folder_count += 1

            # 检查是否为删除模式
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'delete_from_library_radio'):
                    main_window = parent
                    break
                parent = parent.parent()

            is_delete_mode = (main_window and
                            hasattr(main_window, 'delete_from_library_radio') and
                            main_window.delete_from_library_radio.isChecked())

            if is_delete_mode:
                # 删除模式：检查是否有有效的文件或文件夹
                paths = DragDropImageProcessor.extract_paths_from_urls(urls)
                if paths:
                    event.acceptProposedAction()
                    # 设置删除模式的红色警告样式
                    self.setStyleSheet("""
                        QLabel {
                            border: 2px dashed #d32f2f;
                            border-radius: 8px;
                            background-color: #ffebee;
                            color: #d32f2f;
                            font-size: 14px;
                            padding: 20px;
                            font-weight: bold;
                        }
                    """)

                    # 生成删除提示文本
                    total_items = file_count + folder_count
                    if folder_count > 0:
                        if file_count > 0:
                            self.setText(f"🗑️ 准备删除 {total_items} 项\n（{file_count} 个文件 + {folder_count} 个文件夹）")
                        else:
                            self.setText(f"🗑️ 准备删除 {folder_count} 个文件夹")
                    else:
                        self.setText(f"🗑️ 准备删除 {file_count} 个文件")
                else:
                    event.ignore()
            else:
                # 添加模式：提取所有图片文件（包括文件夹中的）
                image_files = DragDropImageProcessor.extract_image_files_from_urls(urls)
                if image_files:
                    event.acceptProposedAction()
                    # 更新样式以显示可以放置
                    self.setStyleSheet("""
                        QLabel {
                            border: 2px dashed #4CAF50;
                            border-radius: 8px;
                            background-color: #e8f5e8;
                            color: #4CAF50;
                            font-size: 14px;
                            padding: 20px;
                            font-weight: bold;
                        }
                    """)

                    # 生成提示文本
                    if folder_count > 0:
                        if file_count > 0:
                            self.setText(f"📥 准备接收 {len(image_files)} 个图片文件\n（来自 {file_count} 个文件 + {folder_count} 个文件夹）")
                        else:
                            self.setText(f"📥 准备接收 {len(image_files)} 个图片文件\n（来自 {folder_count} 个文件夹）")
                    else:
                        self.setText(f"📥 准备接收 {len(image_files)} 个图片文件")
                else:
                    event.ignore()
        else:
            event.ignore()

    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        _ = event  # 标记参数已使用
        # 恢复原始样式
        self.setStyleSheet("""
            QLabel {
                border: 2px dashed #cccccc;
                border-radius: 8px;
                background-color: #f9f9f9;
                color: #666666;
                font-size: 14px;
                padding: 20px;
            }
            QLabel:hover {
                border-color: #4CAF50;
                background-color: #f0f8f0;
                color: #4CAF50;
            }
        """)
        self.setText("""
🖼️ 拖拽图片或文件夹到此区域

支持单个或多个图片文件，以及包含图片的文件夹
自动执行：横向放置 + 复制到选择的文件夹

支持格式：JPG, PNG, BMP, GIF, TIFF, WEBP
        """)

    def dropEvent(self, event: QDropEvent):
        """拖拽放置事件"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()

            # 检查是否为删除模式（通过检查主窗口的状态）
            # 这里我们需要从主窗口获取操作模式
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'delete_from_library_radio'):
                    main_window = parent
                    break
                parent = parent.parent()

            is_delete_mode = (main_window and
                            hasattr(main_window, 'delete_from_library_radio') and
                            main_window.delete_from_library_radio.isChecked())

            if is_delete_mode:
                # 删除模式：提取原始路径（文件和文件夹）
                paths = DragDropImageProcessor.extract_paths_from_urls(urls)
                if paths:
                    event.acceptProposedAction()
                    self.files_dropped.emit(paths)
                    # 恢复原始样式
                    self.dragLeaveEvent(None)
                else:
                    event.ignore()
            else:
                # 添加模式：提取图片文件（展开文件夹）
                image_files = DragDropImageProcessor.extract_image_files_from_urls(urls)
                if image_files:
                    event.acceptProposedAction()
                    self.files_dropped.emit(image_files)
                    # 恢复原始样式
                    self.dragLeaveEvent(None)
                else:
                    event.ignore()
        else:
            event.ignore()


class DragDropDeleteWorker(QThread):
    """
    拖拽删除工作线程
    负责执行删除操作和索引更新
    """
    progress = pyqtSignal(int)
    log = pyqtSignal(str)
    finished = pyqtSignal(bool, str, dict)  # success, message, deletion_statistics

    def __init__(self, paths: List[str], library_path: str):
        super().__init__()
        self.paths = paths
        self.library_path = library_path
        self._stop_requested = False

    def request_stop(self):
        """请求停止处理"""
        self._stop_requested = True

    def run(self):
        """执行删除操作"""
        try:
            self.log.emit("🗑️ 开始删除操作...")

            # 第一步：验证路径
            self.progress.emit(10)
            self.log.emit("📋 验证拖拽路径...")

            validation_result = DragDropDeleteProcessor.validate_paths_in_library(
                self.paths, self.library_path
            )

            if not validation_result['valid']:
                invalid_count = len(validation_result['invalid_paths'])
                error_msg = f"发现 {invalid_count} 个无效路径（不在图库内或不存在）"
                self.log.emit(f"❌ {error_msg}")
                for invalid_path in validation_result['invalid_paths']:
                    self.log.emit(f"   ❌ {invalid_path}")
                self.finished.emit(False, error_msg, {})
                return

            if self._stop_requested:
                self.finished.emit(False, "用户取消操作", {})
                return

            # 第二步：收集删除信息
            self.progress.emit(30)
            self.log.emit("📊 分析删除内容...")

            deletion_info = DragDropDeleteProcessor.collect_deletion_info(
                validation_result['files'], validation_result['folders']
            )

            total_items = deletion_info['total_files'] + deletion_info['total_folders']
            self.log.emit(f"📋 将删除 {deletion_info['total_files']} 个文件和 {deletion_info['total_folders']} 个文件夹")

            # 显示详细信息
            if deletion_info['image_files']:
                self.log.emit(f"🖼️ 图片文件: {len(deletion_info['image_files'])} 个")
            if deletion_info['other_files']:
                self.log.emit(f"📄 其他文件: {len(deletion_info['other_files'])} 个")

            for folder_name, folder_info in deletion_info['folder_contents'].items():
                self.log.emit(f"📁 文件夹 '{folder_name}': {folder_info['total_files']} 个文件 "
                            f"(图片: {folder_info['image_files']}, 其他: {folder_info['other_files']})")

            if self._stop_requested:
                self.finished.emit(False, "用户取消操作", {})
                return

            # 第三步：执行删除
            self.progress.emit(50)
            self.log.emit("🗑️ 执行删除操作...")

            deletion_results = DragDropDeleteProcessor.delete_files_and_folders(
                validation_result['files'], validation_result['folders'], self.log.emit
            )

            if self._stop_requested:
                self.finished.emit(False, "用户取消操作", {})
                return

            # 第四步：更新索引
            self.progress.emit(80)
            if HAS_IMAGE_INDEXER:
                self.log.emit("🔄 更新图库索引...")
                try:
                    indexer = ImageIndexerDuckDB(fast_mode=True)
                    if indexer.is_indexed(self.library_path):
                        success, message, count = indexer.scan_library(self.library_path, fast_mode=True)
                        if success:
                            self.log.emit(f"✅ 索引更新完成，当前索引 {count} 个文件")
                        else:
                            self.log.emit(f"⚠️ 索引更新失败: {message}")
                    else:
                        self.log.emit("⚠️ 图库未建立索引，跳过索引更新")
                except Exception as e:
                    self.log.emit(f"⚠️ 索引更新出错: {str(e)}")
            else:
                self.log.emit("⚠️ 图库索引器不可用，跳过索引更新")

            # 完成
            self.progress.emit(100)

            # 生成统计信息
            statistics = {
                'total_files_requested': deletion_info['total_files'],
                'total_folders_requested': deletion_info['total_folders'],
                'deleted_files': deletion_results['deleted_files'],
                'deleted_folders': deletion_results['deleted_folders'],
                'failed_files': deletion_results['failed_files'],
                'failed_folders': deletion_results['failed_folders'],
                'deletion_info': deletion_info,
                'deletion_results': deletion_results
            }

            success_count = deletion_results['deleted_files'] + deletion_results['deleted_folders']
            total_requested = deletion_info['total_files'] + deletion_info['total_folders']

            if success_count == total_requested:
                message = f"删除完成！成功删除 {success_count} 项"
                self.finished.emit(True, message, statistics)
            else:
                failed_count = deletion_results['failed_files'] + deletion_results['failed_folders']
                message = f"删除部分完成！成功 {success_count} 项，失败 {failed_count} 项"
                self.finished.emit(True, message, statistics)

        except Exception as e:
            error_msg = f"删除操作出错: {str(e)}"
            self.log.emit(f"❌ {error_msg}")
            self.finished.emit(False, error_msg, {})


class DragDropProcessorWorker(QThread):
    """
    拖拽图片处理工作线程
    支持重复检测和策略处理
    优化：分批处理同名不同宽高比图片，确保第一次拖拽也能正确显示
    """
    progress = pyqtSignal(int)
    log = pyqtSignal(str)
    finished = pyqtSignal(bool, str, int, dict, dict)  # success, message, processed_count, statistics, file_details
    duplicate_found = pyqtSignal(str, str, str)  # image_path, existing_path, clean_filename

    def __init__(self, image_files: List[str], output_dir: str, check_duplicates: bool = True, duplicate_strategy: str = "overwrite"):
        super().__init__()
        self.image_files = image_files
        self.output_dir = output_dir
        self.check_duplicates = check_duplicates
        self.duplicate_strategy = duplicate_strategy  # "overwrite" 或 "skip"
        self.duplicate_actions = {}  # 存储用户对重复文件的选择

        # 统计信息
        self.statistics = {
            'total_files': 0,
            'processed_files': 0,
            'success_files': 0,
            'skipped_files': 0,
            'error_files': 0,
            'same_aspect_ratio_overwritten': 0,  # 同名同宽高比覆盖的文件数
            'different_aspect_ratio_renamed': 0,  # 同名不同宽高比重命名的文件数
            'new_files_processed': 0,  # 新文件处理数（没有同名冲突的文件）
        }

        # 详细文件列表跟踪
        self.file_details = {
            'skipped_files': [],  # 被跳过的文件：[{'original_name': str, 'reason': str}, ...]
            'overwritten_files': [],  # 被覆盖的文件：[{'original_name': str, 'target_name': str, 'reason': str}, ...]
            'new_files': [],  # 新添加的文件：[{'original_name': str, 'target_name': str}, ...]
            'renamed_files': [],  # 重命名的文件：[{'original_name': str, 'target_name': str, 'reason': str}, ...]
            'error_files': [],  # 处理失败的文件：[{'original_name': str, 'error': str}, ...]
        }

    def set_duplicate_action(self, image_path: str, action: str):
        """设置重复文件的处理动作"""
        self.duplicate_actions[image_path] = action

    def _group_images_by_clean_name_and_aspect_ratio(self, image_files: List[str]) -> dict:
        """
        按原始文件名和宽高比对图片进行分组，支持基于中文名字的智能分类
        返回: {
            'clean_name': {
                'same_aspect_ratio': [files],  # 同名同宽高比的文件
                'different_aspect_ratio': [files],  # 同名不同宽高比的文件
                'new_files': [files]  # 新文件（没有同名冲突）
            }
        }
        """
        groups = {}

        # 第一步：按中文名字对图片进行预分组
        chinese_groups = ChineseNameExtractor.group_images_by_chinese_name(
            [(path, os.path.basename(path)) for path in image_files]
        )

        # 记录中文名字分组信息
        self.log.emit("📊 中文名字分组分析:")
        for chinese_name, files in chinese_groups.items():
            if chinese_name != 'no_chinese':
                if len(files) > 1:
                    self.log.emit(f"   {chinese_name}: {len(files)} 个文件 (将创建文件夹)")
                    # 为多个相同中文名字的图片创建文件夹
                    folder_path = os.path.join(self.output_dir, chinese_name)
                    ChineseNameExtractor.create_folder_if_needed(folder_path, self.log.emit)
                else:
                    self.log.emit(f"   {chinese_name}: 1 个文件 (放置在主目录)")

        if 'no_chinese' in chinese_groups:
            self.log.emit(f"   无中文名字: {len(chinese_groups['no_chinese'])} 个文件 (放置在主目录)")

        # 第二步：收集所有文件的基本信息
        file_infos = []
        for image_path in image_files:
            try:
                # 获取原始文件名
                original_filename = os.path.basename(image_path)
                clean_filename = original_filename
                clean_name = os.path.splitext(clean_filename)[0]

                # 获取当前图片的宽高比
                try:
                    with Image.open(image_path) as img:
                        width, height = img.size
                        current_aspect_ratio = width / height if height > 0 else 0
                except Exception as e:
                    self.log.emit(f"⚠️ 无法获取图片宽高比: {original_filename}, 错误: {str(e)}")
                    current_aspect_ratio = 0

                # 提取中文名字并确定目标目录
                chinese_name = ChineseNameExtractor.extract_chinese_name(clean_filename)
                target_output_dir = self.output_dir

                if chinese_name:
                    # 检查是否有多个相同中文名字的文件
                    same_chinese_files = chinese_groups.get(chinese_name, [])
                    if len(same_chinese_files) > 1:
                        # 多个相同中文名字的文件，使用专门的文件夹
                        target_output_dir = os.path.join(self.output_dir, chinese_name)
                    else:
                        # 只有一个文件，检查是否有匹配的现有文件夹
                        matching_folder = ChineseNameExtractor.find_matching_folder(chinese_name, self.output_dir)
                        if matching_folder:
                            target_output_dir = matching_folder

                file_infos.append({
                    'path': image_path,
                    'original_filename': original_filename,
                    'clean_filename': clean_filename,
                    'clean_name': clean_name,
                    'aspect_ratio': current_aspect_ratio,
                    'chinese_name': chinese_name,
                    'target_output_dir': target_output_dir
                })

            except Exception as e:
                self.log.emit(f"❌ 分析图片时出错: {os.path.basename(image_path)}, 错误: {str(e)}")

        # 第二步：按clean_name分组并分析冲突
        for file_info in file_infos:
            clean_name = file_info['clean_name']

            # 初始化分组
            if clean_name not in groups:
                groups[clean_name] = {
                    'same_aspect_ratio': [],
                    'different_aspect_ratio': [],
                    'new_files': []
                }

            # 检查图库中是否已存在同名文件（在正确的目标目录中检查）
            duplicate_result = DragDropImageProcessor.check_duplicate_in_library_enhanced(
                file_info['path'], file_info['target_output_dir']
            )

            # 检查当前批次中是否有其他同名文件
            current_batch_same_name_files = [
                f for f in file_infos
                if f['clean_name'] == clean_name and f['path'] != file_info['path']
            ]

            has_library_same_name = duplicate_result['has_same_name']
            has_batch_same_name = len(current_batch_same_name_files) > 0

            if has_library_same_name:
                # 图库中有同名文件
                if duplicate_result['is_same_aspect_ratio']:
                    # 同名同宽高比
                    groups[clean_name]['same_aspect_ratio'].append({
                        'path': file_info['path'],
                        'aspect_ratio': file_info['aspect_ratio'],
                        'clean_filename': file_info['clean_filename'],
                        'suggested_filename': duplicate_result['suggested_filename']
                    })
                else:
                    # 同名不同宽高比
                    groups[clean_name]['different_aspect_ratio'].append({
                        'path': file_info['path'],
                        'aspect_ratio': file_info['aspect_ratio'],
                        'clean_filename': file_info['clean_filename'],
                        'suggested_filename': duplicate_result['suggested_filename']
                    })
            elif has_batch_same_name:
                # 图库中没有同名文件，但当前批次中有同名文件，归类为不同宽高比处理
                groups[clean_name]['different_aspect_ratio'].append({
                    'path': file_info['path'],
                    'aspect_ratio': file_info['aspect_ratio'],
                    'clean_filename': file_info['clean_filename'],
                    'suggested_filename': file_info['clean_filename']  # 临时文件名，后续会重新分配
                })
            else:
                # 既没有图库同名文件，也没有批次同名文件，归类为新文件
                groups[clean_name]['new_files'].append({
                    'path': file_info['path'],
                    'aspect_ratio': file_info['aspect_ratio'],
                    'clean_filename': file_info['clean_filename'],
                    'suggested_filename': file_info['clean_filename']
                })

        return groups

    def _process_batch_with_delay(self, batch_files: List[dict], batch_name: str, delay_seconds: float = 0.5) -> tuple:
        """
        处理一批文件，支持延迟处理
        """
        batch_success = 0
        batch_errors = 0
        
        self.log.emit(f"🔄 开始处理{batch_name}批次，共 {len(batch_files)} 个文件")
        
        for i, file_info in enumerate(batch_files):
            try:
                image_path = file_info['path']
                suggested_filename = file_info['suggested_filename']
                target_output_dir = file_info.get('target_output_dir', self.output_dir)

                # 处理单个图片
                success, processed_filename, message = DragDropImageProcessor.process_single_image_with_suggested_name(
                    image_path, target_output_dir, suggested_filename, self.log.emit
                )
                
                if success:
                    batch_success += 1
                    self.log.emit(f"✅ {batch_name}批次处理成功: {processed_filename}")
                else:
                    batch_errors += 1
                    self.log.emit(f"❌ {batch_name}批次处理失败: {message}")
                    # 记录错误文件详细信息
                    original_name = os.path.basename(image_path)
                    self.file_details['error_files'].append({
                        'original_name': original_name,
                        'error': message
                    })

                # 添加小延迟，确保文件系统操作完成
                if delay_seconds > 0 and i < len(batch_files) - 1:
                    time.sleep(delay_seconds)

            except Exception as e:
                batch_errors += 1
                error_msg = str(e)
                self.log.emit(f"❌ {batch_name}批次处理文件时出错: {error_msg}")
                # 记录错误文件详细信息
                original_name = os.path.basename(file_info['path'])
                self.file_details['error_files'].append({
                    'original_name': original_name,
                    'error': error_msg
                })
        
        self.log.emit(f"✅ {batch_name}批次处理完成: 成功 {batch_success} 个，失败 {batch_errors} 个")
        return batch_success, batch_errors

    def _collect_existing_files_info(self, clean_name: str, output_dir: str) -> list:
        """
        收集图库中所有同名文件的信息
        返回: [{'path': str, 'aspect_ratio': float, 'is_new': False}, ...]
        """
        existing_files_info = []

        try:
            # 检查基础文件名（检查所有支持的扩展名）
            for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.tif', '.webp']:
                base_path = os.path.join(output_dir, f"{clean_name}{ext}")
                if os.path.exists(base_path):
                    try:
                        with Image.open(base_path) as img:
                            width, height = img.size
                            aspect_ratio = width / height if height > 0 else 0
                            existing_files_info.append({
                                'path': base_path,
                                'aspect_ratio': aspect_ratio,
                                'is_new': False
                            })
                    except Exception:
                        existing_files_info.append({
                            'path': base_path,
                            'aspect_ratio': 0,
                            'is_new': False
                        })
                    break  # 找到一个就停止

            # 检查序号文件
            counter = 2
            consecutive_missing = 0
            while counter <= MAX_FILENAME_COUNTER and consecutive_missing < 5:
                found_any = False
                for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.tif', '.webp']:
                    numbered_path = os.path.join(output_dir, f"{clean_name}_{counter}{ext}")
                    if os.path.exists(numbered_path):
                        try:
                            with Image.open(numbered_path) as img:
                                width, height = img.size
                                aspect_ratio = width / height if height > 0 else 0
                                existing_files_info.append({
                                    'path': numbered_path,
                                    'aspect_ratio': aspect_ratio,
                                    'is_new': False
                                })
                        except Exception:
                            existing_files_info.append({
                                'path': numbered_path,
                                'aspect_ratio': 0,
                                'is_new': False
                            })
                        found_any = True
                        break

                if found_any:
                    consecutive_missing = 0
                else:
                    consecutive_missing += 1

                counter += 1

        except Exception as e:
            self.log.emit(f"⚠️ 收集现有文件信息时出错: {str(e)}")

        return existing_files_info

    def _create_different_aspect_ratio_batches(self, different_aspect_groups: dict) -> list:
        """
        创建同名不同宽高比文件的分批处理列表
        每个批次中，每个clean_name只包含一个文件

        Args:
            different_aspect_groups: {clean_name: [file_info_list], ...}

        Returns:
            list: [[batch1_files], [batch2_files], ...]
        """
        batches = []

        # 找出最大的文件数量，确定需要多少个批次
        max_files_per_name = max(len(files) for files in different_aspect_groups.values()) if different_aspect_groups else 0

        if max_files_per_name == 0:
            return batches

        # 为每个批次创建文件列表
        for batch_index in range(max_files_per_name):
            batch_files = []

            for clean_name, files in different_aspect_groups.items():
                if batch_index < len(files):
                    # 当前批次包含这个clean_name的第batch_index个文件
                    batch_files.append(files[batch_index])

            if batch_files:
                batches.append(batch_files)

        # 记录分批信息
        self.log.emit(f"📦 分批策略:")
        for clean_name, files in different_aspect_groups.items():
            if len(files) > 1:
                self.log.emit(f"   {clean_name}: {len(files)} 个文件将分 {len(files)} 个批次处理")
                for i, file_info in enumerate(files):
                    filename = os.path.basename(file_info['path'])
                    aspect_ratio = file_info['aspect_ratio']
                    self.log.emit(f"     批次{i+1}: {filename} (宽高比: {aspect_ratio:.3f})")
            else:
                filename = os.path.basename(files[0]['path'])
                aspect_ratio = files[0]['aspect_ratio']
                self.log.emit(f"   {clean_name}: 1 个文件 - {filename} (宽高比: {aspect_ratio:.3f})")

        return batches

    def run(self):
        processed_files = []  # 记录已处理的文件，用于错误回滚
        try:
            total_files = len(self.image_files)
            self.statistics['total_files'] = total_files

            self.log.emit(f"🚀 开始优化处理 {total_files} 个图片文件...")
            self.log.emit(f"📁 目标文件夹: {self.output_dir}")
            if self.check_duplicates:
                strategy_text = "覆盖" if self.duplicate_strategy == "overwrite" else "跳过"
                self.log.emit(f"🔍 启用重复检测功能，同名同宽高比策略：{strategy_text}")
            self.log.emit("=" * 60)

            # 规范化输出目录路径
            self.output_dir = normalize_path_for_processing(self.output_dir)

            # 第一步：按原始文件名和宽高比对图片进行分组
            self.log.emit("📊 第一步：分析图片文件，按同名同宽高比和同名不同宽高比分组...")
            groups = self._group_images_by_clean_name_and_aspect_ratio(self.image_files)
            
            # 统计分组信息
            total_same_aspect_ratio = 0
            total_different_aspect_ratio = 0
            total_new_files = 0
            for clean_name, group in groups.items():
                same_count = len(group['same_aspect_ratio'])
                diff_count = len(group['different_aspect_ratio'])
                new_count = len(group['new_files'])
                total_same_aspect_ratio += same_count
                total_different_aspect_ratio += diff_count
                total_new_files += new_count

                if same_count > 0 or diff_count > 0 or new_count > 0:
                    self.log.emit(f"📋 {clean_name}: 同名同宽高比 {same_count} 个，同名不同宽高比 {diff_count} 个，新文件 {new_count} 个")

            self.log.emit(f"📈 分组完成：同名同宽高比总计 {total_same_aspect_ratio} 个，同名不同宽高比总计 {total_different_aspect_ratio} 个，新文件总计 {total_new_files} 个")
            self.log.emit("=" * 60)

            # 第二步：先处理同名同宽高比的文件（根据策略跳过或覆盖）
            self.log.emit("🔄 第二步：处理同名同宽高比文件...")
            same_aspect_ratio_files = []
            for clean_name, group in groups.items():
                same_aspect_ratio_files.extend(group['same_aspect_ratio'])
            
            if same_aspect_ratio_files:
                if self.duplicate_strategy == "skip":
                    # 跳过策略：直接跳过所有同名同宽高比文件
                    skipped_count = len(same_aspect_ratio_files)
                    self.statistics['skipped_files'] += skipped_count
                    self.statistics['processed_files'] += skipped_count

                    # 记录跳过的文件详细信息
                    for file_info in same_aspect_ratio_files:
                        original_name = os.path.basename(file_info['path'])
                        self.file_details['skipped_files'].append({
                            'original_name': original_name,
                            'reason': '同名同宽高比文件，用户选择跳过策略'
                        })

                    self.log.emit(f"⏭️ 跳过策略：跳过 {skipped_count} 个同名同宽高比文件")
                else:
                    # 覆盖策略：处理同名同宽高比文件
                    batch_success, batch_errors = self._process_batch_with_delay(
                        same_aspect_ratio_files, "同名同宽高比", PROCESSING_DELAY_NORMAL
                    )
                    self.statistics['success_files'] += batch_success
                    self.statistics['error_files'] += batch_errors
                    self.statistics['processed_files'] += len(same_aspect_ratio_files)
                    self.statistics['same_aspect_ratio_overwritten'] += batch_success

                    # 记录覆盖的文件详细信息
                    for file_info in same_aspect_ratio_files:
                        original_name = os.path.basename(file_info['path'])
                        target_name = file_info.get('suggested_filename', original_name)
                        self.file_details['overwritten_files'].append({
                            'original_name': original_name,
                            'target_name': target_name,
                            'reason': '同名同宽高比文件，用户选择覆盖策略'
                        })
            else:
                self.log.emit("✅ 没有同名同宽高比文件需要处理")

            # 更新进度（第二步完成33%）
            self.progress.emit(33)
            self.log.emit("=" * 60)

            # 第三步：处理新文件（没有同名冲突的文件）
            self.log.emit("📄 第三步：处理新文件...")
            new_files = []
            for clean_name, group in groups.items():
                new_files.extend(group['new_files'])

            if new_files:
                batch_success, batch_errors = self._process_batch_with_delay(
                    new_files, "新文件", PROCESSING_DELAY_NORMAL
                )
                self.statistics['success_files'] += batch_success
                self.statistics['error_files'] += batch_errors
                self.statistics['processed_files'] += len(new_files)
                self.statistics['new_files_processed'] += batch_success

                # 记录新文件的详细信息
                for file_info in new_files:
                    original_name = os.path.basename(file_info['path'])
                    target_name = file_info.get('suggested_filename', original_name)
                    self.file_details['new_files'].append({
                        'original_name': original_name,
                        'target_name': target_name
                    })
            else:
                self.log.emit("✅ 没有新文件需要处理")

            # 更新进度（第三步完成66%）
            self.progress.emit(66)
            self.log.emit("=" * 60)

            # 第四步：处理同名不同宽高比的文件（分组处理，每组一个同名不同宽高比图片）
            self.log.emit("📝 第四步：处理同名不同宽高比文件...")

            # 收集所有同名不同宽高比的文件，按clean_name分组
            different_aspect_groups = {}
            for clean_name, group in groups.items():
                if group['different_aspect_ratio']:
                    different_aspect_groups[clean_name] = group['different_aspect_ratio']

            if different_aspect_groups:
                # 将同名不同宽高比的文件分成多个批次，每个批次每个clean_name只包含一个文件
                batches = self._create_different_aspect_ratio_batches(different_aspect_groups)

                self.log.emit(f"📦 将同名不同宽高比文件分为 {len(batches)} 个批次处理，每批次每个同名只包含一个文件")

                total_batch_success = 0
                total_batch_errors = 0

                for batch_index, batch_files in enumerate(batches):
                    self.log.emit(f"🔄 开始处理第 {batch_index + 1}/{len(batches)} 批次...")

                    # 为当前批次的文件分配正确的序号
                    processed_batch_files = []
                    for file_info in batch_files:
                        clean_name = os.path.splitext(file_info['clean_filename'])[0]

                        # 收集图库中所有同名文件的信息（包括现有的和前面批次已处理的）
                        existing_files_info = self._collect_existing_files_info(clean_name, self.output_dir)

                        # 收集当前批次及后续批次中同名的文件（用于完整的宽高比排序）
                        # 注意：不包括前面批次已处理的文件，因为它们已经在existing_files_info中了
                        remaining_batch_same_name_files = []
                        for batch_idx in range(batch_index, len(batches)):
                            for other_file_info in batches[batch_idx]:
                                other_clean_name = os.path.splitext(other_file_info['clean_filename'])[0]
                                if other_clean_name == clean_name:
                                    remaining_batch_same_name_files.append({
                                        'path': other_file_info['path'],
                                        'aspect_ratio': other_file_info['aspect_ratio'],
                                        'is_new': True
                                    })

                        # 将所有相关文件合并（现有文件 + 当前及后续批次的同名文件）
                        all_files_info = existing_files_info.copy()
                        all_files_info.extend(remaining_batch_same_name_files)

                        # 按宽高比倒序排序（宽高比大的在前）
                        all_files_info.sort(key=lambda x: x['aspect_ratio'], reverse=True)

                        # 为当前文件分配正确的序号
                        for i, sorted_file_info in enumerate(all_files_info):
                            if sorted_file_info.get('is_new', False) and sorted_file_info['path'] == file_info['path']:
                                if i == 0:
                                    # 宽高比最大的使用基础文件名
                                    file_info['suggested_filename'] = file_info['clean_filename']
                                else:
                                    # 其他的使用对应序号
                                    name, ext = os.path.splitext(file_info['clean_filename'])
                                    file_info['suggested_filename'] = f"{name}_{i + 1}{ext}"
                                break

                        processed_batch_files.append(file_info)

                        # 记录排序信息
                        filename = os.path.basename(file_info['path'])
                        aspect_ratio = file_info['aspect_ratio']
                        suggested = file_info['suggested_filename']
                        position = next(i for i, x in enumerate(all_files_info) if x.get('is_new', False) and x['path'] == file_info['path']) + 1
                        self.log.emit(f"   批次{batch_index + 1} - {clean_name}: 位置{position}. {filename} (宽高比: {aspect_ratio:.3f}) → {suggested}")

                    # 处理当前批次
                    if processed_batch_files:
                        # 添加延迟确保前面的文件操作完全完成
                        if batch_index > 0:
                            self.log.emit("⏳ 等待前一批次文件系统操作完成...")
                            time.sleep(PROCESSING_DELAY_BATCH)  # 批次间延迟

                        batch_success, batch_errors = self._process_batch_with_delay(
                            processed_batch_files, f"同名不同宽高比-批次{batch_index + 1}", PROCESSING_DELAY_LONG
                        )
                        total_batch_success += batch_success
                        total_batch_errors += batch_errors

                        self.log.emit(f"✅ 第 {batch_index + 1} 批次完成: 成功 {batch_success} 个，失败 {batch_errors} 个")

                # 更新总体统计
                self.statistics['success_files'] += total_batch_success
                self.statistics['error_files'] += total_batch_errors
                self.statistics['processed_files'] += sum(len(batch) for batch in batches)
                self.statistics['different_aspect_ratio_renamed'] += total_batch_success

                # 记录重命名文件的详细信息
                for batch_files in batches:
                    for file_info in batch_files:
                        original_name = os.path.basename(file_info['path'])
                        target_name = file_info.get('suggested_filename', original_name)
                        if original_name != target_name:
                            self.file_details['renamed_files'].append({
                                'original_name': original_name,
                                'target_name': target_name,
                                'reason': '同名不同宽高比，按宽高比倒序重命名'
                            })

                self.log.emit(f"🎯 同名不同宽高比文件处理完成: 总成功 {total_batch_success} 个，总失败 {total_batch_errors} 个")
            else:
                self.log.emit("✅ 没有同名不同宽高比文件需要处理")

            # 更新进度到100%
            self.progress.emit(100)
            self.log.emit("=" * 60)

            # 输出最终统计
            self.log.emit(f"🎉 拖拽处理完成！")
            self.log.emit(f"📊 处理统计：")
            self.log.emit(f"   总文件数: {self.statistics['total_files']}")
            self.log.emit(f"   处理成功: {self.statistics['success_files']}")
            self.log.emit(f"   跳过文件: {self.statistics['skipped_files']}")
            self.log.emit(f"   处理失败: {self.statistics['error_files']}")
            if self.statistics['new_files_processed'] > 0:
                self.log.emit(f"   新文件处理: {self.statistics['new_files_processed']}")
            if self.statistics['same_aspect_ratio_overwritten'] > 0:
                self.log.emit(f"   同名同宽高比覆盖: {self.statistics['same_aspect_ratio_overwritten']}")
            if self.statistics['different_aspect_ratio_renamed'] > 0:
                self.log.emit(f"   同名不同宽高比重命名: {self.statistics['different_aspect_ratio_renamed']}")
            self.log.emit("=" * 60)

            if self.statistics['success_files'] > 0:
                message = f"成功处理 {self.statistics['success_files']} 个图片文件！"
                if self.statistics['skipped_files'] > 0:
                    message += f"（跳过 {self.statistics['skipped_files']} 个文件）"
                self.finished.emit(True, message, self.statistics['success_files'], self.statistics, self.file_details)
            else:
                if self.statistics['skipped_files'] > 0:
                    self.finished.emit(True, f"跳过了 {self.statistics['skipped_files']} 个文件，没有新文件需要处理", 0, self.statistics, self.file_details)
                else:
                    self.finished.emit(False, "没有文件处理成功", 0, self.statistics, self.file_details)

        except Exception as e:
            self.log.emit(f"❌ 拖拽处理过程中发生严重错误: {str(e)}")
            import traceback
            self.log.emit(f"🔍 详细错误信息: {traceback.format_exc()}")

            # 如果发生严重错误，记录已处理的文件信息
            if processed_files:
                self.log.emit(f"已成功处理 {len(processed_files)} 个文件")

            self.finished.emit(False, f"处理失败: {str(e)}", 0, self.statistics, self.file_details)


class IndexLibraryWorker(QThread):
    """
    图库索引工作线程
    负责图库索引的建立、维护和查询
    """
    progress = pyqtSignal(int)  # 进度信号 (0-100)
    log = pyqtSignal(str)       # 日志信号
    status = pyqtSignal(str)    # 状态信号
    finished = pyqtSignal(bool, str, int)  # 完成信号 (success, message, indexed_count)
    index_status = pyqtSignal(bool, int, str)  # 索引状态信号 (is_indexed, count, status_text)

    def __init__(self, library_path, operation_type='build_index', fast_mode=False):
        super().__init__()
        self.library_path = library_path
        self.operation_type = operation_type  # 'build_index', 'check_status', 'update_index'
        self.fast_mode = fast_mode
        self._stop_requested = False
        self.indexer = None

    def request_stop(self):
        """请求停止索引操作"""
        self._stop_requested = True
        if self.indexer:
            self.indexer.stop_indexing()
        self.log.emit("🛑 收到停止请求，正在安全停止索引...")

    def run(self):
        """线程运行方法"""
        try:
            if not HAS_IMAGE_INDEXER:
                self.finished.emit(False, "图库索引器模块未找到，无法执行索引操作", 0)
                return

            # 创建索引器实例
            self.indexer = ImageIndexerDuckDB(fast_mode=self.fast_mode)

            # 连接索引器信号
            self.indexer.progress_signal.connect(self.progress.emit)
            self.indexer.status_signal.connect(self.status.emit)
            self.indexer.error_signal.connect(self.log.emit)

            if self.operation_type == 'build_index':
                self._build_index()
            elif self.operation_type == 'check_status':
                self._check_status()
            elif self.operation_type == 'update_index':
                self._update_index()
            else:
                self.finished.emit(False, f"未知的操作类型: {self.operation_type}", 0)

        except Exception as e:
            error_msg = f"索引操作失败: {str(e)}"
            self.log.emit(error_msg)
            self.finished.emit(False, error_msg, 0)

    def _build_index(self):
        """建立图库索引"""
        try:
            self.log.emit("🚀 开始建立图库索引...")
            self.log.emit(f"📁 图库路径: {self.library_path}")

            mode_text = "快速模式" if self.fast_mode else "完整模式"
            self.log.emit(f"🔧 索引模式: {mode_text}")

            # 执行索引建立
            success, message, indexed_count = self.indexer.scan_library(
                self.library_path,
                fast_mode=self.fast_mode
            )

            if success:
                self.log.emit(f"✅ 索引建立完成！共索引 {indexed_count} 个图片文件")
                self.index_status.emit(True, indexed_count, f"已索引 {indexed_count} 个文件")
                self.finished.emit(True, f"成功建立图库索引，共索引 {indexed_count} 个文件", indexed_count)
            else:
                self.log.emit(f"❌ 索引建立失败: {message}")
                self.finished.emit(False, f"索引建立失败: {message}", 0)

        except Exception as e:
            error_msg = f"建立索引时出错: {str(e)}"
            self.log.emit(error_msg)
            self.finished.emit(False, error_msg, 0)

    def _check_status(self):
        """检查索引状态"""
        try:
            self.log.emit("🔍 正在检查图库索引状态...")

            # 检查是否已建立索引
            is_indexed = self.indexer.is_indexed(self.library_path)

            if is_indexed:
                # 获取索引统计信息
                if self.indexer.set_library_path(self.library_path):
                    try:
                        result = self.indexer.db.execute("SELECT COUNT(*) FROM image_files").fetchone()
                        count = result[0] if result else 0

                        self.log.emit(f"✅ 图库已建立索引，共有 {count} 个文件")
                        self.index_status.emit(True, count, f"已索引 {count} 个文件")
                        self.finished.emit(True, f"图库已建立索引，共有 {count} 个文件", count)
                    except Exception as db_error:
                        self.log.emit(f"⚠️ 读取索引统计信息失败: {str(db_error)}")
                        self.index_status.emit(True, 0, "索引存在但统计失败")
                        self.finished.emit(True, "图库已建立索引，但无法获取统计信息", 0)
                else:
                    self.log.emit("⚠️ 索引文件存在但无法连接")
                    self.index_status.emit(False, 0, "索引文件损坏")
                    self.finished.emit(False, "索引文件存在但无法连接，可能已损坏", 0)
            else:
                self.log.emit("📝 图库尚未建立索引")
                self.index_status.emit(False, 0, "未建立索引")
                self.finished.emit(True, "图库尚未建立索引", 0)

        except Exception as e:
            error_msg = f"检查索引状态时出错: {str(e)}"
            self.log.emit(error_msg)
            self.index_status.emit(False, 0, "状态检查失败")
            self.finished.emit(False, error_msg, 0)

    def _update_index(self):
        """更新图库索引（增量更新）"""
        try:
            self.log.emit("🔄 开始更新图库索引...")

            # 先检查是否已有索引
            if not self.indexer.is_indexed(self.library_path):
                self.log.emit("📝 图库尚未建立索引，将执行完整索引建立...")
                self._build_index()
                return

            # 执行增量更新（重新扫描整个库）
            # 注意：当前的ImageIndexerDuckDB实现会清空现有数据重新索引
            # 这实际上是完整重建，但保持了接口的一致性
            success, message, indexed_count = self.indexer.scan_library(
                self.library_path,
                fast_mode=self.fast_mode
            )

            if success:
                self.log.emit(f"✅ 索引更新完成！共索引 {indexed_count} 个图片文件")
                self.index_status.emit(True, indexed_count, f"已索引 {indexed_count} 个文件")
                self.finished.emit(True, f"成功更新图库索引，共索引 {indexed_count} 个文件", indexed_count)
            else:
                self.log.emit(f"❌ 索引更新失败: {message}")
                self.finished.emit(False, f"索引更新失败: {message}", 0)

        except Exception as e:
            error_msg = f"更新索引时出错: {str(e)}"
            self.log.emit(error_msg)
            self.finished.emit(False, error_msg, 0)


class HighPerformanceImageProcessorWorker(QThread):
    """高性能图片处理工作线程 - 专门优化大批量处理"""
    progress = pyqtSignal(int)
    log = pyqtSignal(str)
    finished = pyqtSignal(bool, str)
    memory_status = pyqtSignal(str)  # 内存状态信号

    def __init__(self, folder_path, operation_type):
        super().__init__()
        self.folder_path = folder_path
        self.operation_type = operation_type  # 'rotate_images', 'move_images', 或 'clean_and_move'
        self._stop_requested = False
        self._mover = UltraHighPerformanceImageMover()
        self._cleaner = CleanAndMoveProcessor()

    def request_stop(self):
        """请求停止处理"""
        self._stop_requested = True
        self._mover.request_stop()
        self._cleaner.request_stop()
        self.log.emit("🛑 收到停止请求，正在安全停止...")

    def run(self):
        """
        线程运行方法 - 增强版本，修复完成时崩溃问题
        """
        rotated_count = 0
        start_time = time.time()

        try:
            self.log.emit(f"🚀 开始执行 {self.operation_type} 操作...")
            self.log.emit(f"📁 目标文件夹: {self.folder_path}")

            # 报告初始内存状态
            try:
                memory_info = SystemResourceMonitor.get_memory_usage()
                if 'percent' in memory_info:
                    self.memory_status.emit(f"初始内存使用: {memory_info['percent']:.1f}%")
            except Exception as memory_error:
                self.log.emit(f"⚠️ 内存状态检查失败: {str(memory_error)}")

            if self.operation_type == 'rotate_images':
                try:
                    rotated_count = ImageOrientationProcessor.process_folder(
                        self.folder_path,
                        progress_callback=self._safe_progress_emit,
                        log_callback=self._safe_log_emit
                    )

                    end_time = time.time()
                    total_time = end_time - start_time

                    # 安全的完成信号发送
                    if not self._stop_requested:
                        success_msg = f"🎉 图片方向处理完成！旋转了 {rotated_count} 个文件，耗时 {total_time:.2f} 秒"
                        self._safe_finished_emit(True, success_msg)
                    else:
                        interrupt_msg = f"⏹️ 操作被用户中断，已旋转 {rotated_count} 个文件"
                        self._safe_finished_emit(False, interrupt_msg)

                except Exception as rotate_error:
                    error_msg = f"❌ 图片旋转过程中出错: {str(rotate_error)}"
                    self._safe_log_emit(error_msg)
                    self._safe_finished_emit(False, error_msg)

            elif self.operation_type == 'move_images':
                try:
                    moved_count = self._mover.process_folder_move_images(
                        self.folder_path,
                        progress_callback=self._safe_progress_emit,
                        log_callback=self._safe_log_emit
                    )

                    end_time = time.time()
                    total_time = end_time - start_time

                    # 安全的完成信号发送
                    if not self._stop_requested:
                        success_msg = f"🎉 图片移动处理完成！移动了 {moved_count} 个文件，耗时 {total_time:.2f} 秒"
                        self._safe_finished_emit(True, success_msg)
                    else:
                        interrupt_msg = f"⏹️ 操作被用户中断，已移动 {moved_count} 个文件"
                        self._safe_finished_emit(False, interrupt_msg)

                except Exception as move_error:
                    error_msg = f"❌ 图片移动过程中出错: {str(move_error)}"
                    self._safe_log_emit(error_msg)
                    self._safe_finished_emit(False, error_msg)

            elif self.operation_type == 'clean_and_move':
                try:
                    results = self._cleaner.process_library_clean_and_move(
                        self.folder_path,
                        progress_callback=self._safe_progress_emit,
                        log_callback=self._safe_log_emit
                    )

                    end_time = time.time()
                    total_time = end_time - start_time

                    # 安全的完成信号发送
                    if not self._stop_requested:
                        success_msg = (f"🎉 清洗移动处理完成！处理了 {results['processed']} 个文件，"
                                     f"优化了 {results['optimized']} 个文件名，"
                                     f"移动了 {results['moved']} 个文件，耗时 {total_time:.2f} 秒")
                        self._safe_finished_emit(True, success_msg)
                    else:
                        interrupt_msg = (f"⏹️ 操作被用户中断，已处理 {results['processed']} 个文件，"
                                       f"优化了 {results['optimized']} 个文件名")
                        self._safe_finished_emit(False, interrupt_msg)

                except Exception as clean_error:
                    error_msg = f"❌ 清洗移动过程中出错: {str(clean_error)}"
                    self._safe_log_emit(error_msg)
                    self._safe_finished_emit(False, error_msg)



            else:
                error_msg = f"❌ 未知的操作类型: {self.operation_type}"
                self._safe_finished_emit(False, error_msg)

        except Exception as e:
            error_msg = f"❌ 线程运行过程中发生严重错误: {str(e)}"
            self._safe_log_emit(error_msg)
            logging.error(error_msg, exc_info=True)  # 记录完整的错误堆栈
            self._safe_finished_emit(False, error_msg)
        finally:
            # 确保在任何情况下都进行安全的资源清理
            self._safe_cleanup()

    def _safe_progress_emit(self, progress):
        """安全的进度信号发送"""
        try:
            if not self.isFinished():
                # 确保进度值在有效范围内
                safe_progress = min(100, max(0, int(progress)))
                self.progress.emit(safe_progress)
        except Exception as e:
            # 进度更新失败不应该影响主要处理流程
            pass

    def _safe_log_emit(self, message):
        """安全的日志信号发送"""
        try:
            if not self.isFinished():
                self.log.emit(str(message))
        except Exception as e:
            # 日志发送失败不应该影响主要处理流程
            pass

    def _safe_finished_emit(self, success, message):
        """安全的完成信号发送"""
        try:
            if not self.isFinished():
                self.finished.emit(bool(success), str(message))
        except Exception as e:
            # 如果正常的完成信号发送失败，尝试发送错误信号
            try:
                self.finished.emit(False, f"信号发送失败: {str(e)}")
            except:
                pass

    def _safe_cleanup(self):
        """安全的资源清理"""
        try:
            # 内存清理
            gc.collect()

            # 报告最终内存状态
            try:
                memory_info = SystemResourceMonitor.get_memory_usage()
                if 'percent' in memory_info:
                    self.memory_status.emit(f"最终内存使用: {memory_info['percent']:.1f}%")
            except Exception as memory_error:
                self._safe_log_emit(f"⚠️ 最终内存状态检查失败: {str(memory_error)}")

            self._safe_log_emit("🧹 已清理内存资源")

        except Exception as cleanup_error:
            try:
                self._safe_log_emit(f"⚠️ 资源清理时出现问题: {str(cleanup_error)}")
            except:
                # 如果连日志都发送不了，就静默处理
                pass


# 为了兼容性，保留原来的类名
class ImageProcessorWorker(HighPerformanceImageProcessorWorker):
    """兼容性类，继承高性能处理器"""
    pass


# ---------------------------
# UI层
# ---------------------------
class ImageProcessorApp(QMainWindow):
    def __init__(self):
        super().__init__()
        # 设置主窗口实例引用
        set_main_window_instance(self)
        # 初始化SupabaseHelper引用
        self.supabase_helper = None
        self.init_ui()
        # 更新窗口标题以显示登录用户
        self.update_window_title()
        self.worker = None
        self.drag_drop_worker = None  # 拖拽处理工作线程
        self.index_worker = None     # 图库索引工作线程

        # 加载保存的图库路径配置
        self.load_library_path_config()

    def update_window_title(self):
        """更新窗口标题，显示登录用户名"""
        title = f"{ROBOT_SMART_NAME} v{ROBOT_CURRENT_VERSION}"

        # 添加用户信息
        if self.supabase_helper and self.supabase_helper.is_connected():
            try:
                email = self.supabase_helper.get_user_email()
                if email:
                    title += f" - 用户: {email}"
            except Exception as e:
                logging.error(f"获取用户信息时出错: {e}")

        self.setWindowTitle(title)

    def check_version(self):
        """检查应用版本并更新窗口标题"""
        # 设置默认标题
        app_title = ROBOT_SMART_NAME

        if self.supabase_helper and self.supabase_helper.is_connected():
            try:
                # 尝试获取配置，先检查用户是否已登录
                if self.supabase_helper.is_authenticated():
                    log.info("用户已登录，使用已认证客户端获取配置")
                else:
                    log.info("用户未登录，将尝试使用匿名客户端获取配置")

                config = self.supabase_helper.fetch_config(IMAGE_FINDER_BOT_TAG)
                if not config:
                    log.error("未能获取云端配置信息")
                    # 即使获取失败也设置默认标题
                    self.setWindowTitle(app_title)

                    # 如果用户已登录但仍然获取失败，可能是权限问题
                    if self.supabase_helper.is_authenticated():
                        QMessageBox.warning(
                            self,
                            "配置获取警告",
                            "已登录但无法获取云端配置，可能是权限问题，将使用默认配置"
                        )
                    else:
                        QMessageBox.warning(
                            self,
                            "配置获取警告",
                            "无法获取云端配置，将使用默认配置"
                        )
                    return  # 使用默认配置继续运行，而不是退出

                # 成功获取配置
                latest_version = config.get('image_tool_ver')
                if not latest_version:
                    log.warning("云端配置中没有版本信息，将使用当前版本")
                    latest_version = ROBOT_CURRENT_VERSION

                log.info(f"当前应用版本: {ROBOT_CURRENT_VERSION}, 云端版本: {latest_version}")
                if ROBOT_CURRENT_VERSION != latest_version:
                    msg = QMessageBox()
                    msg.setIcon(QMessageBox.Icon.Critical)  # 使用错误图标
                    msg.setText("版本不匹配")
                    msg.setInformativeText(f"当前版本 {ROBOT_CURRENT_VERSION} 与云端版本 {latest_version} 不匹配，请升级到最新版本后再使用")
                    msg.setWindowTitle("版本错误")
                    msg.setStandardButtons(QMessageBox.StandardButton.Ok)
                    msg.exec()
                    # 强制退出应用
                    log.error(f"版本不匹配，强制退出应用。当前版本: {ROBOT_CURRENT_VERSION}, 云端版本: {latest_version}")
                    sys.exit(1)

                # 更新应用标题
                latest_title = config.get('app_title')
                if latest_title:
                    app_title = latest_title + '-v' + latest_version

                # 添加用户邮箱到标题
                if self.supabase_helper.is_authenticated():
                    user_email = self.supabase_helper.get_user_email()
                    if user_email:
                        app_title = f"{app_title} - {user_email}"
                        log.info(f"已将用户邮箱 {user_email} 添加到窗口标题")

                self.setWindowTitle(app_title)

            except Exception as e:
                log.error(f"云端配置检查失败: {type(e).__name__}: {str(e)}")
                # 异常情况下也设置默认标题
                self.setWindowTitle(ROBOT_SMART_NAME)
                QMessageBox.warning(
                    self,
                    "配置检查警告",
                    f"获取云端配置时出现异常，将使用默认配置"
                )
                # 不强制退出，允许用户继续使用
        else:
            # 如果没有连接到Supabase，使用默认标题
            log.warning("Supabase未连接，使用默认标题")
            self.setWindowTitle(app_title)

    def closeEvent(self, event):
        """处理窗口关闭事件，优雅退出程序"""
        _ = event  # 标记参数已使用
        try:
            # 登出Supabase
            try:
                if self.supabase_helper and self.supabase_helper.is_connected():
                    self.supabase_helper.logout()
                    logging.info("已成功登出Supabase")
            except Exception as e:
                logging.error(f"登出Supabase时出错: {e}")

            # 停止所有正在运行的任务
            if hasattr(self, 'worker') and self.worker and self.worker.isRunning():
                self.worker.terminate()
                self.worker.wait()  # 等待线程结束

            if hasattr(self, 'drag_drop_worker') and self.drag_drop_worker and self.drag_drop_worker.isRunning():
                self.drag_drop_worker.terminate()
                self.drag_drop_worker.wait()  # 等待拖拽处理线程结束

            if hasattr(self, 'drag_drop_delete_worker') and self.drag_drop_delete_worker and self.drag_drop_delete_worker.isRunning():
                self.drag_drop_delete_worker.terminate()
                self.drag_drop_delete_worker.wait()  # 等待删除处理线程结束

            if hasattr(self, 'index_worker') and self.index_worker and self.index_worker.isRunning():
                self.index_worker.terminate()
                self.index_worker.wait()  # 等待索引工作线程结束

            # 确保所有日志都被写入
            for handler in log.handlers[:]:
                handler.close()
                log.removeHandler(handler)

            # 退出应用
            QApplication.quit()
        except Exception as e:
            log.error(f"关闭程序时发生错误: {e}")
            sys.exit(1)  # 如果优雅退出失败，强制退出

    def init_ui(self):
        """初始化UI元素"""
        # 设置窗口标题和大小
        self.setWindowTitle('图片处理工具')
        self.setGeometry(300, 300, 800, 600)

        # 创建顶层容器
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)

        # ----- 顶部说明和操作区域 -----
        top_group = QGroupBox("操作区域")
        top_layout = QVBoxLayout()

        # 选择图库文件夹（统一文件夹）
        folder_layout = QHBoxLayout()
        folder_label = QLabel("图库文件夹:")
        self.folder_path_edit = QLineEdit()
        self.folder_path_edit.setReadOnly(True)
        self.folder_path_edit.setPlaceholderText("选择图库文件夹 - 既可批量处理，也可作为拖拽目标")
        browse_button = QPushButton("选择图库文件夹")
        browse_button.clicked.connect(self.select_library_folder)

        folder_layout.addWidget(folder_label)
        folder_layout.addWidget(self.folder_path_edit)
        folder_layout.addWidget(browse_button)

        top_layout.addLayout(folder_layout)

        # 功能按钮区域
        button_layout = QHBoxLayout()

        # 清洗移动按钮
        self.clean_and_move_button = QPushButton("1. 清洗移动")
        self.clean_and_move_button.setMinimumHeight(50)
        self.clean_and_move_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.clean_and_move_button.clicked.connect(self.clean_and_move_images)
        self.clean_and_move_button.setEnabled(False)
        self.clean_and_move_button.setToolTip(
            "优化图片文件名并智能分类（只有多个同名时才创建文件夹）\n"
            "• 扫描图库中的所有图片文件\n"
            "• 优化文件名：将空格替换为'-'\n"
            "• 智能分类：2个以上同样中文名才创建文件夹\n"
            "• 唯一中文名和无中文名的图片保留在主目录"
        )

        self.rotate_images_button = QPushButton("2. 图片横向放置")
        self.rotate_images_button.setMinimumHeight(50)
        self.rotate_images_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.rotate_images_button.clicked.connect(self.rotate_images)
        self.rotate_images_button.setEnabled(False)
        self.rotate_images_button.setToolTip(
            "检测竖向图片并旋转90度，使所有图片都横向显示\n"
            "• 扫描图库中的所有图片文件\n"
            "• 自动识别竖向图片（高度>宽度）\n"
            "• 将竖向图片顺时针旋转90度变为横向"
        )

        # 图库索引管理按钮
        self.index_library_button = QPushButton("3. 图库索引管理")
        self.index_library_button.setMinimumHeight(50)
        self.index_library_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.index_library_button.clicked.connect(self.manage_library_index)
        self.index_library_button.setEnabled(False)
        self.index_library_button.setToolTip(
            "建立和维护图片库索引，支持快速查询\n"
            "• 建立图片文件索引（文件名、尺寸、创建时间等）\n"
            "• 支持快速模式和完整模式索引\n"
            "• 提供索引状态检查和增量更新功能"
        )

        # 添加停止按钮
        self.stop_button = QPushButton("停止处理")
        self.stop_button.setMinimumHeight(50)
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.stop_button.clicked.connect(self.stop_processing)
        self.stop_button.setEnabled(False)

        button_layout.addWidget(self.clean_and_move_button)
        button_layout.addWidget(self.rotate_images_button)
        button_layout.addWidget(self.index_library_button)
        button_layout.addWidget(self.stop_button)

        top_layout.addLayout(button_layout)

        # 拖拽操作目标设置
        drag_operation_group = QGroupBox("拖拽操作目标")
        drag_operation_layout = QHBoxLayout()

        from PyQt6.QtWidgets import QRadioButton, QButtonGroup
        self.drag_operation_button_group = QButtonGroup()

        self.add_to_library_radio = QRadioButton("添加到图库")
        self.add_to_library_radio.setChecked(True)  # 默认选择添加到图库
        self.add_to_library_radio.setToolTip("将拖拽的图片文件添加到图库中")

        self.delete_from_library_radio = QRadioButton("从图库删除")
        self.delete_from_library_radio.setToolTip("从图库中删除拖拽的图片文件或文件夹")

        self.drag_operation_button_group.addButton(self.add_to_library_radio, 0)
        self.drag_operation_button_group.addButton(self.delete_from_library_radio, 1)

        # 连接单选按钮变化事件
        self.add_to_library_radio.toggled.connect(self.update_drag_drop_area_text)
        self.delete_from_library_radio.toggled.connect(self.update_drag_drop_area_text)

        drag_operation_layout.addWidget(QLabel("拖拽操作:"))
        drag_operation_layout.addWidget(self.add_to_library_radio)
        drag_operation_layout.addWidget(self.delete_from_library_radio)
        drag_operation_layout.addStretch()  # 添加弹性空间

        drag_operation_group.setLayout(drag_operation_layout)
        top_layout.addWidget(drag_operation_group)

        # 同名图片处理策略设置
        strategy_group = QGroupBox("同名图片处理策略")
        strategy_layout = QHBoxLayout()

        self.strategy_button_group = QButtonGroup()

        self.skip_radio = QRadioButton("跳过")
        self.skip_radio.setChecked(True)  # 默认选择跳过
        self.skip_radio.setToolTip("跳过同名同宽高比的图片，不进行处理")

        self.overwrite_radio = QRadioButton("覆盖")
        self.overwrite_radio.setToolTip("覆盖同名同宽高比的图片")

        self.strategy_button_group.addButton(self.skip_radio, 0)
        self.strategy_button_group.addButton(self.overwrite_radio, 1)

        strategy_layout.addWidget(QLabel("同名同宽高比图片:"))
        strategy_layout.addWidget(self.skip_radio)
        strategy_layout.addWidget(self.overwrite_radio)
        strategy_layout.addStretch()  # 添加弹性空间

        strategy_group.setLayout(strategy_layout)
        top_layout.addWidget(strategy_group)

        # 添加图库索引状态显示区域
        index_status_group = QGroupBox("图库索引状态")
        index_status_layout = QHBoxLayout()

        # 索引状态标签
        self.index_status_label = QLabel("索引状态: 未检查")
        self.index_status_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #666666;
                padding: 5px;
                border: 1px solid #ddd;
                border-radius: 3px;
                background-color: #f9f9f9;
            }
        """)

        # 索引统计标签
        self.index_count_label = QLabel("图片数量: --")
        self.index_count_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #666666;
                padding: 5px;
                border: 1px solid #ddd;
                border-radius: 3px;
                background-color: #f9f9f9;
            }
        """)

        index_status_layout.addWidget(self.index_status_label)
        index_status_layout.addWidget(self.index_count_label)
        index_status_layout.addStretch()  # 添加弹性空间

        index_status_group.setLayout(index_status_layout)
        top_layout.addWidget(index_status_group)

        # 添加拖拽区域（红框区域）
        drag_drop_group = QGroupBox("功能说明")
        drag_drop_layout = QVBoxLayout()

        # 创建拖拽区域
        self.drag_drop_area = DragDropArea()
        self.drag_drop_area.files_dropped.connect(self.handle_dropped_files)

        # 添加红色边框样式（对应截图中的红框）
        self.drag_drop_area.setStyleSheet("""
            QLabel {
                border: 2px dashed #ff4444;
                border-radius: 8px;
                background-color: #fff5f5;
                color: #666666;
                font-size: 14px;
                padding: 20px;
            }
            QLabel:hover {
                border-color: #4CAF50;
                background-color: #f0f8f0;
                color: #4CAF50;
            }
        """)

        # 设置初始拖拽区域文本
        self.update_drag_drop_area_text()

        drag_drop_layout.addWidget(self.drag_drop_area)

        # 添加详细功能说明
        detailed_explanation = QLabel("""
功能说明：
1. 清洗移动：优化图片文件名并智能分类（只有多个同名时才创建文件夹）
   • 扫描图库中的所有图片文件
   • 优化文件名：将空格替换为'-'，去除多余字符
   • 智能分类：2个以上同样中文名才创建文件夹，唯一中文名保留在主目录
2. 图片横向放置：检测竖向图片并旋转90度，使所有图片都横向显示
3. 图库索引管理：建立和维护图片库索引，支持快速查询
   • 建立图片文件索引（文件名、尺寸、创建时间等）
   • 支持快速模式和完整模式索引
   • 提供索引状态检查和增量更新功能
4. 拖拽功能：直接拖拽图片文件或文件夹到上方区域，自动执行优化+旋转+复制+索引更新
   • 支持单个或多个图片文件拖拽
   • 支持文件夹拖拽，自动提取文件夹中的所有图片
   • 自动优化文件名（空格替换为'-'）
   • 可选择同名图片处理策略：跳过或覆盖
   • 支持基于中文名字的智能分类和文件夹创建
        """)
        detailed_explanation.setWordWrap(True)
        detailed_explanation.setStyleSheet("color: #666; font-size: 12px; margin: 10px;")
        drag_drop_layout.addWidget(detailed_explanation)

        drag_drop_group.setLayout(drag_drop_layout)
        top_layout.addWidget(drag_drop_group)

        top_group.setLayout(top_layout)

        # ----- 日志和状态区域 -----
        log_group = QGroupBox("处理日志")
        log_layout = QVBoxLayout()

        # 进度条
        progress_layout = QHBoxLayout()
        progress_label = QLabel("进度:")
        self.progress_bar = QProgressBar()
        progress_layout.addWidget(progress_label)
        progress_layout.addWidget(self.progress_bar)
        log_layout.addLayout(progress_layout)

        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)

        log_group.setLayout(log_layout)

        # ----- 组装主界面 -----
        main_layout.addWidget(top_group)
        main_layout.addWidget(log_group)

        self.setCentralWidget(main_widget)

        # 更新日志
        self.update_log("程序已启动，请选择图库文件夹")
        self.update_log("功能：1.清洗移动 2.图片横向放置 3.图库索引管理 4.拖拽图片文件或文件夹到红框区域进行处理")
        self.update_log("拖拽功能支持：单个/多个图片文件、包含图片的文件夹，自动优化文件名，支持中文名字智能分类，可选择同名图片处理策略")

    def select_library_folder(self):
        """选择图库文件夹（统一文件夹）"""
        # 获取上次选择的路径作为默认路径
        last_path = self.folder_path_edit.text() if self.folder_path_edit.text() else os.path.expanduser('~')

        folder = QFileDialog.getExistingDirectory(self, '选择图库文件夹', last_path)
        if folder:
            self.folder_path_edit.setText(folder)
            self.log_text.clear()
            self.progress_bar.setValue(0)
            self.update_log(f"已选择图库文件夹: {folder}")

            # 启用功能按钮
            self.clean_and_move_button.setEnabled(True)
            self.rotate_images_button.setEnabled(True)
            self.index_library_button.setEnabled(True)

            # 更新按钮状态提示
            self.update_log("✅ 所有功能按钮已启用，可以开始处理图片")

            # 更新拖拽区域的提示文本
            self.update_drag_drop_area_text()

            # 检查图库索引状态
            self.check_library_index_status()

            # 保存图库路径配置
            self.save_library_path_config(folder)

    def update_drag_drop_area_text(self):
        """更新拖拽区域的文本"""
        if hasattr(self, 'drag_drop_area'):
            library_folder = self.folder_path_edit.text()

            # 检查是否选择了删除操作
            is_delete_mode = hasattr(self, 'delete_from_library_radio') and self.delete_from_library_radio.isChecked()

            if library_folder:
                if is_delete_mode:
                    # 删除模式的提示文本
                    self.drag_drop_area.setText(f"""
🗑️ 拖拽图片或文件夹到此区域进行删除

图库文件夹: {library_folder}
⚠️ 警告：将删除拖拽的图片文件或文件夹
只能删除图库内的文件，会同时更新索引

支持格式：JPG, PNG, BMP, GIF, TIFF, WEBP
                    """)
                    # 设置删除模式的红色警告样式
                    self.drag_drop_area.setStyleSheet("""
                        QLabel {
                            border: 2px dashed #ff4444;
                            border-radius: 8px;
                            background-color: #fff5f5;
                            color: #d32f2f;
                            font-size: 14px;
                            padding: 20px;
                            font-weight: bold;
                        }
                        QLabel:hover {
                            border-color: #d32f2f;
                            background-color: #ffebee;
                            color: #b71c1c;
                        }
                    """)
                else:
                    # 添加模式的提示文本
                    self.drag_drop_area.setText(f"""
🖼️ 拖拽图片或文件夹到此区域

图库文件夹: {library_folder}
支持单个或多个图片文件，以及包含图片的文件夹
自动执行：横向放置 + 复制到图库文件夹

支持格式：JPG, PNG, BMP, GIF, TIFF, WEBP
                    """)
                    # 设置添加模式的正常样式
                    self.drag_drop_area.setStyleSheet("""
                        QLabel {
                            border: 2px dashed #ff4444;
                            border-radius: 8px;
                            background-color: #fff5f5;
                            color: #666666;
                            font-size: 14px;
                            padding: 20px;
                        }
                        QLabel:hover {
                            border-color: #4CAF50;
                            background-color: #f0f8f0;
                            color: #4CAF50;
                        }
                    """)
            else:
                # 未选择图库文件夹的提示
                operation_text = "删除" if is_delete_mode else "添加"
                self.drag_drop_area.setText(f"""
🖼️ 拖拽图片或文件夹到此区域进行{operation_text}

请先选择图库文件夹
支持单个或多个图片文件，以及包含图片的文件夹

支持格式：JPG, PNG, BMP, GIF, TIFF, WEBP
                """)

    def check_library_index_status(self):
        """检查图库索引状态"""
        library_folder = self.folder_path_edit.text()
        if not library_folder or not HAS_IMAGE_INDEXER:
            self.index_status_label.setText("索引状态: 不可用")
            self.index_count_label.setText("图片数量: --")
            return

        # 启动索引状态检查工作线程
        if hasattr(self, 'index_worker') and self.index_worker and self.index_worker.isRunning():
            return  # 如果已有索引任务在运行，不重复检查

        self.index_worker = IndexLibraryWorker(library_folder, 'check_status')
        self.index_worker.index_status.connect(self.update_index_status)
        self.index_worker.finished.connect(self.index_check_finished)
        self.index_worker.start()

    def update_index_status(self, is_indexed: bool, count: int, status_text: str):
        """更新索引状态显示"""
        if is_indexed:
            self.index_status_label.setText(f"索引状态: ✅ 已建立")
            self.index_status_label.setStyleSheet("""
                QLabel {
                    font-size: 12px;
                    color: #4CAF50;
                    padding: 5px;
                    border: 1px solid #4CAF50;
                    border-radius: 3px;
                    background-color: #e8f5e8;
                }
            """)
        else:
            self.index_status_label.setText(f"索引状态: ❌ 未建立")
            self.index_status_label.setStyleSheet("""
                QLabel {
                    font-size: 12px;
                    color: #f44336;
                    padding: 5px;
                    border: 1px solid #f44336;
                    border-radius: 3px;
                    background-color: #ffebee;
                }
            """)

        self.index_count_label.setText(f"图片数量: {count}")

    def index_check_finished(self, success: bool, message: str, count: int):
        """索引状态检查完成"""
        if not success:
            self.update_log(f"索引状态检查: {message}")

    def save_library_path_config(self, library_path):
        """保存图库路径到配置文件"""
        try:
            # 创建配置目录（如果不存在）
            config_dir = os.path.join(os.path.expanduser('~'), '.dachuan-image-tool')
            os.makedirs(config_dir, exist_ok=True)

            # 保存配置
            config = {
                'library_path': library_path,
                'last_updated': time.time()
            }

            config_file = os.path.join(config_dir, 'config.json')
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            self.update_log(f"图库路径已保存到配置文件")
        except Exception as e:
            self.update_log(f"保存图库路径配置失败: {str(e)}")

    def load_library_path_config(self):
        """从配置文件加载图库路径"""
        try:
            config_file = os.path.join(os.path.expanduser('~'), '.dachuan-image-tool', 'config.json')
            if not os.path.exists(config_file):
                return

            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            library_path = config.get('library_path', '')
            if library_path and os.path.exists(library_path):
                self.folder_path_edit.setText(library_path)
                self.update_log(f"已恢复图库文件夹路径: {library_path}")

                # 启用功能按钮
                self.clean_and_move_button.setEnabled(True)
                self.rotate_images_button.setEnabled(True)
                self.index_library_button.setEnabled(True)

                # 更新按钮状态提示
                self.update_log("✅ 已恢复图库配置，所有功能按钮已启用")

                # 更新拖拽区域的提示文本
                self.update_drag_drop_area_text()

                # 检查图库索引状态
                self.check_library_index_status()
            else:
                if library_path:
                    self.update_log(f"保存的图库路径不存在，已清除: {library_path}")
        except Exception as e:
            self.update_log(f"加载图库路径配置失败: {str(e)}")

    def manage_library_index(self):
        """管理图库索引"""
        library_folder = self.folder_path_edit.text()
        if not library_folder:
            QMessageBox.warning(self, "错误", "请先选择图库文件夹")
            return

        if not HAS_IMAGE_INDEXER:
            QMessageBox.warning(self, "错误", "图库索引器模块未找到，无法执行索引操作")
            return

        # 显示索引管理对话框
        self.show_index_management_dialog()

    def show_index_management_dialog(self):
        """显示索引管理对话框"""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QCheckBox

        dialog = QDialog(self)
        dialog.setWindowTitle("图库索引管理")
        dialog.setFixedSize(400, 300)
        dialog.setModal(True)

        layout = QVBoxLayout(dialog)

        # 标题
        title_label = QLabel("图库索引管理")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 当前状态显示
        status_label = QLabel(f"当前图库: {self.folder_path_edit.text()}")
        status_label.setWordWrap(True)
        layout.addWidget(status_label)

        current_status = QLabel()
        current_status.setText(f"索引状态: {self.index_status_label.text().replace('索引状态: ', '')}")
        layout.addWidget(current_status)

        current_count = QLabel()
        current_count.setText(f"图片数量: {self.index_count_label.text().replace('图片数量: ', '')}")
        layout.addWidget(current_count)

        # 快速模式选项
        fast_mode_checkbox = QCheckBox("快速模式（获取图片尺寸，跳过详细EXIF数据，速度更快）")
        fast_mode_checkbox.setChecked(True)  # 默认使用快速模式
        layout.addWidget(fast_mode_checkbox)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 建立/重建索引按钮
        build_button = QPushButton("建立/重建索引")
        build_button.clicked.connect(lambda: self.start_index_operation('build_index', fast_mode_checkbox.isChecked(), dialog))

        # 更新索引按钮
        update_button = QPushButton("更新索引")
        update_button.clicked.connect(lambda: self.start_index_operation('update_index', fast_mode_checkbox.isChecked(), dialog))

        # 取消按钮
        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(dialog.reject)

        button_layout.addWidget(build_button)
        button_layout.addWidget(update_button)
        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)

        dialog.exec()

    def start_index_operation(self, operation_type: str, fast_mode: bool, dialog):
        """启动索引操作"""
        library_folder = self.folder_path_edit.text()

        # 关闭对话框
        dialog.accept()

        # 检查是否有其他任务在运行
        if (hasattr(self, 'worker') and self.worker and self.worker.isRunning()) or \
           (hasattr(self, 'drag_drop_worker') and self.drag_drop_worker and self.drag_drop_worker.isRunning()) or \
           (hasattr(self, 'index_worker') and self.index_worker and self.index_worker.isRunning()):
            QMessageBox.warning(self, "错误", "有其他处理任务正在运行，请稍后再试")
            return

        # 启动索引操作
        operation_names = {
            'build_index': '建立索引',
            'update_index': '更新索引'
        }

        operation_name = operation_names.get(operation_type, operation_type)
        mode_text = "快速模式" if fast_mode else "完整模式"

        self.update_log(f"开始{operation_name}（{mode_text}）...")

        # 禁用相关按钮
        self.clean_and_move_button.setEnabled(False)
        self.rotate_images_button.setEnabled(False)
        self.index_library_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        # 创建并启动索引工作线程
        self.index_worker = IndexLibraryWorker(library_folder, operation_type, fast_mode)

        # 连接信号槽
        self.index_worker.progress.connect(self.update_progress)
        self.index_worker.log.connect(self.update_log)
        self.index_worker.status.connect(self.update_log)
        self.index_worker.index_status.connect(self.update_index_status)
        self.index_worker.finished.connect(self.index_operation_finished)

        # 启动工作线程
        self.index_worker.start()

    def index_operation_finished(self, success: bool, message: str, indexed_count: int):
        """索引操作完成"""
        if success:
            self.statusBar().showMessage('索引操作完成')
            QMessageBox.information(self, '完成', message)
        else:
            self.statusBar().showMessage('索引操作失败')
            QMessageBox.warning(self, '错误', message)

        # 重置UI状态
        self.reset_ui_state()

        # 重新检查索引状态
        self.check_library_index_status()

    def handle_dropped_files(self, image_files: List[str]):
        """处理拖拽的图片文件"""
        # 检查是否选择了图库文件夹
        library_folder = self.folder_path_edit.text()
        if not library_folder:
            QMessageBox.warning(self, "错误", "请先选择图库文件夹")
            return

        # 规范化路径格式，使用反斜杠
        library_folder = normalize_path_for_processing(library_folder)

        if not os.path.exists(library_folder):
            QMessageBox.warning(self, "错误", f"图库文件夹不存在: {library_folder}")
            return

        # 检查是否有其他处理任务正在运行
        if (hasattr(self, 'worker') and self.worker and self.worker.isRunning()) or \
           (hasattr(self, 'drag_drop_worker') and self.drag_drop_worker and self.drag_drop_worker.isRunning()) or \
           (hasattr(self, 'drag_drop_delete_worker') and hasattr(self, 'drag_drop_delete_worker') and self.drag_drop_delete_worker.isRunning()) or \
           (hasattr(self, 'index_worker') and self.index_worker and self.index_worker.isRunning()):
            QMessageBox.warning(self, "错误", "有其他处理任务正在运行，请稍后再试")
            return

        # 检查操作目标
        is_delete_mode = hasattr(self, 'delete_from_library_radio') and self.delete_from_library_radio.isChecked()

        if is_delete_mode:
            # 删除模式：处理拖拽的文件/文件夹删除
            self.handle_delete_operation(image_files, library_folder)
        else:
            # 添加模式：处理拖拽的图片文件添加
            self.handle_add_operation(image_files, library_folder)

    def handle_add_operation(self, image_files: List[str], library_folder: str):
        """处理添加操作"""
        # 检查是否有图片文件
        if not image_files:
            QMessageBox.warning(self, "错误", "没有检测到有效的图片文件")
            return

        # 规范化图片文件路径
        normalized_image_files = []
        for image_file in image_files:
            normalized_path = normalize_path_for_processing(image_file)
            if os.path.exists(normalized_path):
                normalized_image_files.append(normalized_path)
            else:
                self.update_log(f"警告：图片文件不存在，跳过: {image_file}")

        if not normalized_image_files:
            QMessageBox.warning(self, "错误", "没有找到有效的图片文件")
            return

        # 开始处理拖拽的文件
        self.start_drag_drop_processing(normalized_image_files, library_folder)

    def handle_delete_operation(self, paths: List[str], library_folder: str):
        """处理删除操作"""
        if not paths:
            QMessageBox.warning(self, "错误", "没有检测到有效的文件或文件夹")
            return

        # 显示删除确认对话框
        self.show_delete_confirmation_dialog(paths, library_folder)

    def show_delete_confirmation_dialog(self, paths: List[str], library_folder: str):
        """显示删除确认对话框"""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QTextEdit

        # 先验证路径
        validation_result = DragDropDeleteProcessor.validate_paths_in_library(paths, library_folder)

        if not validation_result['valid']:
            # 显示无效路径错误
            invalid_paths = validation_result['invalid_paths']
            error_msg = f"以下 {len(invalid_paths)} 个路径无效（不在图库内或不存在）：\n\n"
            for path in invalid_paths[:10]:  # 最多显示10个
                error_msg += f"• {path}\n"
            if len(invalid_paths) > 10:
                error_msg += f"... 还有 {len(invalid_paths) - 10} 个"

            QMessageBox.warning(self, "删除失败", error_msg)
            return

        # 收集删除信息
        deletion_info = DragDropDeleteProcessor.collect_deletion_info(
            validation_result['files'], validation_result['folders']
        )

        # 创建确认对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("确认删除操作")
        dialog.setFixedSize(600, 500)
        dialog.setModal(True)

        layout = QVBoxLayout(dialog)

        # 警告标题
        title_label = QLabel("⚠️ 危险操作：确认删除")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #d32f2f;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #ffebee;
            border: 2px solid #d32f2f;
            border-radius: 5px;
        """)
        layout.addWidget(title_label)

        # 删除统计信息
        total_files = deletion_info['total_files']
        total_folders = deletion_info['total_folders']

        stats_text = f"""
📊 删除统计：
• 文件数量：{total_files} 个
• 文件夹数量：{total_folders} 个
• 图片文件：{len(deletion_info['image_files'])} 个
• 其他文件：{len(deletion_info['other_files'])} 个
        """

        if deletion_info['folder_contents']:
            stats_text += "\n📁 文件夹详情：\n"
            for folder_name, folder_info in deletion_info['folder_contents'].items():
                stats_text += f"• {folder_name}: {folder_info['total_files']} 个文件 "
                stats_text += f"(图片: {folder_info['image_files']}, 其他: {folder_info['other_files']})\n"

        stats_label = QLabel(stats_text)
        stats_label.setStyleSheet("""
            font-size: 12px;
            color: #333;
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
        """)
        layout.addWidget(stats_label)

        # 详细文件列表
        details_label = QLabel("📋 详细文件列表：")
        details_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        layout.addWidget(details_label)

        details_text = QTextEdit()
        details_text.setReadOnly(True)
        details_text.setMaximumHeight(150)

        details_content = ""
        if validation_result['files']:
            details_content += "📄 文件：\n"
            for file_path in validation_result['files']:
                details_content += f"  • {os.path.relpath(file_path, library_folder)}\n"
            details_content += "\n"

        if validation_result['folders']:
            details_content += "📁 文件夹：\n"
            for folder_path in validation_result['folders']:
                details_content += f"  • {os.path.relpath(folder_path, library_folder)}/\n"

        details_text.setPlainText(details_content)
        details_text.setStyleSheet("font-family: 'Consolas', 'Monaco', monospace; font-size: 11px;")
        layout.addWidget(details_text)

        # 警告提示
        warning_label = QLabel("⚠️ 警告：此操作不可撤销！删除的文件将无法恢复！")
        warning_label.setStyleSheet("""
            color: #d32f2f;
            font-weight: bold;
            font-size: 14px;
            margin: 10px 0;
            padding: 10px;
            background-color: #ffebee;
            border-radius: 5px;
        """)
        layout.addWidget(warning_label)

        # 按钮区域
        button_layout = QHBoxLayout()

        cancel_button = QPushButton("取消")
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #757575;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #616161;
            }
        """)
        cancel_button.clicked.connect(dialog.reject)

        confirm_button = QPushButton("确认删除")
        confirm_button.setStyleSheet("""
            QPushButton {
                background-color: #d32f2f;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #b71c1c;
            }
        """)
        confirm_button.clicked.connect(lambda: self.confirm_delete_operation(dialog, paths, library_folder))

        button_layout.addStretch()
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(confirm_button)
        button_layout.addStretch()

        layout.addLayout(button_layout)

        # 显示对话框
        dialog.exec()

    def confirm_delete_operation(self, dialog, paths: List[str], library_folder: str):
        """确认删除操作"""
        dialog.accept()
        self.start_delete_processing(paths, library_folder)

    def start_delete_processing(self, paths: List[str], library_folder: str):
        """开始删除处理"""
        self.update_log(f"🗑️ 开始删除操作，共 {len(paths)} 项...")

        # 禁用按钮，防止重复操作
        self.clean_and_move_button.setEnabled(False)
        self.rotate_images_button.setEnabled(False)
        self.index_library_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        # 创建删除处理工作线程
        self.drag_drop_delete_worker = DragDropDeleteWorker(paths, library_folder)

        # 连接信号槽
        self.drag_drop_delete_worker.progress.connect(self.update_progress)
        self.drag_drop_delete_worker.log.connect(self.update_log)
        self.drag_drop_delete_worker.finished.connect(self.delete_process_finished)

        # 启动工作线程
        self.drag_drop_delete_worker.start()

    def delete_process_finished(self, success: bool, message: str, statistics: dict):
        """删除处理完成"""
        try:
            if success:
                self.statusBar().showMessage('删除操作完成')

                # 显示删除结果统计弹窗
                if statistics:
                    self.show_delete_statistics(statistics)
                else:
                    QMessageBox.information(self, '完成', message)

                # 更新数据库索引（删除已删除的图片记录）
                self.update_library_index_after_delete(statistics)
            else:
                self.statusBar().showMessage('删除操作失败')
                QMessageBox.warning(self, '错误', message)

        except Exception as e:
            self.update_log(f"删除处理完成回调出错: {str(e)}")
        finally:
            # 重置UI状态
            self.reset_ui_state()

    def show_delete_statistics(self, statistics: dict):
        """显示删除统计信息弹窗"""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QTextEdit

        # 创建统计信息对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("删除操作完成")
        dialog.setFixedSize(600, 500)
        dialog.setModal(True)

        layout = QVBoxLayout(dialog)

        # 标题
        title_label = QLabel("🗑️ 删除操作完成！")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #d32f2f;")
        layout.addWidget(title_label)

        # 统计信息
        deletion_info = statistics.get('deletion_info', {})
        deletion_results = statistics.get('deletion_results', {})

        stats_text = f"""
📊 删除统计：
• 请求删除文件：{statistics.get('total_files_requested', 0)} 个
• 请求删除文件夹：{statistics.get('total_folders_requested', 0)} 个
• 成功删除文件：{statistics.get('deleted_files', 0)} 个
• 成功删除文件夹：{statistics.get('deleted_folders', 0)} 个
• 失败文件：{statistics.get('failed_files', 0)} 个
• 失败文件夹：{statistics.get('failed_folders', 0)} 个
        """

        stats_label = QLabel(stats_text)
        stats_label.setWordWrap(True)
        stats_label.setStyleSheet("font-size: 12px; color: #333; background-color: #f9f9f9; padding: 15px; border-radius: 5px;")
        layout.addWidget(stats_label)

        # 详细结果
        if deletion_results.get('failed_operations'):
            details_label = QLabel("❌ 失败操作详情：")
            details_label.setStyleSheet("font-weight: bold; margin-top: 10px; color: #d32f2f;")
            layout.addWidget(details_label)

            details_text = QTextEdit()
            details_text.setReadOnly(True)
            details_text.setMaximumHeight(150)

            failed_content = ""
            for error in deletion_results['failed_operations']:
                failed_content += f"• {error}\n"

            details_text.setPlainText(failed_content)
            details_text.setStyleSheet("font-family: 'Consolas', 'Monaco', monospace; font-size: 11px; color: #d32f2f;")
            layout.addWidget(details_text)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        ok_button = QPushButton("确定")
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 20px;
                border-radius: 4px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        ok_button.clicked.connect(dialog.accept)

        button_layout.addWidget(ok_button)
        button_layout.addStretch()

        layout.addLayout(button_layout)

        # 显示对话框
        dialog.exec()

    def start_drag_drop_processing(self, image_files: List[str], library_folder: str):
        """开始拖拽处理"""
        self.update_log(f"开始处理拖拽的 {len(image_files)} 个图片文件到图库文件夹...")

        # 禁用按钮，防止重复操作
        self.clean_and_move_button.setEnabled(False)
        self.rotate_images_button.setEnabled(False)
        self.index_library_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        # 从UI读取用户选择的策略
        if self.skip_radio.isChecked():
            duplicate_strategy = "skip"
            strategy_text = "跳过"
        else:
            duplicate_strategy = "overwrite"
            strategy_text = "覆盖"

        self.update_log(f"同名同宽高比图片策略: {strategy_text}")

        # 创建拖拽处理工作线程（启用重复检测，传递策略）
        self.drag_drop_worker = DragDropProcessorWorker(
            image_files,
            library_folder,
            check_duplicates=True,
            duplicate_strategy=duplicate_strategy
        )

        # 连接信号槽
        self.drag_drop_worker.progress.connect(self.update_progress)
        self.drag_drop_worker.log.connect(self.update_log)
        self.drag_drop_worker.finished.connect(self.drag_drop_process_finished)
        self.drag_drop_worker.duplicate_found.connect(self.handle_duplicate_found)

        # 启动工作线程
        self.drag_drop_worker.start()

    def handle_duplicate_found(self, image_path: str, existing_path: str, clean_filename: str):
        """处理发现重复文件的情况"""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel

        # 创建重复文件处理对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("发现重复文件")
        dialog.setFixedSize(500, 300)
        dialog.setModal(True)

        layout = QVBoxLayout(dialog)

        # 标题
        title_label = QLabel("发现重复文件")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 文件信息
        info_label = QLabel(f"""
要添加的文件: {os.path.basename(image_path)}
文件名: {clean_filename}
已存在文件: {existing_path}

请选择处理方式：
        """)
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 跳过按钮
        skip_button = QPushButton("跳过此文件")
        skip_button.clicked.connect(lambda: self.set_duplicate_action_and_close(dialog, image_path, "skip"))

        # 替换按钮
        replace_button = QPushButton("替换现有文件")
        replace_button.clicked.connect(lambda: self.set_duplicate_action_and_close(dialog, image_path, "replace"))

        # 取消按钮
        cancel_button = QPushButton("取消所有操作")
        cancel_button.clicked.connect(lambda: self.set_duplicate_action_and_close(dialog, image_path, "cancel"))

        button_layout.addWidget(skip_button)
        button_layout.addWidget(replace_button)
        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)

        # 显示对话框（非阻塞）
        dialog.show()

    def set_duplicate_action_and_close(self, dialog, image_path: str, action: str):
        """设置重复文件处理动作并关闭对话框"""
        if hasattr(self, 'drag_drop_worker') and self.drag_drop_worker:
            if action == "cancel":
                # 取消所有操作
                self.drag_drop_worker.terminate()
                self.reset_ui_state()
                self.update_log("用户取消了拖拽操作")
            else:
                # 设置处理动作
                self.drag_drop_worker.set_duplicate_action(image_path, action)
                action_text = "跳过" if action == "skip" else "替换"
                self.update_log(f"用户选择{action_text}重复文件: {os.path.basename(image_path)}")

        dialog.close()

    def show_drag_drop_statistics(self, statistics: dict, file_details: dict = None):
        """显示拖拽处理统计信息弹窗"""
        from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                                     QLabel, QTextEdit, QTabWidget, QWidget, QScrollArea)

        # 创建统计信息对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("拖拽处理完成")
        dialog.setFixedSize(700, 600)
        dialog.setModal(True)

        layout = QVBoxLayout(dialog)

        # 标题
        title_label = QLabel("🎉 拖拽处理完成！")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #4CAF50;")
        layout.addWidget(title_label)

        # 创建标签页组件
        tab_widget = QTabWidget()

        # 第一个标签页：总体统计
        summary_widget = QWidget()
        summary_layout = QVBoxLayout(summary_widget)

        # 统计信息文本
        stats_text = f"""
📊 处理统计：
• 总文件数：{statistics.get('total_files', 0)}
• 处理成功：{statistics.get('success_files', 0)}
• 跳过文件：{statistics.get('skipped_files', 0)}
• 处理失败：{statistics.get('error_files', 0)}

🔄 文件处理详情：
• 新文件处理：{statistics.get('new_files_processed', 0)}
• 同名同宽高比覆盖：{statistics.get('same_aspect_ratio_overwritten', 0)}
• 同名不同宽高比重命名：{statistics.get('different_aspect_ratio_renamed', 0)}
        """

        # 如果有特殊情况，添加说明
        if statistics.get('new_files_processed', 0) > 0:
            stats_text += f"\n💡 说明：{statistics.get('new_files_processed', 0)} 个新图片已成功添加到图库"

        if statistics.get('same_aspect_ratio_overwritten', 0) > 0:
            stats_text += f"\n💡 说明：{statistics.get('same_aspect_ratio_overwritten', 0)} 个同名同宽高比图片已按策略执行覆盖操作"

        if statistics.get('different_aspect_ratio_renamed', 0) > 0:
            stats_text += f"\n📝 说明：{statistics.get('different_aspect_ratio_renamed', 0)} 个同名不同宽高比图片已按宽高比倒序分配序号"

        stats_label = QLabel(stats_text)
        stats_label.setWordWrap(True)
        stats_label.setStyleSheet("font-size: 12px; color: #333; background-color: #f9f9f9; padding: 15px; border-radius: 5px;")
        summary_layout.addWidget(stats_label)

        tab_widget.addTab(summary_widget, "📊 总体统计")

        # 如果有详细文件信息，添加详细信息标签页
        if file_details:
            self._add_file_details_tabs(tab_widget, file_details)

        layout.addWidget(tab_widget)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        ok_button = QPushButton("确定")
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 20px;
                border-radius: 4px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        ok_button.clicked.connect(dialog.accept)

        button_layout.addWidget(ok_button)
        button_layout.addStretch()

        layout.addLayout(button_layout)

        # 显示对话框
        dialog.exec()

    def _add_file_details_tabs(self, tab_widget, file_details: dict):
        """添加详细文件信息标签页"""
        from PyQt6.QtWidgets import QTextEdit, QWidget, QVBoxLayout

        # 跳过的文件标签页
        if file_details.get('skipped_files'):
            skipped_widget = QWidget()
            skipped_layout = QVBoxLayout(skipped_widget)

            skipped_text = QTextEdit()
            skipped_text.setReadOnly(True)

            content = f"⏭️ 跳过的文件 ({len(file_details['skipped_files'])} 个)：\n\n"
            for i, file_info in enumerate(file_details['skipped_files'], 1):
                content += f"{i}. {file_info['original_name']}\n"
                content += f"   原因：{file_info['reason']}\n\n"

            skipped_text.setPlainText(content)
            skipped_text.setStyleSheet("font-family: 'Consolas', 'Monaco', monospace; font-size: 11px;")
            skipped_layout.addWidget(skipped_text)

            tab_widget.addTab(skipped_widget, f"⏭️ 跳过 ({len(file_details['skipped_files'])})")

        # 覆盖的文件标签页
        if file_details.get('overwritten_files'):
            overwritten_widget = QWidget()
            overwritten_layout = QVBoxLayout(overwritten_widget)

            overwritten_text = QTextEdit()
            overwritten_text.setReadOnly(True)

            content = f"🔄 覆盖的文件 ({len(file_details['overwritten_files'])} 个)：\n\n"
            for i, file_info in enumerate(file_details['overwritten_files'], 1):
                content += f"{i}. {file_info['original_name']}\n"
                if file_info['original_name'] != file_info['target_name']:
                    content += f"   目标文件：{file_info['target_name']}\n"
                content += f"   原因：{file_info['reason']}\n\n"

            overwritten_text.setPlainText(content)
            overwritten_text.setStyleSheet("font-family: 'Consolas', 'Monaco', monospace; font-size: 11px;")
            overwritten_layout.addWidget(overwritten_text)

            tab_widget.addTab(overwritten_widget, f"🔄 覆盖 ({len(file_details['overwritten_files'])})")

        # 新文件标签页
        if file_details.get('new_files'):
            new_files_widget = QWidget()
            new_files_layout = QVBoxLayout(new_files_widget)

            new_files_text = QTextEdit()
            new_files_text.setReadOnly(True)

            content = f"✨ 新添加的文件 ({len(file_details['new_files'])} 个)：\n\n"
            for i, file_info in enumerate(file_details['new_files'], 1):
                content += f"{i}. {file_info['original_name']}\n"
                if file_info['original_name'] != file_info['target_name']:
                    content += f"   目标文件：{file_info['target_name']}\n"
                content += "\n"

            new_files_text.setPlainText(content)
            new_files_text.setStyleSheet("font-family: 'Consolas', 'Monaco', monospace; font-size: 11px;")
            new_files_layout.addWidget(new_files_text)

            tab_widget.addTab(new_files_widget, f"✨ 新文件 ({len(file_details['new_files'])})")

        # 重命名的文件标签页
        if file_details.get('renamed_files'):
            renamed_widget = QWidget()
            renamed_layout = QVBoxLayout(renamed_widget)

            renamed_text = QTextEdit()
            renamed_text.setReadOnly(True)

            content = f"📝 重命名的文件 ({len(file_details['renamed_files'])} 个)：\n\n"
            for i, file_info in enumerate(file_details['renamed_files'], 1):
                content += f"{i}. {file_info['original_name']}\n"
                content += f"   重命名为：{file_info['target_name']}\n"
                content += f"   原因：{file_info['reason']}\n\n"

            renamed_text.setPlainText(content)
            renamed_text.setStyleSheet("font-family: 'Consolas', 'Monaco', monospace; font-size: 11px;")
            renamed_layout.addWidget(renamed_text)

            tab_widget.addTab(renamed_widget, f"📝 重命名 ({len(file_details['renamed_files'])})")

        # 错误文件标签页
        if file_details.get('error_files'):
            error_widget = QWidget()
            error_layout = QVBoxLayout(error_widget)

            error_text = QTextEdit()
            error_text.setReadOnly(True)

            content = f"❌ 处理失败的文件 ({len(file_details['error_files'])} 个)：\n\n"
            for i, file_info in enumerate(file_details['error_files'], 1):
                content += f"{i}. {file_info['original_name']}\n"
                content += f"   错误：{file_info['error']}\n\n"

            error_text.setPlainText(content)
            error_text.setStyleSheet("font-family: 'Consolas', 'Monaco', monospace; font-size: 11px; color: #d32f2f;")
            error_layout.addWidget(error_text)

            tab_widget.addTab(error_widget, f"❌ 错误 ({len(file_details['error_files'])})")

    def drag_drop_process_finished(self, success: bool, message: str, processed_count: int, statistics: dict = None, file_details: dict = None):
        """拖拽处理完成"""
        try:
            if success:
                self.statusBar().showMessage('拖拽处理完成')

                # 显示详细统计信息弹窗
                if statistics:
                    self.show_drag_drop_statistics(statistics, file_details)
                else:
                    # 兼容旧版本，显示简单信息
                    detailed_message = f"{message}\n处理了 {processed_count} 个文件"
                    QMessageBox.information(self, '完成', detailed_message)

                # 如果处理成功且有图库索引器，使用增量更新索引
                if processed_count > 0 and HAS_IMAGE_INDEXER:
                    self.update_library_index_after_add(statistics)
            else:
                self.statusBar().showMessage('拖拽处理失败')
                QMessageBox.warning(self, '错误', message)

        except Exception as e:
            self.update_log(f"拖拽处理完成回调出错: {str(e)}")
        finally:
            # 重置UI状态
            self.reset_ui_state()

    def update_library_index_after_drag_drop_async(self):
        """异步更新图库索引，避免阻塞UI"""
        self._update_library_index_common(async_mode=True)

    def drag_drop_index_update_finished(self, success: bool, message: str, count: int):
        """拖拽后索引更新完成回调"""
        if success:
            self.update_log(f"✅ 拖拽后索引更新完成，共索引 {count} 个文件")
            # 强制刷新UI显示，确保第一次拖拽后也能正确显示
            QApplication.processEvents()
            # 再次检查索引状态，确保显示正确
            self.check_library_index_status()
        else:
            self.update_log(f"❌ 拖拽后索引更新失败: {message}")

    def update_library_index_after_drag_drop(self):
        """拖拽处理完成后更新图库索引（同步版本，保留兼容性）"""
        self._update_library_index_common(async_mode=False)

    def _update_library_index_common(self, async_mode=True, added_files=None):
        """通用的图库索引更新方法（已弃用，使用专门的增量更新方法）

        Args:
            async_mode: 是否使用异步模式
            added_files: 新添加的文件路径列表，如果提供则使用增量更新
        """
        self.update_log("⚠️ 使用了已弃用的通用索引更新方法，建议使用专门的增量更新方法")

        library_folder = self.folder_path_edit.text()
        if not library_folder or not HAS_IMAGE_INDEXER:
            return

        # 优先使用增量更新
        if added_files:
            # 模拟统计信息结构
            mock_statistics = {
                'file_details': {
                    'successful_files': added_files
                }
            }
            self.update_library_index_after_add(mock_statistics)
        else:
            # 回退到完整重建
            self._fallback_to_full_rebuild(library_folder)

    def _execute_sync_index_update(self, library_folder):
        """执行同步索引更新"""
        try:
            # 规范化路径格式，使用反斜杠
            library_folder = normalize_path_for_processing(library_folder)

            # 创建新的索引器实例，避免连接冲突
            indexer = ImageIndexerDuckDB(fast_mode=True)

            # 检查是否已有索引
            if indexer.is_indexed(library_folder):
                # 使用完整重建而不是增量更新，确保第一次拖拽后显示正确
                self.update_log("正在进行完整索引重建以确保显示正确...")
                success, message, count = indexer.scan_library(library_folder, fast_mode=True)

                if success:
                    self.update_log(f"索引重建完成，共索引 {count} 个文件")
                    # 更新索引状态显示
                    self.update_index_status(True, count, f"已索引 {count} 个文件")
                else:
                    self.update_log(f"索引重建失败: {message}")
            else:
                self.update_log("图库尚未建立索引，跳过索引更新")

        except Exception as e:
            self.update_log(f"更新索引时出错: {str(e)}")
            import traceback
            self.update_log(f"详细错误信息: {traceback.format_exc()}")

    def update_library_index_after_delete(self, statistics: dict):
        """删除操作完成后更新图库索引（增量删除）"""
        library_folder = self.folder_path_edit.text()
        if not library_folder or not HAS_IMAGE_INDEXER:
            self.update_log("⚠️ 图库路径为空或索引器不可用，跳过索引更新")
            return

        try:
            # 从统计信息中提取已删除的文件路径
            deleted_files = []

            if not statistics:
                self.update_log("⚠️ 没有删除统计信息，跳过索引更新")
                return

            deletion_info = statistics.get('deletion_info', {})
            deletion_results = statistics.get('deletion_results', {})

            self.update_log("📊 分析删除的图片文件...")

            # 1. 收集直接删除的图片文件
            if 'deleted_file_list' in deletion_results:
                for file_path in deletion_results['deleted_file_list']:
                    # 检查是否为图片文件
                    if self._is_image_file(file_path):
                        deleted_files.append(file_path)
                        self.update_log(f"🖼️ 发现删除的图片文件: {os.path.basename(file_path)}")

            # 2. 收集删除文件夹中的图片文件
            if 'deleted_folder_list' in deletion_results and 'folder_contents' in deletion_info:
                for folder_path in deletion_results['deleted_folder_list']:
                    folder_name = os.path.basename(folder_path)

                    # 从删除前收集的信息中获取文件夹内容
                    if folder_name in deletion_info['folder_contents']:
                        folder_info = deletion_info['folder_contents'][folder_name]
                        folder_files = folder_info.get('file_list', [])

                        for file_path in folder_files:
                            if self._is_image_file(file_path):
                                deleted_files.append(file_path)
                                self.update_log(f"🖼️ 发现文件夹中的图片文件: {os.path.basename(file_path)}")

            if not deleted_files:
                self.update_log("ℹ️ 没有需要从索引中删除的图片文件")
                return

            self.update_log(f"📋 共找到 {len(deleted_files)} 个需要从索引中删除的图片文件")

            # 规范化路径格式
            library_folder = normalize_path_for_processing(library_folder)

            # 转换为相对路径
            relative_paths = []
            for file_path in deleted_files:
                try:
                    rel_path = os.path.relpath(file_path, library_folder)
                    # 规范化路径分隔符
                    rel_path = rel_path.replace('\\', '/')
                    relative_paths.append(rel_path)
                except Exception as e:
                    self.update_log(f"⚠️ 转换相对路径失败 {file_path}: {str(e)}")

            if not relative_paths:
                self.update_log("⚠️ 没有有效的相对路径需要从索引中删除")
                return

            # 创建索引器实例
            indexer = ImageIndexerDuckDB(fast_mode=True)
            indexer.set_library_path(library_folder)

            # 检查索引是否存在
            if not indexer.is_indexed(library_folder):
                self.update_log("ℹ️ 图库尚未建立索引，跳过索引删除")
                return

            # 执行增量删除（带重试机制）
            self.update_log(f"🗑️ 正在从索引中删除 {len(relative_paths)} 条记录...")
            success, message, deleted_count = self._execute_index_delete_with_retry(indexer, relative_paths)

            if success:
                self.update_log(f"✅ 索引删除完成: {message}")

                # 获取更新后的图片数量
                current_count = indexer.get_image_count()
                self.update_log(f"📊 当前索引中的图片数量: {current_count}")

                # 更新UI显示
                self.update_index_status(True, current_count, f"已索引 {current_count} 个文件")

                # 强制刷新UI显示
                self.check_library_index_status()
            else:
                self.update_log(f"❌ 索引删除失败: {message}")
                # 删除失败时，尝试完整重建索引
                self.update_log("🔄 删除失败，尝试完整重建索引...")
                self._fallback_to_full_rebuild(library_folder)

            # 显式关闭索引器连接
            indexer.close()

        except Exception as e:
            self.update_log(f"❌ 删除后更新索引时出错: {str(e)}")
            import traceback
            self.update_log(f"详细错误信息: {traceback.format_exc()}")

    def _execute_index_delete_with_retry(self, indexer, relative_paths, max_retries=3):
        """执行索引删除操作，带重试机制处理数据库冲突"""
        import time

        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    # 等待一段时间后重试
                    wait_time = attempt * 0.5  # 0.5秒, 1秒, 1.5秒
                    self.update_log(f"⏳ 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)

                    # 重新创建索引器实例，避免连接冲突
                    library_folder = self.folder_path_edit.text()
                    indexer.close()
                    indexer = ImageIndexerDuckDB(fast_mode=True)
                    indexer.set_library_path(library_folder)

                self.update_log(f"🔄 尝试删除索引记录 (第 {attempt + 1}/{max_retries} 次)...")
                success, message, deleted_count = indexer.remove_images_by_paths(relative_paths)

                if success:
                    return success, message, deleted_count
                else:
                    self.update_log(f"⚠️ 第 {attempt + 1} 次尝试失败: {message}")

            except Exception as e:
                error_msg = str(e)
                self.update_log(f"⚠️ 第 {attempt + 1} 次尝试出错: {error_msg}")

                # 检查是否是数据库冲突错误
                if "write-write conflict" in error_msg or "TransactionContext Error" in error_msg:
                    if attempt < max_retries - 1:
                        self.update_log("🔄 检测到数据库写冲突，将重试...")
                        continue
                    else:
                        self.update_log("❌ 多次重试后仍然失败，可能存在数据库锁定问题")
                        return False, f"数据库冲突，重试 {max_retries} 次后失败: {error_msg}", 0
                else:
                    # 非冲突错误，直接返回失败
                    return False, f"索引删除失败: {error_msg}", 0

        return False, f"重试 {max_retries} 次后仍然失败", 0

    def _is_image_file(self, file_path: str) -> bool:
        """检查文件是否为图片文件"""
        try:
            ext = os.path.splitext(file_path.lower())[1]
            return ext in SUPPORTED_IMAGE_EXTENSIONS
        except:
            return False

    def update_library_index_after_add(self, statistics: dict):
        """添加操作完成后更新图库索引（增量添加）"""
        library_folder = self.folder_path_edit.text()
        if not library_folder or not HAS_IMAGE_INDEXER:
            self.update_log("⚠️ 图库路径为空或索引器不可用，跳过索引更新")
            return

        try:
            # 从统计信息中获取成功处理的文件列表
            processed_files = []

            if not statistics:
                self.update_log("⚠️ 没有处理统计信息，跳过索引更新")
                return

            self.update_log("📊 分析添加的图片文件...")

            # 从统计信息中获取成功处理的文件列表
            if 'file_details' in statistics:
                file_details = statistics['file_details']
                for category, files in file_details.items():
                    if category in ['successful_files', 'overwritten_files']:
                        for file_path in files:
                            if self._is_image_file(file_path):
                                processed_files.append(file_path)
                                self.update_log(f"🖼️ 发现添加的图片文件: {os.path.basename(file_path)}")

            if not processed_files:
                self.update_log("ℹ️ 没有需要添加到索引的图片文件")
                # 仍然需要检查索引状态，可能需要完整重建
                self._fallback_to_full_rebuild(library_folder)
                return

            self.update_log(f"📋 共找到 {len(processed_files)} 个需要添加到索引的图片文件")

            # 规范化路径格式
            library_folder = normalize_path_for_processing(library_folder)

            # 创建索引器实例
            indexer = ImageIndexerDuckDB(fast_mode=True)
            indexer.set_library_path(library_folder)

            # 检查是否已有索引
            if not indexer.is_indexed(library_folder):
                self.update_log("ℹ️ 图库尚未建立索引，将执行完整索引建立...")
                self._fallback_to_full_rebuild(library_folder)
                return

            # 转换为相对路径
            relative_paths = []
            for file_path in processed_files:
                try:
                    rel_path = os.path.relpath(file_path, library_folder)
                    # 规范化路径分隔符
                    rel_path = rel_path.replace('\\', '/')
                    relative_paths.append(rel_path)
                except Exception as e:
                    self.update_log(f"⚠️ 转换相对路径失败 {file_path}: {str(e)}")

            if not relative_paths:
                self.update_log("⚠️ 没有有效的相对路径需要添加到索引")
                return

            # 执行增量添加
            self.update_log(f"➕ 正在向索引中添加 {len(relative_paths)} 条记录...")
            success, message, added_count = indexer.add_images_by_paths(relative_paths, fast_mode=True)

            if success:
                self.update_log(f"✅ 索引添加完成: {message}")

                # 获取更新后的图片数量
                current_count = indexer.get_image_count()
                self.update_log(f"📊 当前索引中的图片数量: {current_count}")

                # 更新UI显示
                self.update_index_status(True, current_count, f"已索引 {current_count} 个文件")

                # 强制刷新UI显示
                self.check_library_index_status()
            else:
                self.update_log(f"❌ 索引添加失败: {message}")
                # 失败时尝试完整重建
                self._fallback_to_full_rebuild(library_folder)

            # 显式关闭索引器连接
            indexer.close()

            # 延迟再次检查索引状态，确保显示正确
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(500, self.check_library_index_status)

        except Exception as e:
            self.update_log(f"❌ 添加后更新索引时出错: {str(e)}")
            import traceback
            self.update_log(f"详细错误信息: {traceback.format_exc()}")
            # 出错时尝试完整重建
            self._fallback_to_full_rebuild(library_folder)

    def _fallback_to_full_rebuild(self, library_folder: str):
        """回退到完整重建索引"""
        try:
            self.update_log("🔄 回退到完整索引重建...")
            indexer = ImageIndexerDuckDB(fast_mode=True)

            if indexer.is_indexed(library_folder):
                success, message, count = indexer.scan_library(library_folder, fast_mode=True)
                if success:
                    self.update_log(f"✅ 索引重建完成，共索引 {count} 个文件")
                    self.update_index_status(True, count, f"已索引 {count} 个文件")
                else:
                    self.update_log(f"❌ 索引重建失败: {message}")
            else:
                self.update_log("图库尚未建立索引，跳过重建")

            indexer.close()
        except Exception as e:
            self.update_log(f"❌ 完整重建索引失败: {str(e)}")

    def _execute_incremental_add_update(self, library_folder: str, added_files: List[str]):
        """执行增量添加更新

        Args:
            library_folder: 图库文件夹路径
            added_files: 新添加的文件路径列表
        """
        try:
            # 规范化路径格式
            library_folder = normalize_path_for_processing(library_folder)

            # 转换为相对路径
            relative_paths = []
            for file_path in added_files:
                try:
                    rel_path = os.path.relpath(file_path, library_folder)
                    relative_paths.append(rel_path)
                except Exception as e:
                    self.update_log(f"转换相对路径失败 {file_path}: {str(e)}")

            if not relative_paths:
                self.update_log("没有有效的文件路径需要添加到索引")
                return

            # 创建索引器实例
            indexer = ImageIndexerDuckDB(fast_mode=True)
            indexer.set_library_path(library_folder)

            # 检查是否已有索引
            if not indexer.is_indexed(library_folder):
                self.update_log("图库尚未建立索引，将执行完整索引建立...")
                self._execute_sync_index_update(library_folder)
                return

            # 执行增量添加
            self.update_log(f"正在向索引中添加 {len(relative_paths)} 条记录...")
            success, message, added_count = indexer.add_images_by_paths(relative_paths, fast_mode=True)

            if success:
                self.update_log(f"✅ 索引添加完成: {message}")

                # 获取更新后的图片数量
                current_count = indexer.get_image_count()
                self.update_index_status(True, current_count, f"已索引 {current_count} 个文件")

                # 强制刷新UI显示
                self.check_library_index_status()
            else:
                self.update_log(f"❌ 索引添加失败: {message}")

        except Exception as e:
            self.update_log(f"增量添加索引时出错: {str(e)}")
            import traceback
            self.update_log(f"详细错误信息: {traceback.format_exc()}")

    def _incremental_index_update(self, indexer, library_folder):
        """执行真正的增量索引更新，只处理新增的图片文件"""
        try:
            # 设置图库路径
            indexer.set_library_path(library_folder)

            # 收集当前文件夹中的所有图片文件
            current_files = set()
            supported_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.tif', '.webp')
            for root, dirs, files in os.walk(library_folder):
                for file in files:
                    if file.lower().endswith(supported_extensions):
                        rel_path = os.path.relpath(os.path.join(root, file), library_folder)
                        # 规范化路径，使用反斜杠
                        rel_path = normalize_path_for_processing(rel_path)
                        current_files.add(rel_path)

            # 获取数据库中已索引的文件
            indexed_files = set()
            try:
                if indexer.db:
                    result = indexer.db.execute("SELECT relative_path FROM image_files").fetchall()
                    indexed_files = {row[0] for row in result}
            except Exception as e:
                self.update_log(f"获取已索引文件列表失败: {str(e)}")
                # 如果无法获取已索引文件，说明可能是第一次建立索引
                if not indexed_files:
                    self.update_log("数据库为空，执行完整索引建立...")
                    return indexer.scan_library(library_folder, fast_mode=True)
                else:
                    return False, f"获取已索引文件列表失败: {str(e)}", 0

            # 找出新增的文件
            new_files = current_files - indexed_files

            if not new_files:
                self.update_log("没有发现新增的图片文件")
                return True, "索引已是最新", len(indexed_files)

            self.update_log(f"发现 {len(new_files)} 个新增图片文件，开始增量索引...")

            # 使用完整重建而不是增量更新，确保第一次拖拽后显示正确
            self.update_log("为确保显示正确，执行完整索引重建...")
            return indexer.scan_library(library_folder, fast_mode=True)

        except Exception as e:
            self.update_log(f"❌ 增量索引更新失败: {str(e)}")
            # 如果增量更新失败，回退到完整重建
            self.update_log("增量更新失败，回退到完整重建...")
            try:
                return indexer.scan_library(library_folder, fast_mode=True)
            except Exception as rebuild_error:
                return False, f"完整重建也失败: {str(rebuild_error)}", 0

    def update_log(self, message):
        """更新日志"""
        self.log_text.append(message)
        # 滚动到底部以显示最新日志
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)

    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
        # 强制立即更新UI
        QApplication.processEvents()

    def clean_and_move_images(self):
        """执行清洗移动"""
        folder_path = self.folder_path_edit.text()
        if not folder_path:
            QMessageBox.warning(self, "错误", "请先选择图库文件夹")
            return

        # 确认对话框
        reply = QMessageBox.question(
            self,
            "确认清洗移动操作",
            f"📁 目标图库: {folder_path}\n\n"
            "此操作将执行以下步骤：\n"
            "1️⃣ 扫描图库中的所有图片文件\n"
            "2️⃣ 优化文件名（空格替换为'-'，去除多余字符）\n"
            "3️⃣ 分析中文名字出现次数进行智能分类\n"
            "4️⃣ 只有2个以上同样中文名的图片才创建文件夹\n"
            "5️⃣ 唯一中文名的图片保留在主目录\n"
            "6️⃣ 无中文名的图片保留在主目录\n\n"
            "📂 示例分类规则：\n"
            "   • '晕染-001.jpg' + '晕染-002.jpg' → 晕染/ 文件夹\n"
            "   • '人物-写真.jpg' (唯一) → 主目录\n"
            "   • 'english-file.jpg' (无中文) → 主目录\n\n"
            "⚠️ 注意：此操作会修改文件名和文件位置\n"
            "💡 建议：操作前请备份重要文件\n\n"
            "是否继续执行清洗移动操作？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.update_log("开始执行图库清洗移动...")
            self.start_processing('clean_and_move')

    def rotate_images(self):
        """执行图片旋转"""
        folder_path = self.folder_path_edit.text()
        if not folder_path:
            QMessageBox.warning(self, "错误", "请先选择图库文件夹")
            return

        self.update_log("开始执行图库图片横向放置...")
        self.start_processing('rotate_images')

    def move_images(self):
        """执行图片移动整理"""
        folder_path = self.folder_path_edit.text()
        if not folder_path:
            QMessageBox.warning(self, "错误", "请先选择图库文件夹")
            return

        # 确认对话框
        reply = QMessageBox.question(
            self,
            "确认操作",
            "此操作将：\n"
            "1. 将所有子文件夹中的图片移动到主目录\n"
            "2. 同名文件将被覆盖\n"
            "3. 删除所有空的子文件夹\n\n"
            "此操作不可撤销，是否继续？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.update_log("开始执行图库图片移动整理...")
            self.start_processing('move_images')



    def start_processing(self, operation_type):
        """开始处理"""
        folder_path = self.folder_path_edit.text()

        # 禁用功能按钮，启用停止按钮
        self.clean_and_move_button.setEnabled(False)
        self.rotate_images_button.setEnabled(False)
        self.index_library_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        # 创建Worker线程处理
        self.worker = ImageProcessorWorker(folder_path, operation_type)

        # 连接信号槽
        self.worker.progress.connect(self.update_progress)
        self.worker.log.connect(self.update_log)
        self.worker.finished.connect(self.process_finished)

        # 启动Worker线程
        self.worker.start()

    def stop_processing(self):
        """停止处理"""
        if hasattr(self, 'worker') and self.worker and self.worker.isRunning():
            self.update_log("正在请求停止处理...")
            self.worker.request_stop()
            self.stop_button.setEnabled(False)
            self.stop_button.setText("正在停止...")

        if hasattr(self, 'drag_drop_worker') and self.drag_drop_worker and self.drag_drop_worker.isRunning():
            self.update_log("正在停止拖拽处理...")
            self.drag_drop_worker.terminate()
            self.drag_drop_worker.wait(3000)  # 等待最多3秒
            self.reset_ui_state()

        if hasattr(self, 'drag_drop_delete_worker') and self.drag_drop_delete_worker and self.drag_drop_delete_worker.isRunning():
            self.update_log("正在停止删除操作...")
            self.drag_drop_delete_worker.request_stop()
            self.drag_drop_delete_worker.terminate()
            self.drag_drop_delete_worker.wait(3000)  # 等待最多3秒
            self.reset_ui_state()

        if hasattr(self, 'index_worker') and self.index_worker and self.index_worker.isRunning():
            self.update_log("正在停止索引操作...")
            self.index_worker.request_stop()
            self.stop_button.setEnabled(False)
            self.stop_button.setText("正在停止...")

    def reset_ui_state(self):
        """重置UI状态"""
        # 重新启用功能按钮，禁用停止按钮
        if self.folder_path_edit.text():
            self.clean_and_move_button.setEnabled(True)
            self.rotate_images_button.setEnabled(True)
            self.index_library_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.stop_button.setText("停止处理")

        # 更新拖拽区域的文本显示，确保显示正确的操作模式说明
        self.update_drag_drop_area_text()

    def process_finished(self, success, message):
        """处理完成"""
        if success:
            self.statusBar().showMessage('处理完成')
            QMessageBox.information(self, '完成', message)
        else:
            self.statusBar().showMessage('处理失败')
            QMessageBox.warning(self, '错误', message)

        # 重置UI状态
        self.reset_ui_state()


if __name__ == '__main__':
    app = QApplication(sys.argv)

    # 初始化SupabaseHelper
    supabase_helper = SupabaseHelper()

    # 显示登录对话框
    login_dialog = LoginDialog(supabase_helper, ROBOT_SMART_NAME, ROBOT_CURRENT_VERSION)
    if login_dialog.exec() == 1:  # 1 表示 QDialog.Accepted
        # 登录成功，创建主界面
        window = ImageProcessorApp()

        # 设置SupabaseHelper引用
        window.supabase_helper = supabase_helper

        # 检查版本（必须在设置supabase_helper之后）
        window.check_version()

        # 在主界面日志区域显示登录成功信息
        if supabase_helper.is_connected():
            try:
                email = supabase_helper.get_user_email()
                if email:
                    window.update_log(f"用户 {email} 登录成功")
                    window.update_log("正在初始化应用...")
                else:
                    window.update_log("用户登录成功")
            except Exception as e:
                window.update_log(f"获取用户信息时出错: {e}")

        # 显示主界面
        window.show()
        sys.exit(app.exec())
    else:
        # 用户取消登录，退出程序
        sys.exit(0)
