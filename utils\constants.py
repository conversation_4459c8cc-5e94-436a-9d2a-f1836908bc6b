#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
常量定义模块

提供全局常量定义和统一的访问方法，方便全局修改和维护
"""

import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("Constants")

class PhotoshopConstants:
    """Photoshop相关常量"""

    # 颜色模式
    COLOR_MODE_RGB = 2  # RGB模式
    COLOR_MODE_CMYK = 3  # CMYK模式
    COLOR_MODE_LAB = 4  # Lab模式

    # 背景填充
    BACKGROUND_WHITE = 1  # 白色背景
    BACKGROUND_TRANSPARENT = 2  # 透明背景

    # 保存选项
    SAVE_CHANGES = 1  # 保存更改
    DONT_SAVE_CHANGES = 2  # 不保存更改
    PROMPT_TO_SAVE = 3  # 提示保存

    # 画布尺寸限制（厘米）
    MAX_CANVAS_HEIGHT_CM = 5000  # 最大高度50米
    MAX_CANVAS_WIDTH_CM = 300    # 最大宽度3米

    # 画布尺寸限制（像素，基于300PPI）
    MAX_CANVAS_HEIGHT_PX = 590551  # 最大高度50米对应的像素数 (5000cm * 0.393701in/cm * 300px/in)
    MAX_CANVAS_WIDTH_PX = 35433    # 最大宽度3米对应的像素数 (300cm * 0.393701in/cm * 300px/in)

    # 默认PPI
    DEFAULT_PPI = 72  # 默认分辨率72PPI

class CanvasConstants:
    """画布相关常量"""

    # 默认画布设置
    DEFAULT_CANVAS_WIDTH_M = 2.0  # 默认画布宽度2米
    DEFAULT_IMAGE_SPACING_CM = 0.1  # 默认图片间距0.1厘米


# 注意：原ClassificationConstants类已删除，因为A/B/C分类逻辑已废弃
# 项目现在使用RectPack算法统一处理所有图片

class Constants:
    """常量访问类，提供统一的常量访问方法"""

    # 单例实例
    _instance = None

    # 常量字典
    _constants = {}

    @classmethod
    def get_instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = Constants()
            cls._instance._init_constants()
        return cls._instance

    def _init_constants(self):
        """初始化常量字典"""
        # 添加Photoshop常量
        for attr_name in dir(PhotoshopConstants):
            if not attr_name.startswith('_'):
                self._constants[f"PS_{attr_name}"] = getattr(PhotoshopConstants, attr_name)

        # 添加画布常量
        for attr_name in dir(CanvasConstants):
            if not attr_name.startswith('_'):
                self._constants[f"CANVAS_{attr_name}"] = getattr(CanvasConstants, attr_name)

        # 分类常量已移除，统一处理

    def get(self, name, default=None):
        """获取常量值

        Args:
            name: 常量名称
            default: 默认值，如果常量不存在则返回此值

        Returns:
            常量值
        """
        return self._constants.get(name, default)

    def __getattr__(self, name):
        """通过属性访问常量

        Args:
            name: 常量名称

        Returns:
            常量值

        Raises:
            AttributeError: 如果常量不存在
        """
        if name in self._constants:
            return self._constants[name]
        raise AttributeError(f"常量 {name} 不存在")

# 创建全局访问函数
def get_constant(name, default=None):
    """获取常量值

    Args:
        name: 常量名称
        default: 默认值，如果常量不存在则返回此值

    Returns:
        常量值
    """
    return Constants.get_instance().get(name, default)

# 导出常量实例，方便直接导入使用
constants = Constants.get_instance()
