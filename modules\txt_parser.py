"""
TXT文件解析模块
从robot_bot_0.0.12.py移植的txt文件解析功能
"""

import os
import re
import logging
import pandas as pd
import threading
import time
import json
from concurrent.futures import ThreadPoolExecutor, as_completed

log = logging.getLogger(__name__)


class TxtParser:
    """TXT文件解析器，负责解析txt文件内容"""
    
    def __init__(self, material_keywords=None, remove_words=None, openai_client_manager=None):
        """
        初始化TXT解析器
        
        Args:
            material_keywords: 材质关键词列表
            remove_words: 需要移除的词汇列表
            openai_client_manager: OpenAI客户端管理器
        """
        self.material_keywords = material_keywords or ['印花2m', 'DLDM2M', '圈绒棉布2m', '波斯绒']
        self.remove_words = remove_words or ['颜色分类', '颜色']
        self.openai_mgr = openai_client_manager
        
        # 初始化正则表达式模式
        self._init_patterns()
        
        # 初始化材质映射
        self._init_material_mapping()
        
        # 缓存
        self.openai_cache = {}
        
    def _init_patterns(self):
        """初始化正则表达式模式"""
        self.s_persian_pattern = re.compile(
            r'^S波斯绒-([A-Za-z0-9\u4e00-\u9fa5\-]+)[；;]?$'
        )
        self.dldm_special_pattern = re.compile(
            r'^DLDM2M～(\d+)\*(\d+)～(\d+)～([A-Za-z0-9\u4e00-\u9fa5\-]+)[；;]?$'
        )
        self.print_size_qty_pattern = re.compile(
            r'^印花2m-([A-Za-z0-9\u4e00-\u9fa5\-]+)-(\d+)-(\d+).*?(\d+)$'
        )
        self.qrmb_pattern = re.compile(
            r'^([A-Za-z0-9]+)@(\d+\.?\d*)-(\d+\.?\d*)-(\d+)-([A-Za-z0-9\u4e00-\u9fa5\-]+)-?([A-Za-z0-9\u4e00-\u9fa5\-]*)[；;]?$'
        )
        self.leading_symbols_pattern = re.compile(r'^[,;，。；#]+')
        self.separator_pattern = re.compile(r'[-～~]')
        
    def _init_material_mapping(self):
        """初始化材质映射"""
        self.material_case_mapping = {}
        for keyword in self.material_keywords:
            self.material_case_mapping[keyword.lower()] = keyword
            
        # 构建材质正则模式
        self.material_separators = r'[-丨]'
        material_regex = '|'.join(
            f"{re.escape(keyword)}(?:{self.material_separators})"
            for keyword in self.material_keywords
        )
        self.material_patterns = re.compile(
            rf'^(?:{material_regex})',
            re.IGNORECASE
        )
        
        # remove_word处理
        if self.remove_words:
            self.remove_words = sorted(self.remove_words, key=len, reverse=True)
            escaped_remove_words = [re.escape(word) for word in self.remove_words]
            self.remove_word_pattern = re.compile(
                '|'.join(escaped_remove_words),
                re.IGNORECASE
            )
        else:
            self.remove_word_pattern = None
            
    def normalize_material_name(self, material):
        """标准化材质名称"""
        if not material:
            return material
        material_lower = material.lower()
        if material_lower in self.material_case_mapping:
            return self.material_case_mapping[material_lower]
        parts = material.split('-')
        normalized_parts = []
        for part in parts:
            part_lower = part.lower()
            if part_lower in self.material_case_mapping:
                normalized_parts.append(self.material_case_mapping[part_lower])
            else:
                normalized_parts.append(part)
        return '-'.join(normalized_parts)
        
    def clean_pattern(self, pattern):
        """清理图案名称"""
        if not pattern:
            return pattern
        original_pattern = pattern
        pattern = pattern.strip()
        pattern = pattern.replace('·', '')
        pattern = re.sub(r'^[^\w\u4e00-\u9fa5]+', '', pattern).strip()

        material_match = self.material_patterns.search(pattern)
        if material_match:
            matched_part = material_match.group(0)
            pattern = pattern[len(matched_part):].strip()

        pattern = re.sub(r'^丨+', '', pattern).strip()
        pattern = re.sub(r'^(颜色分类?:[^-]+[-])', '', pattern)
        pattern = re.sub(r'^(颜色[:：])', '', pattern)
        pattern = re.sub(r'^颜色分类?[:：][^-]+[-]', '', pattern)
        pattern = re.sub(r'^颜色[:：][^-]+[-]', '', pattern)
        pattern = re.sub(r'^颜色分类?[:：][^;；]+[;；]', '', pattern)
        pattern = self.leading_symbols_pattern.sub('', pattern).strip()
        pattern = pattern.split(',')[0].strip()
        pattern = pattern.split('，')[0].strip()
        
        match = re.match(r'^([\u4e00-\u9fa5A-Za-z]+)[-\s]*([\dA-Za-z]+)?$', pattern)
        if match:
            main_pattern = match.group(1)
            suffix = match.group(2)
            if suffix:
                pattern = f"{main_pattern}{suffix}"
            else:
                pattern = main_pattern
        else:
            pattern = re.sub(r'[-\s]+', '', pattern)
        pattern = pattern.replace(' ', '')

        # 使用 remove_word_pattern 清洗图案名称
        if self.remove_word_pattern:
            original_after_basic = pattern
            prev_pattern = None
            while prev_pattern != pattern:
                prev_pattern = pattern
                pattern = self.remove_word_pattern.sub('', pattern)
                pattern = pattern.strip()
            pattern = re.sub(r'^[-丨]+|[-丨]+$', '', pattern).strip()
            if pattern != original_after_basic:
                log.debug(f"Remove words 清理: '{original_after_basic}' -> '{pattern}'")

        if not pattern:
            log.debug(f"清洗后图案名称为空，原始图案名称: '{original_pattern}'")
            return ''
        log.debug(f"清洗完成: '{original_pattern}' -> '{pattern}'")
        return pattern
        
    def parse_line(self, line, line_number=None, file_name=None):
        """
        解析单行文本
        
        Args:
            line: 要解析的文本行
            line_number: 行号
            file_name: 文件名
            
        Returns:
            list: 解析结果列表
        """
        # 预处理：去除前后空白符
        original_line = line.strip()
        
        if not original_line:
            return []

        # 清洗掉 '共' 之后的数据
        if '共' in original_line:
            original_line = original_line.split('共')[0].strip()

        # 检查"，"后的内容是否包含有效数据
        if '，' in original_line:
            parts = original_line.split('，')
            first_part = parts[0].strip()
            remaining_parts = [p.strip() for p in parts[1:] if p.strip()]
            valid_parts = [first_part]
            for part in remaining_parts:
                has_numbers = bool(re.search(r'\d', part))
                has_separators = len(self.separator_pattern.findall(part)) >= 2
                if has_numbers and has_separators:
                    valid_parts.append(part)
            original_line = '，'.join(valid_parts)

        # 标准化分隔符
        line = self.leading_symbols_pattern.sub('', original_line)
        line = line.rstrip(',;，。；#').strip()
        line = (line.replace('～', '-').replace('~', '-')
                    .replace('*', '-').replace('×', '-')
                    .replace('x', '-').replace('X', '-')
                    .replace('cm', '').replace('Cm', '').replace('CM', '').strip())
        
        # 拆分子项
        sub_lines = re.split('[；;，,]', line)
        sub_lines = [s.strip() for s in sub_lines if s.strip()]
        
        # 去重，保持顺序
        sub_lines = list(dict.fromkeys(sub_lines))
        
        results = []
        for sub_line in sub_lines:
            # 检查分隔符数量
            separator_count = len(self.separator_pattern.findall(sub_line))
            
            if separator_count < 2:
                results.append({
                    '原始数据': original_line,
                    '行号': line_number,
                    '错误原因': "数据不具备拆分条件，缺少必要的分隔符",
                    '错误日志': f"子项 '{sub_line}' 中检测到的分隔符数量不足（最少需要2个分隔符）",
                    '数据处理流程': '规则匹配',
                    '解析错误': "数据不具备拆分条件"
                })
                continue
                
            # 尝试各种格式匹配
            parsed_entry = self._try_parse_formats(sub_line, original_line, line_number)
            if parsed_entry:
                results.append(parsed_entry)
            else:
                # 如果规则匹配失败，尝试OpenAI解析
                if self.openai_mgr:
                    openai_results = self._parse_with_openai(sub_line, line_number, file_name)
                    results.extend(openai_results)
                else:
                    results.append({
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': "无法解析的格式",
                        '错误日志': f"子项 '{sub_line}' 不匹配任何已知格式",
                        '数据处理流程': '规则匹配',
                        '解析错误': "无法解析的格式"
                    })
                    
        return results
        
    def _try_parse_formats(self, sub_line, original_line, line_number):
        """尝试各种格式解析"""
        # 1. 匹配S波斯绒格式
        match = self.s_persian_pattern.match(sub_line)
        if match:
            pattern = self.clean_pattern(match.group(1))
            return {
                '材质': '波斯绒',
                '图案': pattern,
                '宽cm': 0,
                '高cm': 0,
                '图案全称': f"{pattern}-0-0-横版",
                '数量': 1,
                '累计长度': 0,
                '原始数据': original_line,
                '处理方式': '规则匹配'
            }
            
        # 2. 匹配DLDM2M特殊格式
        match = self.dldm_special_pattern.match(sub_line)
        if match:
            width = int(match.group(1))
            length = int(match.group(2))
            quantity = int(match.group(3))
            pattern = self.clean_pattern(match.group(4))
            
            # 确保宽度 > 高度
            if width > 0 and length > 0 and width < length:
                width, length = length, width
                
            orientation = '竖版' if '竖' in original_line else '横版'
            return {
                '材质': 'DLDM2M',
                '图案': pattern,
                '宽cm': width,
                '高cm': length,
                '图案全称': f"{pattern}-{width}-{length}-{orientation}",
                '数量': quantity,
                '累计长度': (length + 1) * quantity,
                '原始数据': original_line,
                '处理方式': '规则匹配'
            }
            
        # 可以继续添加其他格式的匹配...
        
        return None
        
    def _parse_with_openai(self, sub_line, line_number, file_name):
        """使用OpenAI解析"""
        if not self.openai_mgr:
            return [{
                '原始数据': sub_line,
                '行号': line_number,
                '错误原因': 'OpenAI配置不可用',
                '错误日志': 'OpenAI客户端未初始化',
                '数据处理流程': 'AI解析',
                '解析错误': 'OpenAI配置不可用'
            }]
            
        # 检查缓存
        if sub_line in self.openai_cache:
            return self.openai_cache[sub_line]
            
        try:
            response = self.openai_mgr.chat_completion(sub_line)
            # 处理OpenAI响应...
            # 这里需要实现具体的响应处理逻辑
            
            # 暂时返回错误，实际实现时需要完善
            result = [{
                '原始数据': sub_line,
                '行号': line_number,
                '错误原因': 'OpenAI解析功能待完善',
                '错误日志': 'OpenAI解析功能正在开发中',
                '数据处理流程': 'AI解析',
                '解析错误': 'OpenAI解析功能待完善'
            }]
            
            self.openai_cache[sub_line] = result
            return result
            
        except Exception as e:
            error_result = [{
                '原始数据': sub_line,
                '行号': line_number,
                '错误原因': f'OpenAI解析失败: {str(e)}',
                '错误日志': repr(e),
                '数据处理流程': 'AI解析',
                '解析错误': f'OpenAI解析失败: {str(e)}'
            }]
            self.openai_cache[sub_line] = error_result
            return error_result
