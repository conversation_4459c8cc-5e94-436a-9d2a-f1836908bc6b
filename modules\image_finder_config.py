#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
图片查找器配置管理模块
独立的配置数据库管理，支持多宽度参数和参数驱动的边界值逻辑
"""

import os
import duckdb
import re
from typing import Dict, Any, List, Tuple
from utils.log_config import get_logger

log = get_logger("ImageFinderConfig")


class ImageFinderConfigManager:
    """图片查找器配置管理器

    使用独立的 image_finder_config.db 数据库存储配置参数
    支持多宽度参数和参数驱动的边界值逻辑
    """

    _instance = None
    _initialized = False
    _shared_memory_db = None  # 共享内存数据库实例
    
    def __new__(cls, db_file: str = "image_finder_config.db"):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super(ImageFinderConfigManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self, db_file: str = "image_finder_config.db"):
        """初始化配置管理器"""
        if self._initialized:
            return
            
        self.db_file = db_file
        self.db = None
        self._connect_db()
        self._init_tables()
        self._load_default_config()
        
        ImageFinderConfigManager._initialized = True
    
    def _connect_db(self) -> bool:
        """连接到配置数据库"""
        try:
            # 关闭现有连接
            if self.db:
                try:
                    self.db.close()
                except:
                    pass
                self.db = None

            # 确保目录存在
            db_dir = os.path.dirname(os.path.abspath(self.db_file))
            if db_dir and not os.path.exists(db_dir):
                os.makedirs(db_dir, exist_ok=True)

            # 使用更安全的连接方式，避免文件占用问题
            try:
                # 直接连接，如果失败则等待重试
                self.db = duckdb.connect(self.db_file)
                # 测试连接
                self.db.execute("SELECT 1")
                log.info(f"成功连接到图片查找器配置数据库: {self.db_file}")
                return True
            except Exception as e:
                log.warning(f"连接数据库失败: {str(e)}")
                self.db = None

                # 如果是文件占用错误，不删除文件，而是使用共享内存数据库作为临时方案
                if "already open" in str(e) or "being used by another process" in str(e):
                    log.info("数据库文件被占用，使用共享内存数据库作为临时方案")
                    try:
                        # 使用类级别的共享内存数据库
                        if ImageFinderConfigManager._shared_memory_db is None:
                            ImageFinderConfigManager._shared_memory_db = duckdb.connect(":memory:")
                            log.info("创建新的共享内存数据库")
                        self.db = ImageFinderConfigManager._shared_memory_db
                        return True
                    except Exception as e2:
                        log.error(f"创建共享内存数据库失败: {str(e2)}")
                        return False

                return False

        except Exception as e:
            log.error(f"连接配置数据库失败: {str(e)}")
            self.db = None
            return False
    
    def _init_tables(self) -> bool:
        """初始化配置表"""
        if not self.db:
            return False
        
        try:
            # 创建配置表
            self.db.execute("""
                CREATE TABLE IF NOT EXISTS config (
                    key VARCHAR PRIMARY KEY,
                    value VARCHAR,
                    value_type VARCHAR NOT NULL,
                    description VARCHAR
                )
            """)
            
            # 提交更改
            self.db.commit()
            log.info("成功初始化图片查找器配置表")
            return True
        except Exception as e:
            log.error(f"初始化配置表失败: {str(e)}")
            return False
    
    def _load_default_config(self) -> None:
        """加载默认配置"""
        default_config = {
            # 多宽度参数配置
            'width_values': ('163, 205', 'string', '介质宽度值，支持多个值用逗号分隔'),
            
            # 基础参数
            'default_dpi': (72, 'int', '默认DPI值'),
            'no_image_keywords': ('定制, 图', 'string', '无图识别关键字，用逗号分隔'),
            
            # 边界值参数（参数驱动，避免硬编码）
            'boundary_value_1': (100, 'int', '第一个边界值'),
            'boundary_value_2': (160, 'int', '第二个边界值'),
            
            # 增加值参数
            'increment_value_1': (2, 'float', '小于第一边界值时的增加值'),
            'increment_value_2': (3, 'float', '介于两个边界值之间时的增加值'),
            'increment_value_3': (5, 'float', '大于第二边界值时的增加值'),
            
            # 等于边界值时的增加值
            'equal_boundary_increment_1': (2.5, 'float', '等于第一边界值时的增加值'),
            'equal_boundary_increment_2': (3.0, 'float', '等于第二边界值时的增加值'),
            
            # 路径记忆
            'last_table_folder': ('', 'string', '上次选择的表格文件夹路径'),
            'last_library_folder': ('', 'string', '上次选择的图库文件夹路径'),
        }
        
        # 确保所有默认值都存在
        for key, (value, value_type, description) in default_config.items():
            if not self._has_key(key):
                self.set(key, value, description)
    
    def _has_key(self, key: str) -> bool:
        """检查配置键是否存在"""
        try:
            result = self.db.execute("SELECT COUNT(*) FROM config WHERE key = ?", [key]).fetchone()
            return result[0] > 0
        except Exception as e:
            log.error(f"检查配置键失败: {str(e)}")
            return False
    
    def _convert_value(self, value: Any, value_type: str) -> str:
        """将值转换为字符串表示"""
        if value_type == 'bool':
            return str(int(value))
        return str(value)
    
    def _parse_value(self, value_str: str, value_type: str) -> Any:
        """将字符串解析为指定类型的值"""
        if value_type == 'int':
            return int(value_str)
        elif value_type == 'float':
            return float(value_str)
        elif value_type == 'bool':
            return bool(int(value_str))
        else:  # string
            return value_str
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        # 重试机制
        for attempt in range(3):
            if not self.db:
                self._connect_db()

            if not self.db:
                if attempt == 2:  # 最后一次尝试
                    log.error(f"获取配置值 {key} 失败: 数据库连接不可用")
                    return default
                continue

            try:
                result = self.db.execute(
                    "SELECT value, value_type FROM config WHERE key = ?",
                    [key]
                ).fetchone()

                if result:
                    value_str, value_type = result
                    return self._parse_value(value_str, value_type)
                return default
            except Exception as e:
                log.warning(f"获取配置值失败 (尝试 {attempt + 1}/3): {str(e)}")
                if attempt < 2:
                    # 重置连接并重试
                    self.db = None
                    continue
                else:
                    log.error(f"获取配置值 {key} 最终失败: {str(e)}")
                    return default
    
    def set(self, key: str, value: Any, description: str = '') -> bool:
        """设置配置值"""
        if value is None:
            return False

        # 重试机制
        for attempt in range(3):
            if not self.db:
                self._connect_db()

            if not self.db:
                if attempt == 2:  # 最后一次尝试
                    log.error(f"设置配置值 {key} 失败: 数据库连接不可用")
                    return False
                continue

            try:
                # 确定值类型
                if isinstance(value, bool):
                    value_type = 'bool'
                elif isinstance(value, int):
                    value_type = 'int'
                elif isinstance(value, float):
                    value_type = 'float'
                else:
                    value_type = 'string'

                # 转换值为字符串
                value_str = self._convert_value(value, value_type)

                # 插入或更新配置
                self.db.execute("""
                    INSERT OR REPLACE INTO config (key, value, value_type, description)
                    VALUES (?, ?, ?, ?)
                """, [key, value_str, value_type, description])

                return True
            except Exception as e:
                log.warning(f"设置配置值失败 (尝试 {attempt + 1}/3): {str(e)}")
                if attempt < 2:
                    # 重置连接并重试
                    self.db = None
                    continue
                else:
                    log.error(f"设置配置值 {key} 最终失败: {str(e)}")
                    return False
    
    def parse_width_values(self, width_str: str = None) -> List[int]:
        """解析宽度值字符串，支持多个值用逗号分隔
        
        Args:
            width_str: 宽度字符串，如果为None则从配置中获取
            
        Returns:
            List[int]: 解析后的宽度值列表
        """
        if width_str is None:
            width_str = self.get('width_values', '163, 205')
        
        try:
            # 支持中文逗号和英文逗号
            width_str = width_str.replace('，', ',')
            
            # 分割并解析
            width_values = []
            for part in width_str.split(','):
                part = part.strip()
                if part:
                    try:
                        width_values.append(int(float(part)))
                    except (ValueError, TypeError):
                        log.warning(f"无法解析宽度值: {part}")
                        continue
            
            # 如果没有有效值，使用默认值
            if not width_values:
                width_values = [163, 205]
                log.warning("没有有效的宽度值，使用默认值: [163, 205]")
            
            # 排序确保顺序
            width_values.sort()
            
            log.info(f"解析宽度值: {width_values}")
            return width_values
            
        except Exception as e:
            log.error(f"解析宽度值失败: {str(e)}")
            return [163, 205]  # 默认值
    
    def determine_sheet_name(self, height: float, width_values: List[int] = None) -> str:
        """根据高度值确定应该分配到哪个sheet
        
        Args:
            height: 图片高度值
            width_values: 宽度值列表，如果为None则从配置中获取
            
        Returns:
            str: sheet名称
        """
        if width_values is None:
            width_values = self.parse_width_values()
        
        try:
            # 按照用户需求的逻辑：
            # 高 <= 第一个宽度值，归到第一个sheet
            # 第一个宽度值 < 高 <= 第二个宽度值，归到第二个sheet
            # 高 > 最后一个宽度值，归到"超宽幅"sheet
            
            for i, width_val in enumerate(width_values):
                if height <= width_val:
                    return str(width_val)
            
            # 如果高度超过所有宽度值，归到超宽幅
            return "超宽幅"
            
        except Exception as e:
            log.error(f"确定sheet名称失败: {str(e)}")
            return str(width_values[0]) if width_values else "163"
    
    def get_boundary_params(self) -> Dict[str, Any]:
        """获取边界值参数"""
        return {
            'boundary_value_1': self.get('boundary_value_1', 100),
            'boundary_value_2': self.get('boundary_value_2', 160),
            'increment_value_1': self.get('increment_value_1', 2),
            'increment_value_2': self.get('increment_value_2', 3),
            'increment_value_3': self.get('increment_value_3', 5),
            'equal_boundary_increment_1': self.get('equal_boundary_increment_1', 2.5),
            'equal_boundary_increment_2': self.get('equal_boundary_increment_2', 3.0),
        }
    
    def close(self) -> None:
        """关闭数据库连接"""
        if self.db:
            try:
                # 如果是共享内存数据库，不要关闭它
                if self.db is ImageFinderConfigManager._shared_memory_db:
                    log.info("跳过关闭共享内存数据库连接")
                    self.db = None
                else:
                    self.db.close()
                    self.db = None
                    log.info("数据库连接已关闭")
            except Exception as e:
                log.error(f"关闭数据库连接失败: {str(e)}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()


# 全局实例获取函数
_global_image_finder_config = None

def get_image_finder_config() -> ImageFinderConfigManager:
    """获取全局图片查找器配置管理器实例"""
    global _global_image_finder_config
    if _global_image_finder_config is None:
        _global_image_finder_config = ImageFinderConfigManager()
    return _global_image_finder_config
