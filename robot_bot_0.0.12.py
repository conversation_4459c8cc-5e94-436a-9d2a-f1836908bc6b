import re
import os
import sys
import logging
import shutil  # 用于文件操作
import pandas as pd
from PyQt6.QtWidgets import (
    QApplication, QWidget, QPushButton, QVBoxLayout,
    QFileDialog, QMessageBox, QLabel, QDialog, QLineEdit,
    QProgressBar, QTextEdit, QHBoxLayout, QCheckBox, QFrame,
    QGridLayout
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QObject, QTimer, QSettings, QEvent, QRect
from PyQt6.QtGui import QFont, QPainter, QColor, QBrush, QPen, QLinearGradient
from concurrent.futures import ThreadPoolExecutor, as_completed
from supabase import create_client, Client  # 用于 Supabase 集成
import openpyxl  # 用于 Excel 操作
from openai import OpenAI
import time
import json
import threading
import random

# 设置日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()  # 输出到控制台
    ]
)
log = logging.getLogger(__name__)

# Supabase 配置
SUPABASE_URL = 'https://cnqfr7i5g6hfi0gtjhj0.baseapi.memfiredb.com'
SUPABASE_API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYW5vbiIsImV4cCI6MzI4NzM1NDUyNiwiaWF0IjoxNzEwNTU0NTI2LCJpc3MiOiJzdXBhYmFzZSJ9.2OKUbREN2eOTDl5iB9FtHfv-nQaTgHCmm9qeRaNqeyI'

# 用户会话数据 - 将在登录后填充
user_session = {
    "user": None,
    "access_token": None,
    "refresh_token": None
}

# 初始化 Supabase 客户端
supabase = create_client(SUPABASE_URL, SUPABASE_API_KEY)

# 应用名称 & 当前应用版本
ROBOT_SMART_NAME = "DeAI-RPA-操作表格智能助手-AI解析版"
ROBOT_CURRENT_VERSION = "0.0.12"
ROBOT_BOT_TAG = 'robot_bot'

# 自定义Logo组件
class LogoWidget(QFrame):
    def __init__(self, parent=None):
        super(LogoWidget, self).__init__(parent)
        self.setFixedSize(70, 70)
        self.setStyleSheet("background-color: transparent;")

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)  # PyQt6: 使用完整枚举名

        # 绘制圆形背景
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor(0, 120, 215))  # Win11蓝色
        gradient.setColorAt(1, QColor(0, 103, 192))

        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.PenStyle.NoPen)  # PyQt6: 使用完整枚举名
        painter.drawEllipse(0, 0, self.width(), self.height())

        # 绘制简单的"AI"文字
        painter.setPen(QPen(QColor(255, 255, 255), 2))
        painter.setFont(QFont("Arial", 28, QFont.Weight.Bold))  # PyQt6: 使用完整枚举名
        painter.drawText(QRect(0, 0, self.width(), self.height()), Qt.AlignmentFlag.AlignCenter, "AI")  # PyQt6: 使用完整枚举名

# 登录对话框类
class LoginDialog(QDialog):
    def __init__(self, parent=None):
        super(LoginDialog, self).__init__(parent)
        self.setWindowTitle(f"{ROBOT_SMART_NAME} 登录")
        self.setFixedSize(380, 450)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowType.WindowContextHelpButtonHint)  # PyQt6: 使用完整枚举名

        # Win11风格设置
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QLabel {
                color: #202020;
            }
            QLineEdit {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 8px;
                background-color: white;
                selection-background-color: #0078d7;
                min-height: 20px;
            }
            QLineEdit:focus {
                border: 1px solid #0078d7;
            }
            QPushButton#loginButton {
                background-color: #0078d7;
                color: white;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
                min-height: 36px;
            }
            QPushButton#loginButton:hover {
                background-color: #1683d8;
            }
            QPushButton#loginButton:pressed {
                background-color: #006cc1;
            }
            QCheckBox {
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border-radius: 2px;
                border: 1px solid #d0d0d0;
            }
            QCheckBox::indicator:checked {
                background-color: #0078d7;
                border: 1px solid #0078d7;
            }
            #loginCard {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e0e0e0;
            }
        """)

        # 居中显示
        self.center()

        # 创建主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(0)

        # 创建登录卡片
        login_card = QFrame()
        login_card.setObjectName("loginCard")

        # 卡片内部布局
        card_layout = QVBoxLayout(login_card)
        card_layout.setContentsMargins(20, 25, 20, 25)
        card_layout.setSpacing(12)

        # 添加Logo
        logo_layout = QHBoxLayout()
        self.logo = LogoWidget()
        logo_layout.addStretch()
        logo_layout.addWidget(self.logo)
        logo_layout.addStretch()
        card_layout.addLayout(logo_layout)

        # 版本标签
        version_label = QLabel(f"v{ROBOT_CURRENT_VERSION}")
        version_label.setFont(QFont("微软雅黑", 9))
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)  # PyQt6: 使用完整枚举名
        version_label.setStyleSheet("color: #707070; margin-bottom: 10px;")
        card_layout.addWidget(version_label)

        # 添加间距
        card_layout.addSpacing(5)

        # 用户名输入
        username_label = QLabel("用户名")
        username_label.setFont(QFont("微软雅黑", 10))
        card_layout.addWidget(username_label)

        self.username_input = QLineEdit()
        self.username_input.setFont(QFont("微软雅黑", 10))
        self.username_input.setPlaceholderText("请输入邮箱")
        card_layout.addWidget(self.username_input)

        # 密码输入
        password_label = QLabel("密码")
        password_label.setFont(QFont("微软雅黑", 10))
        card_layout.addWidget(password_label)

        self.password_input = QLineEdit()
        self.password_input.setFont(QFont("微软雅黑", 10))
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)  # PyQt6: 使用完整枚举名
        self.password_input.setPlaceholderText("请输入密码")
        card_layout.addWidget(self.password_input)

        # 记住密码选项
        remember_layout = QHBoxLayout()
        self.remember_checkbox = QCheckBox("记住密码")
        self.remember_checkbox.setFont(QFont("微软雅黑", 9))
        remember_layout.addWidget(self.remember_checkbox)
        remember_layout.addStretch()
        card_layout.addLayout(remember_layout)

        # 添加间距
        card_layout.addSpacing(5)

        # 登录按钮
        self.login_button = QPushButton("登 录")
        self.login_button.setObjectName("loginButton")
        self.login_button.setFont(QFont("微软雅黑", 11))
        self.login_button.clicked.connect(self.try_login)
        card_layout.addWidget(self.login_button)

        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setFont(QFont("微软雅黑", 9))
        self.status_label.setStyleSheet("color: #e81123; margin-top: 5px;")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)  # PyQt6: 使用完整枚举名
        card_layout.addWidget(self.status_label)

        # 将登录卡片添加到主布局
        main_layout.addWidget(login_card)

        # 设置主布局
        self.setLayout(main_layout)

        # 设置默认焦点
        self.username_input.setFocus()

        # 加载保存的用户名和密码
        settings = QSettings("DeAI", "RobotBot")
        saved_username = settings.value("username", "")
        saved_password = settings.value("password", "")
        saved_remember = settings.value("remember_password", False, type=bool)

        if saved_username:
            self.username_input.setText(saved_username)
            if saved_remember and saved_password:
                self.password_input.setText(saved_password)
                self.remember_checkbox.setChecked(True)
            self.password_input.setFocus()

    def center(self):
        """将窗口居中显示"""
        qr = self.frameGeometry()
        # PyQt6: 使用QApplication.primaryScreen()替代QDesktopWidget
        screen = QApplication.primaryScreen()
        cp = screen.availableGeometry().center()
        qr.moveCenter(cp)
        self.move(qr.topLeft())

    def try_login(self):
        """尝试登录"""
        email = self.username_input.text().strip()
        password = self.password_input.text()

        if not email:
            self.status_label.setText("请输入用户名")
            return

        if not password:
            self.status_label.setText("请输入密码")
            return

        # 更新UI状态
        self.status_label.setText("正在登录...")
        self.login_button.setEnabled(False)
        self.login_button.setText("登录中...")
        QApplication.processEvents()

        # 创建 Supabase 客户端
        auth_client = create_client(SUPABASE_URL, SUPABASE_API_KEY)

        try:
            # 尝试使用邮箱和密码登录
            response = auth_client.auth.sign_in_with_password({
                "email": email,
                "password": password
            })

            # 保存用户会话信息
            # 使用全局变量，不需要global声明
            user_session["user"] = response.user
            user_session["access_token"] = response.session.access_token
            user_session["refresh_token"] = response.session.refresh_token

            # 保存用户名和密码（如果选择了记住密码）
            settings = QSettings("DeAI", "RobotBot")
            settings.setValue("username", email)
            settings.setValue("remember_password", self.remember_checkbox.isChecked())

            if self.remember_checkbox.isChecked():
                settings.setValue("password", password)
            else:
                settings.setValue("password", "")

            log.info(f"用户 {email} 登录成功")
            self.accept()
        except Exception as e:
            log.error(f"登录失败: {e}")
            self.status_label.setText("登录失败，请检查用户名和密码")
            self.login_button.setText("登 录")
            self.login_button.setEnabled(True)

# 工具类
class Utils:
    @staticmethod
    def is_valid_tracking_number(tracking_number):
        """
        验证快递单号是否符合预期格式

        参数:
            tracking_number: 待验证的快递单号

        返回值:
            bool: 是否为有效的快递单号
        """
        if not tracking_number or not isinstance(tracking_number, str):
            return False

        # 去除空格和其他非法字符
        tracking_number = tracking_number.strip()

        # 检查是否为顺丰单号（以SF开头）
        if tracking_number.upper().startswith('SF') and len(tracking_number) >= 12:
            return True

        # 检查是否为纯数字单号（长度在10-20位之间）
        if tracking_number.isdigit() and 10 <= len(tracking_number) <= 20:
            return True

        return False

# 创建一个函数来获取认证后的 Supabase 客户端
def get_authenticated_client() -> Client:
    """
    获取一个已认证的 Supabase 客户端，用于 RLS 访问
    如果用户已登录，返回认证客户端；如果未登录，返回基础客户端但会记录警告
    """
    try:
        # 创建基础客户端
        auth_client = create_client(SUPABASE_URL, SUPABASE_API_KEY)

        # 检查是否有有效的用户会话
        if user_session["access_token"] and user_session["refresh_token"]:
            try:
                # 设置会话
                auth_client.auth.set_session(user_session["access_token"], user_session["refresh_token"])
                log.info("已成功创建认证客户端(使用会话方式)")
                return auth_client
            except Exception as session_error:
                log.error(f"使用会话方式创建认证客户端失败: {session_error}")
        else:
            log.warning("没有有效的用户会话信息，将使用基础客户端（可能受RLS限制）")

        # 如果没有有效会话或设置会话失败，返回基础客户端
        # 注意：这个客户端将受到RLS限制，可能无法访问某些数据
        return auth_client

    except Exception as e:
        log.error(f"创建认证客户端时出错: {e}")

        # 如果出现错误，返回基础客户端
        return create_client(SUPABASE_URL, SUPABASE_API_KEY)

# ---------------------------
# OpenAI客户端管理器（新增长期记忆机制）
# ---------------------------
class OpenAIClientManager(QObject):
    initialized = pyqtSignal(bool, str)
    status_update = pyqtSignal(str)  # 用于更新日志、进度与网络请求状态

    def __init__(self):
        super().__init__()
        self.client = None
        self.config = None  # 当前的OpenAI配置信息
        self.chat_history = None  # 长期记忆的对话记录
        self._max_context_tokens = 3500  # 最大上下文token阈值，超过则重置

        # 新增：QPM 限速相关变量
        # _qpm: 来自 supabase 的 qpm 值；0 表示不限制，每分钟最大调用次数
        self._qpm = 0
        self._qpm_lock = threading.Lock()
        self._qpm_call_count = 0
        self._qpm_current_window_start = 0

        self._init_thread = QThread()
        self.moveToThread(self._init_thread)
        self._init_thread.started.connect(self._async_init)

    def _async_init(self):
        """异步初始化OpenAI客户端"""
        try:
            self.status_update.emit("正在获取OpenAI配置...")
            start_time = time.time()
            # 获取认证客户端
            auth_client = get_authenticated_client()
            # 从 Supabase 获取配置，使用认证客户端支持RLS
            openai_response = auth_client.table('openai_robot').select('*').eq('robot', ROBOT_BOT_TAG).execute()
            if not openai_response.data:
                raise ValueError("未找到 {ROBOT_BOT_TAG} 配置")

            self.config = openai_response.data[0]
            required_params = ['api_key', 'base_url', 'model', 'sys_role', 'sys_content']
            if not all(self.config.get(p) for p in required_params):
                raise ValueError("OpenAI配置参数不完整")

            # 获取新字段 qpm，0 表示不限制， >0 表示每分钟最大调用次数
            self._qpm = int(self.config.get('qpm', 0))

            self.status_update.emit("正在创建OpenAI客户端...")
            # 采用 OpenAI 兼容API，配置从 Supabase中获取
            self.client = OpenAI(
                api_key=self.config['api_key'],
                base_url=self.config['base_url']
            )

            # 初始化长期记忆，设置系统角色一次
            self._reset_chat_history()
            # 设置最大上下文token数（如配置中有设置则使用配置，否则默认3500）
            self._max_context_tokens = int(self.config.get('max_context_tokens', 3500))

            self.status_update.emit("正在测试OpenAI连接...")
            self._test_connection_async()

            log.info(f"OpenAI初始化完成，耗时 {time.time()-start_time:.2f}s")
            self.initialized.emit(True, "OpenAI客户端初始化成功")
        except Exception as e:
            error_msg = f"OpenAI初始化失败: {str(e)}"
            log.error(error_msg)
            self.initialized.emit(False, error_msg)

    def _rate_limit(self):
        """
        简单的 QPM 限速机制：每分钟最多允许 self._qpm 次调用。
        如果 self._qpm 为 0，则不会限速。
        """
        if self._qpm <= 0:
            return
        with self._qpm_lock:
            current_time = time.time()
            # 如果当前窗口已过60秒，则重置计数器及起始时间
            if (current_time - self._qpm_current_window_start) >= 60:
                self._qpm_current_window_start = current_time
                self._qpm_call_count = 0
            # 如果调用数已达到上限，则等待直到新的时间窗口开始
            if self._qpm_call_count >= self._qpm:
                sleep_time = 60 - (current_time - self._qpm_current_window_start)
                if sleep_time > 0:
                    log.info(f"达到QPM限制，等待 {sleep_time:.2f} 秒")
                    time.sleep(sleep_time)
                self._qpm_current_window_start = time.time()
                self._qpm_call_count = 0
            self._qpm_call_count += 1

    def _test_connection_async(self):
        """异步测试连接（不影响主流程）"""
        def _test():
            try:
                start = time.time()
                test_message = self.config['sys_content'][:50] + "..."
                # 使用长期记忆接口测试
                _ = self.chat_completion(test_message)
                log.debug(f"OpenAI连接测试成功，耗时 {time.time()-start:.2f}s")
            except Exception as e:
                log.warning(f"OpenAI连接测试失败（不影响主要功能）: {str(e)}")
        QTimer.singleShot(0, _test)

    def get_client_config(self):
        """获取当前OpenAI配置（若无则返回空字典）"""
        return self.config if self.config else {}

    def _reset_chat_history(self):
        """重置长期记忆对话，只保留系统消息"""
        system_message = self.config.get('sys_content', '')
        self.chat_history = [{"role": "system", "content": system_message}]

    def _estimate_token_count(self, messages):
        """
        简单估算消息中使用的token数，每4个字符大致算作1个token
        """
        total_chars = sum(len(msg['content']) for msg in messages)
        return total_chars // 4

    def chat_completion(self, user_message, force_system_reset=False):
        """
        使用长期记忆方式与OpenAI接口交互：
          - 仅在首次或强制重置时添加系统消息，后续不重复输入 system role；
          - 检测上下文token数是否超过阈值，超过则重置长期记忆，仅保留系统消息，再执行调用。
        """
        if force_system_reset or not self.chat_history:
            self._reset_chat_history()

        # 当历史消息token数过多时重置
        if self._estimate_token_count(self.chat_history) > self._max_context_tokens:
            self.status_update.emit("Token上下文超过阀值，重置长期记忆对话...")
            self._reset_chat_history()

        # 添加用户消息
        self.chat_history.append({"role": "user", "content": user_message})

        try:
            # 新增：在调用API前检查QPM限制
            self._rate_limit()
            response = self.client.chat.completions.create(
                model=self.config.get('model', 'deepseek-v3'),
                messages=self.chat_history,
                temperature=0.3,
                max_tokens=500,
            )
            assistant_message = response.choices[0].message.content
            # 将助手回复加入长期记忆
            self.chat_history.append({"role": "assistant", "content": assistant_message})
            return response
        except Exception as e:
            raise e

    def clone(self):
        """
        Clone the current OpenAIClientManager to create an isolated conversation context
        for individual file processing.
        """
        new_mgr = OpenAIClientManager.__new__(OpenAIClientManager)
        new_mgr.client = self.client
        new_mgr.config = self.config
        new_mgr.chat_history = [{"role": "system", "content": self.config.get('sys_content', '')}]
        new_mgr._max_context_tokens = self._max_context_tokens
        # 共享 QPM 限速相关配置和状态（确保所有克隆实例都遵循同一限速策略）
        new_mgr._qpm = self._qpm
        new_mgr._qpm_lock = self._qpm_lock
        new_mgr._qpm_call_count = self._qpm_call_count
        new_mgr._qpm_current_window_start = self._qpm_current_window_start
        new_mgr.status_update = self.status_update
        return new_mgr


# ---------------------------
# 解析器模块
# ---------------------------
class LineParser:
    def __init__(self, material_keywords, remove_words, openai_client_manager=None):
        self._lock = threading.Lock()  # 用于保护 OpenAI 请求的线程安全
        self.s_persian_pattern = re.compile(
            r'^S波斯绒-([A-Za-z0-9\u4e00-\u9fa5\-]+)[；;]?$'
        )
        self.dldm_special_pattern = re.compile(
            r'^DLDM2M～(\d+)\*(\d+)～(\d+)～([A-Za-z0-9\u4e00-\u9fa5\-]+)[；;]?$'
        )
        self.print_size_qty_pattern = re.compile(
            r'^印花2m-([A-Za-z0-9\u4e00-\u9fa5\-]+)-(\d+)-(\d+).*?(\d+)$'
        )
        self.qrmb_pattern = re.compile(
            r'^([A-Za-z0-9]+)@(\d+\.?\d*)-(\d+\.?\d*)-(\d+)-([A-Za-z0-9\u4e00-\u9fa5\-]+)-?([A-Za-z0-9\u4e00-\u9fa5\-]*)[；;]?$'
        )
        self.qrmb3m_pattern = re.compile(
            r'^([A-Za-z0-9]+)-(\d+)-(\d+)-(\d+)-([A-Za-z0-9\u4e00-\u9fa5\-]+)(?:-([A-Za-z0-9\u4e00-\u9fa5\-]+))?[；;]?$'
        )
        self.dldm_pattern = re.compile(
            r'^([A-Za-z0-9./\-]+)-(\d+)-(\d+)-(\d+)-([\u4e00-\u9fa5A-Za-z0-9\- ]+)(?:-([A-Za-z0-9\u4e00-\u9fa5\-]+))?[；;]?$'
        )
        self.diameter_pattern = re.compile(
            r'^([A-Za-z0-9\u4e00-\u9fa5]+)-([A-Za-z0-9\u4e00-\u9fa5\-]+)-直径(\d+)(?:cm)?\s+(\d+)$'
        )
        self.normal_pattern = re.compile(
            r'^([^-]+)-([^-]+)-(\d+)-(\d+)(?:\s+(\d+))?(?:\s+(竖))?[；;]?$'
        )
        self.leading_symbols_pattern = re.compile(r'^[,;，。；#]+')
        # 新增：匹配分隔符（半角 '-'、半角 '~' 和全角 '～'）
        self.separator_pattern = re.compile(r'[-～~]')
        # 移除重复的 ZPTQ 格式正则，只保留一个标准格式
        # 格式为: 材质～宽*长～图案；（经过标准化替换后，形如 "材质-宽-长-图案"）
        self.zptq_pattern = re.compile(
            r'^([A-Za-z0-9\u4e00-\u9fa5]+)-(\d+)-(\d+)-([A-Za-z0-9\u4e00-\u9fa5\-]+)[；;]?$'
        )

        # 材质名称相关
        self.material_case_mapping = {}
        for keyword in material_keywords:
            self.material_case_mapping[keyword.lower()] = keyword

        self.material_separators = r'[-丨]'
        material_regex = '|'.join(
            f"{re.escape(keyword)}(?:{self.material_separators})"
            for keyword in material_keywords
        )
        self.material_patterns = re.compile(
            rf'^(?:{material_regex})',
            re.IGNORECASE
        )
        # remove_word处理
        if remove_words:
            self.remove_words = sorted(remove_words, key=len, reverse=True)  # 按长度降序排序，确保优先匹配较长的词
            escaped_remove_words = [re.escape(word) for word in self.remove_words]
            self.remove_word_pattern = re.compile(
                '|'.join(escaped_remove_words),
                re.IGNORECASE
            )
        else:
            self.remove_word_pattern = None

        self.material_keywords = material_keywords

        self.openai_mgr = openai_client_manager  # 可传入 OpenAIClientManager 实例
        self.openai_retries = 5  # 重试次数
        self.openai_timeout = 20  # 请求超时（秒）
        self.openai_cache = {}  # 缓存OpenAI解析结果

        # 新增扩展直径格式：支持在 "直径数字+(可选cm)" 与数量之间包含任意描述文本（例如 "原创设计"）
        self.circle_extended_pattern = re.compile(
            r'^([A-Za-z0-9\u4e00-\u9fa5]+)-([A-Za-z0-9\u4e00-\u9fa5\-]+)-直径(\d+)(?:cm)?(?:\s+\S+)*\s+(\d+)$'
        )

        # 构建材质正则模式
        material_pattern = '|'.join(re.escape(m) for m in material_keywords)

        # 匹配格式：材质-图案(可含多个分隔符)-数字-数字 数字
        # 例如：圈绒棉布2m-喜乐福-白金-60-90 1 或 印花2m-素色-04-200*300cm 2
        self.material_pattern_size = re.compile(
            rf'^({material_pattern})-([^-]+(?:-[^-]+)*)-(\d+)-(\d+)(?:\s*\*\s*(\d+))?\s*(?:cm)?\s+(\d+)$'
        )

    def normalize_material_name(self, material):
        """标准化材质名称"""
        if not material:
            return material
        material_lower = material.lower()
        if material_lower in self.material_case_mapping:
            return self.material_case_mapping[material_lower]
        parts = material.split('-')
        normalized_parts = []
        for part in parts:
            part_lower = part.lower()
            if part_lower in self.material_case_mapping:
                normalized_parts.append(self.material_case_mapping[part_lower])
            else:
                normalized_parts.append(part)
        return '-'.join(normalized_parts)

    def clean_pattern(self, pattern):
        """清理图案名称"""
        if not pattern:
            return pattern
        original_pattern = pattern
        pattern = pattern.strip()
        pattern = pattern.replace('·', '')
        pattern = re.sub(r'^[^\w\u4e00-\u9fa5]+', '', pattern).strip()

        material_match = self.material_patterns.search(pattern)
        if material_match:
            matched_part = material_match.group(0)
            pattern = pattern[len(matched_part):].strip()

        pattern = re.sub(r'^丨+', '', pattern).strip()
        pattern = re.sub(r'^(颜色分类?:[^-]+[-])', '', pattern)
        pattern = re.sub(r'^(颜色[:：])', '', pattern)
        pattern = re.sub(r'^颜色分类?[:：][^-]+[-]', '', pattern)
        pattern = re.sub(r'^颜色[:：][^-]+[-]', '', pattern)
        pattern = re.sub(r'^颜色分类?[:：][^;；]+[;；]', '', pattern)
        pattern = self.leading_symbols_pattern.sub('', pattern).strip()
        pattern = pattern.split(',')[0].strip()
        pattern = pattern.split('，')[0].strip()
        match = re.match(r'^([\u4e00-\u9fa5A-Za-z]+)[-\s]*([\dA-Za-z]+)?$', pattern)
        if match:
            main_pattern = match.group(1)
            suffix = match.group(2)
            if suffix:
                pattern = f"{main_pattern}{suffix}"
            else:
                pattern = main_pattern
        else:
            pattern = re.sub(r'[-\s]+', '', pattern)
        pattern = pattern.replace(' ', '')

        # 使用 remove_word_pattern 清洗图案名称
        if self.remove_word_pattern:
            original_after_basic = pattern
            prev_pattern = None
            while prev_pattern != pattern:
                prev_pattern = pattern
                pattern = self.remove_word_pattern.sub('', pattern)
                pattern = pattern.strip()
            pattern = re.sub(r'^[-丨]+|[-丨]+$', '', pattern).strip()
            if pattern != original_after_basic:
                log.debug(f"Remove words 清理: '{original_after_basic}' -> '{pattern}'")

        if not pattern:
            log.debug(f"清洗后图案名称为空，原始图案名称: '{original_pattern}'")
            return ''
        log.debug(f"清洗完成: '{original_pattern}' -> '{pattern}'")
        return pattern

    def is_result_anomalous(self, entry):
        """
        检查规则匹配结果是否异常：
          如果数量<=0或图案字段全部为数字，则认为异常。
        """
        try:
            quantity = int(entry.get('数量', 1))
            if quantity <= 0:
                return True
        except Exception:
            return True
        pattern = entry.get('图案', '').strip()
        if pattern.isdigit():
            return True
        # 检查整个图案字符串中是否存在汉字
        if re.search("[\u4e00-\u9fa5]", pattern) is None:
            return True
        return False

    def extract_tracking_number(self, text, openai_data=None):
        """
        从文本中提取快递单号。
        支持两种解析方式：
        1. 正则匹配：
           - SF开头的顺丰单号，例如：SF3156390969274
           - 纯数字的单号，长度为10-20位，例如：78885397777199
        2. OpenAI解析：
           - 如果提供了openai_data且包含express_num字段，优先使用该字段作为快递单号

        参数:
            text: 原始文本内容
            openai_data: OpenAI返回的解析结果字典（可选）

        返回值:
            tuple: (是否识别到单号, 单号字符串, 数量)
        """
        # 首先尝试从OpenAI解析结果中获取快递单号
        if openai_data and isinstance(openai_data, dict):
            express_num = openai_data.get('express_num', '').strip()
            if express_num and self.is_valid_tracking_number(express_num):
                # 如果OpenAI结果中有数量字段，使用它
                count = openai_data.get('count', None)
                if count is not None:
                    try:
                        count = int(count)
                    except (ValueError, TypeError):
                        count = None
                return True, express_num, count

        # 如果没有足够的内容或OpenAI解析失败，使用正则匹配
        if not text or len(text) < 5:
            return False, "无", None

        # 分离文本中间的空格，得到可能的单词序列
        parts = re.split(r'\s+', text.strip())

        # 如果只有少于2个部分，可能没有足够信息
        if len(parts) < 2:
            return False, "无", None

        # 检查最后一个部分是否可能是快递单号
        last_part = parts[-1].strip()
        second_last_part = parts[-2].strip() if len(parts) >= 2 else ""

        # 使用通用验证方法检查是否为有效的快递单号
        is_valid = self.is_valid_tracking_number(last_part)

        quantity = None
        # 如果倒数第二个部分是1-3位数字，可能是数量
        if second_last_part and re.match(r'^\d{1,3}$', second_last_part):
            try:
                quantity = int(second_last_part)
            except ValueError:
                quantity = None

        # 如果是有效的快递单号，返回
        if is_valid:
            return True, last_part, quantity

        return False, "无", None

    def is_valid_tracking_number(self, tracking_number):
        """
        验证快递单号是否符合预期格式

        参数:
            tracking_number: 待验证的快递单号

        返回值:
            bool: 是否为有效的快递单号
        """
        return Utils.is_valid_tracking_number(tracking_number)

    def _build_ai_parsed_entry(self, item, raw_line, product, pattern, width, length, count):
        """根据 OpenAI 返回的原始数据和转换规则，生成标准的解析结果"""
        if count <= 0:
            count = 1
        product_normalized = self.normalize_material_name(product)
        pattern_cleaned = self.clean_pattern(pattern)

        # 优化：确保宽度 > 高度，数值大的为宽，数值小的为高
        if width > 0 and length > 0 and width < length:
            width, length = length, width
            log.debug(f"自动纠正尺寸: 宽度({width}) < 高度({length})，已交换")

        # 提取可能的快递单号，现在传入OpenAI解析的item
        has_tracking, tracking_number, tracking_count = self.extract_tracking_number(raw_line, item)

        # 如果从文本中检测到数量，则覆盖AI解析的数量
        if tracking_count is not None:
            count = tracking_count

        # 修改：圆形图案全称不再包含直径信息
        if '直径' in raw_line:
            pattern_full_name = f"{pattern_cleaned}圆形"
        else:
            orientation = '竖版' if '竖' in raw_line else '横版'
            pattern_full_name = f"{pattern_cleaned}-{width}-{length}-{orientation}"

        entry = {
            '材质': product_normalized,
            '图案': pattern_cleaned,
            '宽cm': width,
            '高cm': length,  # 修改：长cm改为高cm
            '图案全称': pattern_full_name,
            '数量': count,
            '原始数据': raw_line,
            '处理方式': 'AI解析',
            '快递单号': tracking_number
        }
        return entry

    def parse_with_openai(self, raw_line, line_number=None, file_name=None):
        """
        使用OpenAI解析未匹配正则或规则匹配异常的数据，同步等待结果。
        新逻辑采用长期记忆方式与OpenAI接口交互，保证在并发环境下不混乱。

        当OpenAI解析数据错误或异常，或解析成功但宽、长均为空时，
        判定为解析失败，并返回包含详细错误信息的记录。

        优化内容：
        1. 增强错误恢复能力，更智能地处理不同类型的API错误
        2. 改进重试策略，针对不同错误类型采用不同的等待时间
        3. 增强JSON解析的健壮性，处理更多边缘情况
        4. 缓存机制优化，避免重复请求相同内容
        """
        # 检查缓存，避免重复请求
        if raw_line in self.openai_cache:
            log.info(f"使用缓存的OpenAI解析结果: {raw_line[:30]}...")
            return self.openai_cache[raw_line]

        config = self.openai_mgr.get_client_config() if self.openai_mgr else {}
        if not config:
            error_entry = {
                '原始数据': raw_line,
                '行号': line_number,
                '错误原因': 'OpenAI配置不可用',
                '错误日志': 'OpenAI客户端未初始化或配置缺失',
                '数据处理流程': 'AI解析',
                '解析错误': 'OpenAI配置不可用'
            }
            self.openai_cache[raw_line] = [error_entry]
            return [error_entry]

        # 辅助函数，用于处理OpenAI接口返回的内容，保证代码复用
        def process_response_content(response):
            try:
                content = response.choices[0].message.content
                # 移除可能的代码块标记
                content = content.replace('```json', '').replace('```', '').strip()

                # 处理可能的XML/HTML标记包装
                for tag_pair in [('<r>', '</r>'), ('<json>', '</json>'), ('<result>', '</result>')]:
                    if tag_pair[0] in content and tag_pair[1] in content:
                        result_match = re.search(f'{tag_pair[0]}\\s*([\\[\\{{].*?[\\]\\}}])\\s*{tag_pair[1]}', content, re.DOTALL)
                        if result_match:
                            content = result_match.group(1).strip()
                            break

                # 修复JSON字符串中的转义问题
                def escape_json_string(match):
                    inner = match.group(1)
                    # 处理控制字符和特殊字符
                    fixed = re.sub(r'[\x00-\x1F\x7F-\x9F]',
                                lambda m: m.group(0).encode('unicode_escape').decode('utf-8'),
                                inner)
                    return f"\"{fixed}\""

                # 应用JSON字符串修复
                content = re.sub(r'"((?:[^"\\]|\\.)*?)"', escape_json_string, content)

                # 处理可能的尾部逗号问题（非标准JSON）
                content = re.sub(r',\s*([}\]])', r'\1', content)

                log.debug(f"清理后的OpenAI JSON内容: {content}")
                self.openai_mgr.status_update.emit(f"OpenAI解析成功，返回数据: {content}")

                try:
                    result_json = json.loads(content)
                    if not isinstance(result_json, list):
                        result_json = [result_json]
                except json.JSONDecodeError as je:
                    # 尝试更激进的JSON修复
                    log.warning(f"标准JSON解析失败: {je}, 尝试修复...")
                    try:
                        # 移除所有注释
                        content = re.sub(r'//.*?$|/\*.*?\*/', '', content, flags=re.MULTILINE|re.DOTALL)
                        # 确保属性名有引号
                        content = re.sub(r'([{,])\s*([a-zA-Z0-9_]+)\s*:', r'\1"\2":', content)
                        result_json = json.loads(content)
                        if not isinstance(result_json, list):
                            result_json = [result_json]
                        log.info("JSON修复成功")
                    except Exception as fix_error:
                        log.error(f"JSON修复失败: {fix_error}")
                        raise ValueError(f"OpenAI返回的JSON格式无效且无法修复: {je}")

                parsed_results = []
                for item in result_json:
                    try:
                        product = item.get('product', '').strip()
                        pattern = item.get('pattern', '').strip()
                        try:
                            # 更健壮的数值转换
                            width = item.get('width', 0)
                            width = int(float(width)) if width else 0

                            length = item.get('length', 0)
                            length = int(float(length)) if length else 0

                            count = item.get('count', 1)
                            count = int(count) if count else 1
                            if count <= 0:
                                count = 1
                        except (ValueError, TypeError) as e:
                            log.warning(f"数值转换错误: {e}, 使用默认值")
                            width, length, count = 0, 0, 1

                        if not product or not pattern:
                            raise ValueError("缺少必要字段: 材质或图案")

                        if width == 0 and length == 0:
                            raise ValueError("OpenAI返回的JSON中高、宽均为空值")

                        # 优化：确保宽度 > 高度，数值大的为宽，数值小的为高
                        if width > 0 and length > 0 and width < length:
                            width, length = length, width
                            log.debug(f"自动纠正尺寸: 宽度({width}) < 高度({length})，已交换")

                        # 保持原有快递单号处理逻辑
                        express_num = item.get('express_num', '').strip()
                        if express_num:
                            # 标准化快递单号格式
                            # 1. 移除可能的前缀如"单号:"
                            express_num = re.sub(r'^(快递单号|单号|号码)\s*[:：]?\s*', '', express_num, flags=re.IGNORECASE)
                            # 2. 移除可能的空格和其他非法字符
                            express_num = re.sub(r'[^A-Za-z0-9]', '', express_num)
                            # 3. 如果以"SF"开头但不是大写，转换为大写
                            if express_num.lower().startswith('sf') and not express_num.startswith('SF'):
                                express_num = 'SF' + express_num[2:]

                            # 验证快递单号格式
                            if self.is_valid_tracking_number(express_num):
                                item['express_num'] = express_num
                                log.info(f"从OpenAI响应中提取到有效的快递单号: {express_num}")
                            else:
                                log.warning(f"OpenAI返回的快递单号格式无效: {express_num}")
                                item['express_num'] = '无'
                        else:
                            item['express_num'] = '无'

                        entry = self._build_ai_parsed_entry(item, raw_line, product, pattern, width, length, count)
                        parsed_results.append(entry)
                    except Exception as e_inner:
                        log.error(f"处理单个结果时出错: {e_inner}, 数据: {item}")
                        parsed_results.append({
                            '原始数据': raw_line,
                            '行号': line_number,
                            '错误原因': f"处理结果出错: {str(e_inner)}",
                            '错误日志': repr(e_inner),
                            '数据处理流程': 'AI解析',
                            '解析错误': f"处理结果出错: {str(e_inner)}"
                        })
                return parsed_results
            except Exception as process_error:
                log.error(f"处理OpenAI响应内容时出错: {process_error}")
                raise process_error

        max_attempts = 3
        last_exception = None
        backoff_times = [2, 5, 10]  # 基础退避时间（秒）

        # 前3次尝试
        for attempt in range(max_attempts):
            try:
                log_msg = f"OpenAI解析中 文件:{file_name} 行:{line_number} 尝试:{attempt+1}/{max_attempts}"
                self.openai_mgr.status_update.emit(log_msg)

                with self._lock:
                    response = self.openai_mgr.chat_completion(
                        raw_line,
                        force_system_reset=(attempt > 0)
                    )

                log.info(f"OpenAI接口返回: {response}")
                parsed_results = process_response_content(response)

                if parsed_results:
                    # 验证结果有效性
                    valid_results = [r for r in parsed_results if '解析错误' not in r]
                    if valid_results:
                        self.openai_cache[raw_line] = parsed_results
                        self.openai_mgr.status_update.emit(f"解析成功: {file_name} 行:{line_number}")
                        return parsed_results
                    else:
                        raise ValueError("解析结果无效，所有结果都包含错误")
                else:
                    raise ValueError("未能生成有效的解析结果")
            except Exception as e:
                last_exception = e
                err_msg = f"OpenAI解析失败 尝试:{attempt+1}/{max_attempts} 错误:{str(e)}"
                self.openai_mgr.status_update.emit(err_msg)
                log.error(f"{err_msg}, 原始数据: {raw_line}")

                # 智能退避策略
                backoff_time = backoff_times[min(attempt, len(backoff_times)-1)]

                # 针对不同类型的错误采用不同的退避策略
                if '429' in str(e) or 'rate limit' in str(e).lower():
                    # 限速错误，使用更长的退避时间
                    backoff_time = backoff_time * 2
                    self.openai_mgr.status_update.emit(f"遇到限速错误, 等待 {backoff_time} 秒后重试")
                elif 'timeout' in str(e).lower():
                    # 超时错误，可能是网络问题
                    backoff_time = backoff_time * 1.5
                    self.openai_mgr.status_update.emit(f"遇到超时错误, 等待 {backoff_time} 秒后重试")
                else:
                    # 其他错误
                    self.openai_mgr.status_update.emit(f"遇到错误, 等待 {backoff_time} 秒后重试")

                time.sleep(backoff_time)

        # 前3次重试后依然失败，进行最后一次尝试
        try:
            self.openai_mgr.status_update.emit("经过3次重试后仍失败, 最后再次尝试带 system role")
            time.sleep(15)  # 延长等待时间，缓解限速压力

            with self._lock:
                # 最后一次尝试使用更低的温度值，获得更确定性的结果
                response = self.openai_mgr.chat_completion(
                    f"请解析以下文本并提取材质、图案、宽度、高度和数量信息，以JSON格式返回: {raw_line}",  # 修改：长度改为高度
                    force_system_reset=True
                )

            log.info(f"OpenAI接口返回(最后尝试): {response}")
            parsed_results = process_response_content(response)

            if parsed_results:
                valid_results = [r for r in parsed_results if '解析错误' not in r]
                if valid_results:
                    self.openai_cache[raw_line] = parsed_results
                    self.openai_mgr.status_update.emit(f"解析成功(最后尝试): {file_name} 行:{line_number}")
                    return parsed_results
                else:
                    raise ValueError("最后尝试解析结果无效，所有结果都包含错误")
            else:
                raise ValueError("最后再次尝试未生成有效解析结果")
        except Exception as final_e:
            final_err_msg = f"OpenAI解析最终失败: {str(final_e)}"
            self.openai_mgr.status_update.emit(final_err_msg)
            log.error(f"{final_err_msg}, 原始数据: {raw_line}")

            error_entry = {
                '原始数据': raw_line,
                '行号': line_number,
                '错误原因': f"OpenAI解析失败: {str(final_e)}",
                '错误日志': repr(final_e),
                '数据处理流程': "OpenAI解析",
                '解析错误': f"OpenAI解析失败: {str(final_e)}"
            }
            self.openai_cache[raw_line] = [error_entry]
            return [error_entry]

    def parse_line(self, line, line_number=None, file_name=None, openai_log_callback=None):
        # 预处理：去除前后空白符
        original_line = line.strip()

        # 新增逻辑：清洗掉 '共' 之后的数据
        if '共' in original_line:
            original_line = original_line.split('共')[0].strip()

        # 新增: 检查"，"后的内容是否包含有效数据
        if '，' in original_line:
            parts = original_line.split('，')
            first_part = parts[0].strip()
            remaining_parts = [p.strip() for p in parts[1:] if p.strip()]
            valid_parts = [first_part]
            for part in remaining_parts:
                has_numbers = bool(re.search(r'\d', part))
                has_separators = len(self.separator_pattern.findall(part)) >= 2
                if has_numbers and has_separators:
                    valid_parts.append(part)
            original_line = '，'.join(valid_parts)

        # 当原始数据中存在全角分隔符 '；' 时，优先以 '；' 作为拆分条件
        if '；' in original_line:
            raw_sub_lines = original_line.split('；')
            sub_lines = []

            # 新增：处理数据补充逻辑
            for i, s in enumerate(raw_sub_lines):
                s_norm = s.strip()
                s_norm = self.leading_symbols_pattern.sub('', s_norm)
                s_norm = s_norm.rstrip(',;，。；#').strip()
                s_norm = (s_norm.replace('～', '-').replace('~', '-')
                        .replace('*', '-').replace('×', '-')
                        .replace('x', '-').replace('X', '-')
                        .replace('cm', '').replace('Cm', '').replace('CM', '').strip())

                # 检查是否缺少必要信息（例如材质和图案）
                parts = s_norm.split('-')
                if len(parts) >= 3 and not any(keyword in s_norm for keyword in self.material_keywords):
                    # 尝试从后续数据中补充信息
                    for next_s in raw_sub_lines[i+1:]:
                        next_norm = next_s.strip()
                        if any(keyword in next_norm for keyword in self.material_keywords):
                            # 提取材质和图案信息
                            material_pattern = re.search(r'([^-]+)-([^-]+)$', next_norm)
                            if material_pattern:
                                material_info = f"{material_pattern.group(1)}-{material_pattern.group(2)}"
                                s_norm = f"{s_norm}-{material_info}"
                                break
                if s_norm:
                    sub_lines.append(s_norm)
        else:
            # 原有逻辑：统一替换、再按多个符号拆分
            line = self.leading_symbols_pattern.sub('', original_line)
            line = line.rstrip(',;，。；#').strip()
            line = (line.replace('～', '-').replace('~', '-')
                        .replace('*', '-').replace('×', '-')
                        .replace('x', '-').replace('X', '-')
                        .replace('cm', '').replace('Cm', '').replace('CM', '').strip())
            sub_lines = re.split('[；;，,]', line)
            sub_lines = [s.strip() for s in sub_lines if s.strip()]

        # 去重，保持顺序
        sub_lines = list(dict.fromkeys(sub_lines))

        results = []
        for sub_line in sub_lines:
            # 检查子项是否至少包含两个分隔符（匹配半角 '-'、半角 '~'、全角 '～'）
            separator_count = len(self.separator_pattern.findall(sub_line))

            # 新增：检查原始子项中的所有分隔符（包括'-'、'~'、'*'）
            original_sub_line = sub_line
            total_separator_count = (
                original_sub_line.count('-') +
                original_sub_line.count('~') +
                original_sub_line.count('*') +
                original_sub_line.count('×') +
                original_sub_line.count('x', 0, len(original_sub_line)) +  # 只计算小写x
                original_sub_line.count('X', 0, len(original_sub_line))    # 只计算大写X
            )

            if separator_count < 2:
                results.append({
                    '原始数据': original_line,
                    '行号': line_number,
                    '错误原因': "数据不具备拆分条件，缺少必要的分隔符",
                    '错误日志': f"子项 '{sub_line}' 中检测到的分隔符数量不足（最少需要2个分隔符）",
                    '数据处理流程': '规则匹配',
                    '解析错误': "数据不具备拆分条件"
                })
                continue

            # 新增：当总分隔符数量大于等于4时，优先使用OpenAI解析
            if total_separator_count >= 4:
                if openai_log_callback:
                    openai_log_callback(f"检测到多分隔符case，优先使用AI解析:'{original_sub_line}'")
                openai_results = self.parse_with_openai(original_sub_line, line_number, file_name)

                if openai_results and isinstance(openai_results, list):
                    valid_results = [r for r in openai_results if '解析错误' not in r]
                    if valid_results:
                        results.extend(valid_results)
                        continue  # 成功解析，跳过后续规则匹配
                    # 如果OpenAI解析失败，继续尝试规则匹配
                    if openai_log_callback:
                        openai_log_callback(f"OpenAI优先解析未返回有效结果，尝试规则匹配: '{original_sub_line}'")

            # 1. 匹配S波斯绒格式
            match = self.s_persian_pattern.match(sub_line)
            if match:
                try:
                    pattern = self.clean_pattern(match.group(1))
                    entry = {
                        '材质': '波斯绒',
                        '图案': pattern,
                        '宽cm': 0,
                        '高cm': 0,  # 修改：长cm改为高cm
                        '图案全称': f"{pattern}-0-0-横版",
                        '数量': 1,
                        '累计长度': 0,
                        '原始数据': original_line,
                        '处理方式': '规则匹配'
                    }
                    # 统一调用辅助方法检查是否异常
                    results.extend(self.process_local_entry(sub_line, entry, original_line, openai_log_callback, line_number, file_name))
                    continue
                except Exception as e:
                    log.error(f"S波斯绒格式解析错误: {e}")
                    results.append({
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': f"S波斯绒格式解析错误: {str(e)}",
                        '错误日志': repr(e),
                        '数据处理流程': '规则匹配',
                        '解析错误': f"S波斯绒格式解析错误: {str(e)}"
                    })
                    continue

            # 2. 匹配DLDM2M特殊格式
            match = self.dldm_special_pattern.match(sub_line)
            if match:
                try:
                    width = int(match.group(1))
                    length = int(match.group(2))
                    quantity = int(match.group(3))
                    pattern = self.clean_pattern(match.group(4))

                    # 优化：确保宽度 > 高度，数值大的为宽，数值小的为高
                    if width > 0 and length > 0 and width < length:
                        width, length = length, width
                        log.debug(f"DLDM2M格式自动纠正尺寸: 已交换为宽{width}x高{length}")

                    orientation = '竖版' if '竖' in original_line else '横版'
                    entry = {
                        '材质': 'DLDM2M',
                        '图案': pattern,
                        '宽cm': width,
                        '高cm': length,  # 修改：长cm改为高cm
                        '图案全称': f"{pattern}-{width}-{length}-{orientation}",
                        '数量': quantity,
                        '累计长度': (length + 1) * quantity,
                        '原始数据': original_line,
                        '处理方式': '规则匹配'
                    }
                    results.extend(self.process_local_entry(sub_line, entry, original_line, openai_log_callback, line_number, file_name))
                    continue
                except Exception as e:
                    log.error(f"DLDM2M特殊格式解析错误: {e}")
                    results.append({
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': f"DLDM2M格式错误: {str(e)}",
                        '错误日志': repr(e),
                        '数据处理流程': '规则匹配',
                        '解析错误': f"DLDM2M格式错误: {str(e)}"
                    })
                    continue

            # 3. 匹配印花尺寸模式
            match = self.print_size_qty_pattern.match(sub_line)
            if match:
                try:
                    material = self.normalize_material_name(match.group(1))
                    width = int(match.group(2))
                    length = int(match.group(3))
                    quantity = int(match.group(4))
                    pattern = self.clean_pattern(match.group(1).split('-')[-1])

                    # 优化：确保宽度 > 高度，数值大的为宽，数值小的为高
                    if width > 0 and length > 0 and width < length:
                        width, length = length, width
                        log.debug(f"印花尺寸模式自动纠正尺寸: 已交换为宽{width}x高{length}")

                    orientation = '竖版' if '竖' in original_line else '横版'
                    entry = {
                        '材质': material,
                        '图案': pattern,
                        '宽cm': width,
                        '高cm': length,  # 修改：长cm改为高cm
                        '图案全称': f"{pattern}-{width}-{length}-{orientation}",
                        '数量': quantity,
                        '累计长度': (length + 1) * quantity,
                        '原始数据': original_line,
                        '处理方式': '规则匹配'
                    }
                    results.extend(self.process_local_entry(sub_line, entry, original_line, openai_log_callback, line_number, file_name))
                    continue
                except Exception as e:
                    log.error(f"印花尺寸模式解析错误: {e}")
                    results.append({
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': f"印花尺寸格式错误: {str(e)}",
                        '错误日志': repr(e),
                        '数据处理流程': '规则匹配',
                        '解析错误': f"印花尺寸格式错误: {str(e)}"
                    })
                    continue

            # 4. 增强QRMB格式处理
            match = self.qrmb_pattern.match(sub_line)
            if match:
                try:
                    material = match.group(1).replace('@', '-')
                    width = int(float(match.group(2)))
                    length = int(float(match.group(3)))
                    quantity = int(match.group(4))
                    sub_material = match.group(5).strip()
                    additional_info = match.group(6).strip() if match.group(6) else ''
                    pattern = self.clean_pattern(sub_material)
                    if additional_info:
                        pattern += f"-{additional_info}"

                    # 优化：确保宽度 > 高度，数值大的为宽，数值小的为高
                    if width > 0 and length > 0 and width < length:
                        width, length = length, width
                        log.debug(f"QRMB格式自动纠正尺寸: 已交换为宽{width}x高{length}")

                    orientation = '竖版' if '竖' in original_line else '横版'
                    entry = {
                        '材质': material,
                        '图案': pattern,
                        '宽cm': width,
                        '高cm': length,  # 修改：长cm改为高cm
                        '图案全称': f"{pattern}-{width}-{length}-{orientation}",
                        '数量': quantity,
                        '累计长度': (length + 1) * quantity,
                        '原始数据': original_line,
                        '处理方式': '规则匹配'
                    }
                    results.extend(self.process_local_entry(sub_line, entry, original_line, openai_log_callback, line_number, file_name))
                    continue
                except Exception as e:
                    log.error(f"QRMB格式解析错误: {e}")
                    results.append({
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': f"QRMB格式错误: {str(e)}",
                        '错误日志': repr(e),
                        '数据处理流程': '规则匹配',
                        '解析错误': f"QRMB格式错误: {str(e)}"
                    })
                    continue

            # 5. 增强QRMB3M格式处理
            match = self.qrmb3m_pattern.match(sub_line)
            if match:
                try:
                    material = self.normalize_material_name(match.group(1))
                    width = int(match.group(2))
                    length = int(match.group(3))
                    quantity = int(match.group(4))
                    pattern_raw = match.group(5)
                    if match.group(6):  # 处理附加信息
                        pattern_raw += f"-{match.group(6)}"
                    pattern = self.clean_pattern(pattern_raw)

                    # 优化：确保宽度 > 高度，数值大的为宽，数值小的为高
                    if width > 0 and length > 0 and width < length:
                        width, length = length, width
                        log.debug(f"QRMB3M格式自动纠正尺寸: 已交换为宽{width}x高{length}")

                    orientation = '竖版' if '竖' in original_line else '横版'
                    entry = {
                        '材质': material,
                        '图案': pattern,
                        '宽cm': width,
                        '高cm': length,  # 修改：长cm改为高cm
                        '图案全称': f"{pattern}-{width}-{length}-{orientation}",
                        '数量': quantity,
                        '累计长度': (length + 1) * quantity,
                        '原始数据': original_line,
                        '处理方式': '规则匹配'
                    }
                    results.extend(self.process_local_entry(sub_line, entry, original_line, openai_log_callback, line_number, file_name))
                    continue
                except Exception as e:
                    log.error(f"QRMB3M格式解析错误: {e}")
                    results.append({
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': f"QRMB3M格式错误: {str(e)}",
                        '错误日志': repr(e),
                        '数据处理流程': '规则匹配',
                        '解析错误': f"QRMB3M格式错误: {str(e)}"
                    })
                    continue

            # 6. 匹配DLDM格式
            match = self.dldm_pattern.match(sub_line)
            if match:
                try:
                    material = self.normalize_material_name(match.group(1).strip())
                    width = int(match.group(2))
                    length = int(match.group(3))
                    quantity = int(match.group(4))
                    pattern_raw = match.group(5).strip()
                    pattern = self.clean_pattern(pattern_raw)

                    # 优化：确保宽度 > 高度，数值大的为宽，数值小的为高
                    if width > 0 and length > 0 and width < length:
                        width, length = length, width
                        log.debug(f"DLDM格式自动纠正尺寸: 已交换为宽{width}x高{length}")

                    orientation = '竖版' if '竖' in original_line else '横版'
                    pattern_full_name = f"{pattern}-{width}-{length}-{orientation}"
                    cumulative_length = (length + 1) * quantity
                    entry = {
                        '材质': material,
                        '图案': pattern,
                        '宽cm': width,
                        '高cm': length,  # 修改：长cm改为高cm
                        '图案全称': pattern_full_name,
                        '数量': quantity,
                        '累计长度': cumulative_length,
                        '版型': orientation,
                        '原始数据': original_line,
                        '处理方式': '规则匹配'
                    }
                    if self.is_result_anomalous(entry):
                        if openai_log_callback:
                            openai_log_callback(
                                f"规则匹配异常(数量为 {quantity} 或 图案为 '{pattern}')，使用OpenAI重新解析: 文件 {file_name} 行 {line_number}：'{sub_line}'"
                            )
                        openai_results = self.parse_with_openai(sub_line, line_number, file_name)
                        if openai_results and any('解析错误' not in r for r in openai_results):
                            results.extend([r for r in openai_results if '解析错误' not in r])
                        else:
                            results.append({
                                '原始数据': original_line,
                                '行号': line_number,
                                '错误原因': f"规则匹配异常，且OpenAI解析未返回有效结果：数量 {quantity}，图案 '{pattern}'",
                                '错误日志': "规则匹配异常",
                                '数据处理流程': '规则匹配',
                                '解析错误': f"规则匹配异常：数量 {quantity}，图案 '{pattern}'"
                            })
                    else:
                        results.extend(self.process_local_entry(sub_line, entry, original_line, openai_log_callback, line_number, file_name))
                    continue
                except (IndexError, ValueError) as e:
                    log.error(f"文件：{file_name}, 行号：{line_number} 解析失败，通用格式错误：{e}")
                    results.append({
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': f"通用格式错误：{e}",
                        '错误日志': repr(e),
                        '数据处理流程': '规则匹配',
                        '解析错误': f"通用格式错误：{e}"
                    })
                    continue

            # 新增扩展直径格式匹配（支持额外描述文本，如"原创设计"）
            match = self.circle_extended_pattern.match(sub_line)
            if match:
                try:
                    material = self.normalize_material_name(match.group(1).strip())
                    pattern_raw = match.group(2).strip()
                    diameter = int(match.group(3))
                    quantity = int(match.group(4))
                    width = diameter
                    length = diameter
                    pattern = self.clean_pattern(pattern_raw)
                    pattern_full_name = f"{pattern}圆形"
                    cumulative_length = (length + 1) * quantity
                    entry = {
                        '材质': material,
                        '图案': pattern,
                        '宽cm': width,
                        '高cm': length,  # 修改：长cm改为高cm
                        '图案全称': pattern_full_name,
                        '数量': quantity,
                        '累计长度': cumulative_length,
                        '版型': '圆形',
                        '原始数据': original_line,
                        '处理方式': '规则匹配'
                    }
                    if self.is_result_anomalous(entry):
                        if openai_log_callback:
                            openai_log_callback(
                                f"规则匹配异常(数量为 {quantity} 或 图案为 '{pattern}')，使用OpenAI重新解析: 文件 {file_name} 行 {line_number}：'{sub_line}'"
                            )
                        openai_results = self.parse_with_openai(sub_line, line_number, file_name)
                        if openai_results and any('解析错误' not in r for r in openai_results):
                            results.extend([r for r in openai_results if '解析错误' not in r])
                        else:
                            results.append({
                                '原始数据': original_line,
                                '行号': line_number,
                                '错误原因': f"直径格式异常，且OpenAI解析未返回有效结果：数量 {quantity}，图案 '{pattern}'",
                                '错误日志': "直径扩展格式解析异常",
                                '数据处理流程': '规则匹配',
                                '解析错误': f"直径扩展格式异常：数量 {quantity}，图案 '{pattern}'"
                            })
                    else:
                        results.extend(self.process_local_entry(sub_line, entry, original_line, openai_log_callback, line_number, file_name))
                    continue
                except (IndexError, ValueError) as e:
                    log.error(f"文件：{file_name}, 行号：{line_number} 解析失败，直径扩展格式错误：{e}")
                    results.append({
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': f"直径扩展格式错误：{e}",
                        '错误日志': repr(e),
                        '数据处理流程': '规则匹配',
                        '解析错误': f"直径扩展格式错误：{e}"
                    })
                    continue

            # 7. 匹配直径格式
            match = self.diameter_pattern.match(sub_line)
            if match:
                try:
                    material = self.normalize_material_name(match.group(1).strip())
                    pattern_raw = match.group(2).strip()
                    diameter = int(match.group(3))
                    quantity = int(match.group(4))
                    width = diameter
                    length = diameter
                    pattern = self.clean_pattern(pattern_raw)
                    pattern_full_name = f"{pattern}圆形"
                    cumulative_length = (length + 1) * quantity
                    entry = {
                        '材质': material,
                        '图案': pattern,
                        '宽cm': width,
                        '高cm': length,  # 修改：长cm改为高cm
                        '图案全称': pattern_full_name,
                        '数量': quantity,
                        '累计长度': cumulative_length,
                        '版型': '圆形',
                        '原始数据': original_line,
                        '处理方式': '规则匹配'
                    }
                    if self.is_result_anomalous(entry):
                        if openai_log_callback:
                            openai_log_callback(
                                f"规则匹配异常(数量为 {quantity} 或 图案为 '{pattern}')，使用OpenAI重新解析: 文件 {file_name} 行 {line_number}：'{sub_line}'"
                            )
                        openai_results = self.parse_with_openai(sub_line, line_number, file_name)
                        if openai_results and any('解析错误' not in r for r in openai_results):
                            results.extend([r for r in openai_results if '解析错误' not in r])
                        else:
                            results.append({
                                '原始数据': original_line,
                                '行号': line_number,
                                '错误原因': f"直径格式异常，且OpenAI解析未返回有效结果：数量 {quantity}，图案 '{pattern}'",
                                '错误日志': "直径格式解析异常",
                                '数据处理流程': '规则匹配',
                                '解析错误': f"直径格式异常：数量 {quantity}，图案 '{pattern}'"
                            })
                    else:
                        results.extend(self.process_local_entry(sub_line, entry, original_line, openai_log_callback, line_number, file_name))
                    continue
                except (IndexError, ValueError) as e:
                    log.error(f"文件：{file_name}, 行号：{line_number} 解析失败，直径格式错误：{e}")
                    results.append({
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': f"直径格式错误：{e}",
                        '错误日志': repr(e),
                        '数据处理流程': '规则匹配',
                        '解析错误': f"直径格式错误：{e}"
                    })
                    continue

            # 新增：匹配ZPTQ格式，格式为 材质～宽*长～图案； 默认数量为1
            match = self.zptq_pattern.match(sub_line)
            if match:
                try:
                    material = self.normalize_material_name(match.group(1).strip())
                    width = int(match.group(2))
                    length = int(match.group(3))
                    pattern = self.clean_pattern(match.group(4).strip())
                    quantity = 1  # 默认数量

                    # 优化：确保宽度 > 高度，数值大的为宽，数值小的为高
                    if width > 0 and length > 0 and width < length:
                        width, length = length, width
                        log.debug(f"ZPTQ格式自动纠正尺寸: 已交换为宽{width}x高{length}")

                    orientation = '竖版' if '竖' in original_line else '横版'
                    pattern_full_name = f"{pattern}-{width}-{length}-{orientation}"
                    cumulative_length = (length + 1) * quantity
                    entry = {
                        '材质': material,
                        '图案': pattern,
                        '宽cm': width,
                        '高cm': length,  # 修改：长cm改为高cm
                        '图案全称': pattern_full_name,
                        '数量': quantity,
                        '累计长度': cumulative_length,
                        '原始数据': original_line,
                        '处理方式': '规则匹配'
                    }
                    results.extend(self.process_local_entry(sub_line, entry, original_line, openai_log_callback, line_number, file_name))
                    continue
                except Exception as e:
                    log.error(f"ZPTQ格式解析错误: {e}")
                    results.append({
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': f"ZPTQ格式解析错误: {str(e)}",
                        '错误日志': repr(e),
                        '数据处理流程': '规则匹配',
                        '解析错误': f"ZPTQ格式解析错误: {str(e)}"
                    })
                    continue

            # 8. 匹配普通格式
            match = self.normal_pattern.match(sub_line)
            if match:
                try:
                    material = self.normalize_material_name(match.group(1).strip())
                    pattern = self.clean_pattern(match.group(2).strip())
                    width = int(match.group(3))
                    length = int(match.group(4))
                    quantity = int(match.group(5)) if match.group(5) and match.group(5).isdigit() and int(match.group(5)) > 0 else 1
                    orientation = match.group(6).strip() if match.group(6) else '横版'

                    # 优化：确保宽度 > 高度，数值大的为宽，数值小的为高
                    if width > 0 and length > 0 and width < length:
                        width, length = length, width
                        log.debug(f"通用格式自动纠正尺寸: 已交换为宽{width}x高{length}")

                    pattern_full_name = f"{pattern}-{width}-{length}-{orientation}"
                    cumulative_length = (length + 1) * quantity
                    entry = {
                        '材质': material,
                        '图案': pattern,
                        '宽cm': width,
                        '高cm': length,  # 修改：长cm改为高cm
                        '图案全称': pattern_full_name,
                        '数量': quantity,
                        '累计长度': cumulative_length,
                        '版型': orientation,
                        '原始数据': original_line,
                        '处理方式': '规则匹配'
                    }
                    if self.is_result_anomalous(entry):
                        if openai_log_callback:
                            openai_log_callback(
                                f"规则匹配异常(数量为 {quantity} 或 图案为 '{pattern}')，使用OpenAI重新解析: 文件 {file_name} 行 {line_number}：'{sub_line}'"
                            )
                        openai_results = self.parse_with_openai(sub_line, line_number, file_name)
                        if openai_results and any('解析错误' not in r for r in openai_results):
                            results.extend([r for r in openai_results if '解析错误' not in r])
                        else:
                            results.append({
                                '原始数据': original_line,
                                '行号': line_number,
                                '错误原因': f"普通格式异常且OpenAI解析未返回有效结果：数量 {quantity}，图案 '{pattern}'",
                                '错误日志': "普通格式异常",
                                '数据处理流程': '规则匹配',
                                '解析错误': f"普通格式异常：数量 {quantity}，图案 '{pattern}'"
                            })
                    else:
                        results.extend(self.process_local_entry(sub_line, entry, original_line, openai_log_callback, line_number, file_name))
                    continue
                except (IndexError, ValueError) as e:
                    log.error(f"文件：{file_name}, 行号：{line_number} 解析失败，普通格式错误：{e}")
                    results.append({
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': f"普通格式错误：{e}",
                        '错误日志': repr(e),
                        '数据处理流程': '规则匹配',
                        '解析错误': f"普通格式错误：{e}"
                    })
                    continue

            # 匹配材质-图案-尺寸格式
            match = self.material_pattern_size.match(sub_line)
            if match:
                try:
                    material = match.group(1)  # 材质
                    pattern_raw = match.group(2)  # 原始图案部分
                    width = int(match.group(3))
                    length = int(match.group(4))
                    if match.group(5):  # 处理可能存在的第二个尺寸
                        length = int(match.group(5))
                    quantity = int(match.group(6))

                    # 清理并合并图案名称
                    pattern = self.clean_pattern(pattern_raw)
                    orientation = '竖版' if '竖' in original_line else '横版'

                    # 优化：确保宽度 > 高度，数值大的为宽，数值小的为高
                    if width > 0 and length > 0 and width < length:
                        width, length = length, width
                        log.debug(f"材质模式自动纠正尺寸: 已交换为宽{width}x高{length}")

                    entry = {
                        '材质': material,
                        '图案': pattern,
                        '宽cm': width,
                        '高cm': length,  # 修改：长cm改为高cm
                        '图案全称': f"{pattern}-{width}-{length}-{orientation}",
                        '数量': quantity,
                        '累计长度': (length + 1) * quantity,
                        '原始数据': original_line,
                        '处理方式': '规则匹配'
                    }

                    if self.is_result_anomalous(entry):
                        if openai_log_callback:
                            openai_log_callback(
                                f"规则匹配异常(数量为 {quantity} 或 图案为 '{pattern}')，使用OpenAI重新解析: 文件 {file_name} 行 {line_number}：'{sub_line}'"
                            )
                        openai_results = self.parse_with_openai(sub_line, line_number, file_name)
                        if openai_results and any('解析错误' not in r for r in openai_results):
                            results.extend([r for r in openai_results if '解析错误' not in r])
                        else:
                            results.append({
                                '原始数据': original_line,
                                '行号': line_number,
                                '错误原因': f"材质图案格式异常，且OpenAI解析未返回有效结果：数量 {quantity}，图案 '{pattern}'",
                                '错误日志': "材质图案格式解析异常",
                                '数据处理流程': '规则匹配',
                                '解析错误': f"材质图案格式异常：数量 {quantity}，图案 '{pattern}'"
                            })
                    else:
                        results.extend(self.process_local_entry(sub_line, entry, original_line, openai_log_callback, line_number, file_name))
                    continue
                except Exception as e:
                    log.error(f"材质图案格式解析错误: {e}")
                    results.append({
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': f"材质图案格式错误: {str(e)}",
                        '错误日志': repr(e),
                        '数据处理流程': '规则匹配',
                        '解析错误': f"材质图案格式错误: {str(e)}"
                    })
                    continue

            # 尝试预处理特定格式的数据，如果成功则不需要调用OpenAI
            try:
                # 检查是否符合"产品内容 数量 快递单号"的格式
                # 支持空格和制表符作为分隔符
                parts = re.split(r'[\s\t]+', sub_line.strip(), maxsplit=2)

                if len(parts) >= 2:  # 至少有产品内容和数量
                    product_part = parts[0]
                    quantity_part = parts[1]
                    express_num = parts[2] if len(parts) > 2 else ""

                    log.info(f"预处理分割: 产品部分='{product_part}', 数量='{quantity_part}', 快递单号='{express_num}'")

                    # 验证数量是否为数字
                    if quantity_part.isdigit() and int(quantity_part) > 0:
                        # 按'-'分割产品部分
                        product_elements = product_part.split('-')
                        log.info(f"产品元素分割: {product_elements}")

                        if len(product_elements) >= 2:  # 至少有材质和一个其他元素
                            material = product_elements[0].strip()

                            # 查找宽度和高度  # 修改：长度改为高度
                            width = 0
                            length = 0
                            pattern_elements = []

                            # 检查是否是圆形图案（包含"直径"关键字）
                            is_circular = False
                            diameter_value = 0
                            diameter_element_index = -1

                            # 第一步：检查是否有直径元素，并记录其索引
                            for i, element in enumerate(product_elements[1:], 1):  # 从1开始，因为0是材质
                                element = element.strip()
                                if '直径' in element:
                                    is_circular = True
                                    diameter_element_index = i
                                    log.info(f"检测到直径元素: {element}, 索引: {i}")
                                    # 提取直径后的数字
                                    diameter_match = re.search(r'直径(\d+)', element)
                                    if diameter_match:
                                        try:
                                            diameter_value = int(diameter_match.group(1))
                                            log.info(f"找到直径值: {diameter_value}")
                                            # 对于圆形，宽度和高度都等于直径值  # 修改：长度改为高度
                                            width = diameter_value
                                            length = diameter_value
                                        except ValueError:
                                            log.warning(f"直径值转换失败: {diameter_match.group(1)}")
                                    break  # 找到直径元素后退出循环

                            # 第二步：处理所有非直径元素
                            for i, element in enumerate(product_elements[1:], 1):  # 从1开始，因为0是材质
                                element = element.strip()

                                # 跳过直径元素
                                if i == diameter_element_index:
                                    log.info(f"跳过直径元素: {element}, 索引: {i}")
                                    continue

                                # 处理非直径元素
                                if element.isdigit() or (element.replace('.', '', 1).isdigit() and element.count('.') <= 1):
                                    # 是数字，可能是宽度或高度（如果不是圆形）  # 修改：长度改为高度
                                    if not is_circular:  # 只有非圆形才处理数字作为宽度和高度  # 修改：长度改为高度
                                        num_val = int(float(element))
                                        if width == 0:
                                            width = num_val
                                            log.info(f"找到宽度: {width}")
                                        elif length == 0:
                                            length = num_val
                                            log.info(f"找到高度: {length}")  # 修改：长度改为高度
                                    else:
                                        log.info(f"圆形图案，忽略数字元素: {element}")
                                else:
                                    # 非数字，可能是图案的一部分
                                    pattern_elements.append(element)
                                    log.info(f"添加图案元素: {element}")

                            # 构建图案 - 所有非数字元素组成图案
                            pattern = '-'.join(pattern_elements) if pattern_elements else "无图案"
                            log.info(f"构建图案: {pattern}")

                            # 对于圆形图案，如果通过直径元素没有设置宽度和高度，检查是否有其他数字可以用作直径  # 修改：长度改为高度
                            if ('直径' in product_part or '圆形' in product_part) and not is_circular:
                                is_circular = True
                                log.info("检测到圆形图案关键字，但未找到直径元素")
                                if width == 0 and length == 0:
                                    # 查找任何可能的数字作为直径
                                    for element in product_elements[1:]:
                                        element = element.strip()
                                        if element.isdigit() or (element.replace('.', '', 1).isdigit() and element.count('.') <= 1):
                                            diameter_value = int(float(element))
                                            width = diameter_value
                                            length = diameter_value
                                            log.info(f"圆形图案，使用数字作为直径: {diameter_value}")
                                            break

                            # 构建解析结果
                            quantity = int(quantity_part)

                            # 验证快递单号
                            if express_num:
                                # 清理快递单号中可能的非法字符
                                express_num = re.sub(r'[^A-Za-z0-9]', '', express_num)
                                # 如果以"sf"开头但不是大写，转换为大写
                                if express_num.lower().startswith('sf') and not express_num.startswith('SF'):
                                    express_num = 'SF' + express_num[2:]

                                if self.is_valid_tracking_number(express_num):
                                    tracking_number = express_num
                                    log.info(f"有效的快递单号: {tracking_number}")
                                else:
                                    tracking_number = ""
                                    log.warning(f"无效的快递单号: {express_num}")
                            else:
                                tracking_number = ""

                            # 只有当我们至少有材质、图案和宽度时才构建结果
                            if material and (pattern != "无图案" or is_circular) and width > 0:
                                # 优化：确保宽度 > 高度，数值大的为宽，数值小的为高（圆形除外）
                                if not is_circular and width > 0 and length > 0 and width < length:
                                    width, length = length, width
                                    log.debug(f"预处理自动纠正尺寸: 已交换为宽{width}x高{length}")

                                # 计算累计长度
                                cumulative_length = (length + 1) * quantity

                                # 确定版型
                                version_type = '圆形' if is_circular else ('竖版' if '竖' in product_part else '横版')

                                # 规范化材质和图案
                                normalized_material = self.normalize_material_name(material)
                                cleaned_pattern = self.clean_pattern(pattern)

                                # 构建图案全称
                                if is_circular:
                                    pattern_full_name = f"{cleaned_pattern}圆形"
                                else:
                                    pattern_full_name = f"{cleaned_pattern}-{width}-{length}-{version_type}"

                                # 构建结果项
                                item = {
                                    'product': material,
                                    'pattern': pattern,
                                    'width': str(width),
                                    'length': str(length),
                                    'count': str(quantity),
                                    'express_num': tracking_number,
                                    'raw_data': sub_line
                                }

                                log.info(f"预处理构建结果项: {item}")

                                # 构建标准格式的结果，设置处理方式为"规则匹配"
                                entry = {
                                    '材质': normalized_material,
                                    '图案': cleaned_pattern,
                                    '宽cm': width,
                                    '高cm': length,  # 修改：长cm改为高cm
                                    '图案全称': pattern_full_name,
                                    '数量': quantity,
                                    '原始数据': sub_line,
                                    '处理方式': '规则匹配',  # 设置为规则匹配而非AI解析
                                    '快递单号': tracking_number,
                                    '累计长度': cumulative_length,
                                    '版型': version_type
                                }

                                if openai_log_callback:
                                    openai_log_callback(f"预处理成功解析: {entry}")

                                log.info(f"预处理成功解析: {sub_line}")
                                results.append(entry)
                                continue  # 跳过OpenAI解析
                            else:
                                log.warning(f"预处理缺少必要字段: 材质='{material}', 图案='{pattern}', 宽度={width}, 高度={length}, 是否圆形={is_circular}")  # 修改：添加高度信息
            except Exception as e:
                log.warning(f"预处理解析失败，将使用OpenAI解析: {e}")
                # 预处理失败，继续使用OpenAI解析

            # 最后处理未匹配的情况，使用OpenAI解析
            if openai_log_callback:
                openai_log_callback(f"调用OpenAI解析: 文件 {file_name}, 行号 {line_number}: '{sub_line}'")

            # 如果预处理失败，使用OpenAI解析
            openai_results = self.parse_with_openai(sub_line, line_number, file_name)

            if openai_results and isinstance(openai_results, list):
                valid_results = [r for r in openai_results if '解析错误' not in r]
                if valid_results:
                    results.extend(valid_results)
                else:
                    results.append({
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': "OpenAI解析未返回有效数据",
                        '错误日志': f"子项 '{sub_line}'",
                        '数据处理流程': 'AI解析',
                        '解析错误': "OpenAI解析未返回有效数据"
                    })

        if results:
            # 处理圆形图案标记
            for entry in results:
                if '直径' in entry.get('原始数据', ''):
                    cleaned_pattern = entry.get('图案', '').strip()
                    width = entry.get('宽cm', 0)
                    entry['图案全称'] = f"{cleaned_pattern}圆形"

            # 先按宽度和高度排序
            sorted_results = sorted(
                [entry for entry in results if '解析错误' not in entry],
                key=lambda x: (-x['宽cm'], -x['高cm'])  # 修改：长cm改为高cm
            )

            # 合并相同图案（考虑圆形图案的特殊处理）
            if sorted_results:
                merged_results = self.merge_same_patterns(sorted_results)
                return merged_results
            return results
        else:
            log.debug(f"文件 {file_name}, 行号 {line_number}: 没有解析到任何有效数据。")
            return []

    def merge_same_patterns(self, results):
        """合并相同图案的记录，对于圆形图案需要考虑直径"""
        if not results:
            return results

        merged = []
        pattern_dict = {}

        for entry in results:
            if '解析错误' in entry:
                merged.append(entry)
                continue

            material = entry.get('材质', '')
            pattern = entry.get('图案', '')
            pattern_full = entry.get('图案全称', '')
            width = entry.get('宽cm', 0)

            # 构建合并的key
            if '圆形' in pattern_full:
                # 对于圆形图案，使用"图案全称-直径"作为key
                key = f"{material}-{pattern_full}"
            else:
                # 非圆形图案保持原有逻辑
                key = f"{material}-{pattern_full}"

            if key in pattern_dict:
                # 更新已存在记录的数量和累计长度
                existing = pattern_dict[key]
                existing['数量'] += entry.get('数量', 0)
                existing['累计长度'] += entry.get('累计长度', 0)
                # 合并原始数据，用分号分隔
                if entry.get('原始数据'):
                    existing['原始数据'] = f"{existing['原始数据']};{entry['原始数据']}"
            else:
                pattern_dict[key] = entry.copy()

        merged.extend(pattern_dict.values())

        # 按宽度降序排序
        merged.sort(key=lambda x: (-x.get('宽cm', 0), -x.get('高cm', 0)))  # 修改：长cm改为高cm
        return merged

    def process_local_entry(self, sub_line, entry, original_line, openai_log_callback, line_number, file_name):
        """
        辅助方法：检查本地规则解析结果 entry 是否异常。
        如果异常，则调用 OpenAI 进行补救解析；否则返回原始 entry。

        返回值为列表，不论直接返回单一 entry 还是 OpenAI 解析的结果，都保持调用方的一致接口。
        """
        if self.is_result_anomalous(entry):
            if openai_log_callback:
                openai_log_callback(
                    f"规则匹配异常(数量: {entry.get('数量')} 或 图案: '{entry.get('图案')}')，使用OpenAI重新解析: 文件 {file_name} 行 {line_number}：'{sub_line}'"
                )
            openai_results = self.parse_with_openai(sub_line, line_number, file_name)
            if openai_results and any('解析错误' not in r for r in openai_results):
                return [r for r in openai_results if '解析错误' not in r]
            else:
                return [{
                    '原始数据': original_line,
                    '行号': line_number,
                    '错误原因': f"规则匹配异常：数量 {entry.get('数量')}，图案 '{entry.get('图案')}'",
                    '错误日志': "规则匹配异常",
                    '数据处理流程': entry.get('处理方式', '规则匹配'),
                    '解析错误': f"规则匹配异常：数量 {entry.get('数量')}，图案 '{entry.get('图案')}'"
                }]
        else:
            # 提取快递单号并添加到entry中
            has_tracking, tracking_number, tracking_count = self.extract_tracking_number(original_line)

            # 如果从文本中检测到数量，则覆盖正则解析的数量
            if tracking_count is not None:
                entry['数量'] = tracking_count

            # 添加快递单号字段
            entry['快递单号'] = tracking_number

            return [entry]

# ---------------------------
# 工具类
# ---------------------------
class Utils:
    @staticmethod
    def fetch_texture_categories():
        """
        从kufang_texture_category表获取base_texture和width_arr数据。
        使用认证客户端以支持RLS策略。

        返回:
            dict: {
                'base_texture1': {
                    'sub_textures': [...],
                    'width_arr': [160, 200, 240, 300] 或 None
                },
                'base_texture2': {
                    'sub_textures': [...],
                    'width_arr': None
                },
                ...
            }
        """
        try:
            # 获取认证客户端
            auth_client = get_authenticated_client()

            # 使用认证客户端查询数据
            response = auth_client.table('kufang_texture_category').select('base_texture, texture_arr, width_arr').execute()
            if not response.data:
                log.error("无法从kufang_texture_category获取数据。")
                return {}

            texture_dict = {}
            for item in response.data:
                base_texture = item.get('base_texture', '').strip()
                texture_arr = item.get('texture_arr', '')
                sub_textures = [tex.strip() for tex in texture_arr.split(',') if tex.strip()]
                width_arr_str = item.get('width_arr')
                if width_arr_str:
                    width_arr = [int(w.strip()) for w in width_arr_str.split(',') if w.strip().isdigit()]
                    width_arr = sorted(width_arr)
                else:
                    width_arr = None
                if base_texture:
                    texture_dict[base_texture] = {
                        'sub_textures': sub_textures,
                        'width_arr': width_arr
                    }

            log.info(f"成功从kufang_texture_category获取数据: {texture_dict}")
            return texture_dict
        except Exception as e:
            log.error(f"从kufang_texture_category获取数据时发生错误: {e}")
            return {}

    @staticmethod
    def fetch_robot_config_width_arr():
        """
        从kufang_robot_config表获取全局width_arr数据。
        使用认证客户端以支持RLS策略。
        """
        try:
            # 获取认证客户端
            auth_client = get_authenticated_client()

            # 使用认证客户端查询数据
            response = auth_client.table('kufang_robot_config').select('width_arr').execute()
            if not response.data:
                log.error("无法从kufang_robot_config获取width_arr配置。")
                return None
            width_arr_str = response.data[0].get('width_arr', '')
            if not width_arr_str:
                log.error("kufang_robot_config的width_arr字段为空。")
                return None
            width_arr = [int(w.strip()) for w in width_arr_str.split(',') if w.strip().isdigit()]
            width_arr = sorted(width_arr)
            if not width_arr:
                log.error("kufang_robot_config的width_arr字段格式不正确。")
                return None
            log.info(f"成功从kufang_robot_config获取width_arr: {width_arr}")
            return width_arr
        except Exception as e:
            log.error(f"从kufang_robot_config获取width_arr时发生错误: {e}")
            return None

    @staticmethod
    def fetch_remove_word_arr():
        """
        从数据库获取并处理 remove_word_arr
        使用认证客户端以支持RLS策略。
        """
        try:
            # 获取认证客户端
            auth_client = get_authenticated_client()

            # 使用认证客户端查询数据
            response = auth_client.table('kufang_robot_config').select('remove_word_arr').execute()
            if response.data and response.data[0].get('remove_word_arr'):
                # 将逗号分隔的字符串转换为数组
                remove_words = [word.strip() for word in response.data[0]['remove_word_arr'].split(',') if word.strip()]
                log.info(f"成功获取remove_words: {remove_words}")
                return remove_words
            return None
        except Exception as e:
            log.error(f"获取 remove_word_arr 失败: {e}")
            return None

    @staticmethod
    def fetch_is_parse_unrecognized():
        """
        从kufang_robot_config表获取 is_parse_unrecognized 的BOOL值，
        用于判断是否解析"未识别"文件夹里的 TXT 文件。
        如果获取失败或无数据，则默认返回 True（解析未识别文件夹里的 TXT 文件）。
        使用认证客户端以支持RLS策略。
        """
        try:
            # 获取认证客户端
            auth_client = get_authenticated_client()

            # 使用认证客户端查询数据
            response = auth_client.table('kufang_robot_config').select('is_parse_unrecognized').execute()
            if response.data and response.data[0].get('is_parse_unrecognized') is not None:
                value = response.data[0]['is_parse_unrecognized']
                log.info(f"获取到 is_parse_unrecognized: {value}")
                return bool(value)
            else:
                log.warning("未能从kufang_robot_config获取 is_parse_unrecognized, 使用默认值 False")
                return False
        except Exception as e:
            log.error(f"获取 is_parse_unrecognized 时出错: {e}")
            return True

    @staticmethod
    def get_excel_path(file_path):
        """
        根据TXT文件路径生成对应的Excel文件路径。
        """
        base, _ = os.path.splitext(file_path)
        return f"{base}.xlsx"

    @staticmethod
    def fetch_material_keywords():
        """
        从kufang_texture_category表获取texture_arr字段并生成素材名称列表。
        使用认证客户端以支持RLS策略。
        """
        try:
            # 获取认证客户端
            auth_client = get_authenticated_client()

            # 使用认证客户端查询数据
            response = auth_client.table('kufang_texture_category').select('texture_arr').execute()
            if not response.data:
                log.error("无法从kufang_texture_category获取texture_arr数据。")
                return []
            concatenated = ','.join([item['texture_arr'] for item in response.data if item['texture_arr']])
            keywords = [tex.strip() for tex in concatenated.split(',') if tex.strip()]
            log.info(f"成功获取material_keywords: {keywords}")
            return keywords
        except Exception as e:
            log.error(f"获取material_keywords时发生错误: {e}")
            return []

    @staticmethod
    def is_valid_tracking_number(tracking_number):
        """
        静态方法：验证快递单号是否符合预期格式

        参数:
            tracking_number: 待验证的快递单号

        返回值:
            bool: 是否为有效的快递单号
        """
        if not tracking_number or not isinstance(tracking_number, str):
            return False

        tracking_number = tracking_number.strip()

        # 验证顺丰单号格式：SF开头加10-15位数字
        if tracking_number.startswith('SF') and re.match(r'^SF\d{10,15}$', tracking_number):
            return True

        # 验证纯数字单号格式：10-20位数字
        if tracking_number.isdigit() and 10 <= len(tracking_number) <= 20:
            return True

        return False


# ---------------------------
# 工作线程
# ---------------------------
class Worker(QThread):
    progress = pyqtSignal(int)
    log = pyqtSignal(str)
    finished = pyqtSignal(bool, str)

    def __init__(self, folder_path, openai_mgr, parent=None):
        super().__init__(parent)
        self.folder_path = folder_path
        self.openai_mgr = openai_mgr  # 传入OpenAIClientManager实例
        self.material_keywords = Utils.fetch_material_keywords()
        if not self.material_keywords:
            log.error("无法获取素材名称列表。")
            self.finished.emit(False, "无法获取素材名称列表。")
            return

        # 获取 remove_words
        self.remove_words = Utils.fetch_remove_word_arr()
        if self.remove_words is None:
            log.error("无法获取移除词数组。")
            self.finished.emit(False, "无法获取移除词数组。")

        # 新增：从配置中获取是否解析"未识别"文件夹中的TXT文件
        self.is_parse_unrecognized = Utils.fetch_is_parse_unrecognized()
        log.info(f"is_parse_unrecognized 配置: {self.is_parse_unrecognized}")

        self.parser = LineParser(self.material_keywords, self.remove_words, openai_client_manager=self.openai_mgr)

        self.texture_categories = Utils.fetch_texture_categories()
        if not self.texture_categories:
            self.finished.emit(False, "无法获取素材分类数据。")
            return

        self.sub_to_base_texture = {}
        for base, details in self.texture_categories.items():
            for sub_texture in details['sub_textures']:
                self.sub_to_base_texture[sub_texture] = base

        self.global_width_arr = Utils.fetch_robot_config_width_arr()
        if not self.global_width_arr:
            self.global_width_arr = [160, 200, 240, 300]
            log.info("使用默认的全局width_arr配置。")
        self.global_width_ranges, self.global_sheet_mapping = self.generate_width_mappings(self.global_width_arr)

        self.base_texture_mappings = {}
        for base, details in self.texture_categories.items():
            width_arr = details['width_arr']
            if width_arr:
                width_ranges, sheet_mapping = self.generate_width_mappings(width_arr)
            else:
                width_ranges = self.global_width_ranges
                sheet_mapping = self.global_sheet_mapping
            self.base_texture_mappings[base] = {
                'width_ranges': width_ranges,
                'sheet_mapping': sheet_mapping
            }

    def generate_width_mappings(self, width_arr):
        """
        根据width_arr生成width_ranges和sheet_mapping。
        """
        width_ranges = []
        sheet_mapping = {}
        previous = 0
        for width in width_arr:
            width_ranges.append((previous, width, str(width)))
            sheet_mapping[width] = str(width)
            previous = width
        width_ranges.append((previous, float('inf'), 'Other'))
        log.info(f"生成的width_ranges: {width_ranges} 和 sheet_mapping: {sheet_mapping}")
        return width_ranges, sheet_mapping

    def run(self):
        try:
            self.log.emit("开始组织TXT文件到对应的文件夹...")
            organized = self.organize_files()
            if not organized:
                self.log.emit("没有需要组织的TXT文件。")
            else:
                self.log.emit("文件组织完成。")

            txt_files = []
            # 遍历所有子文件夹中的 TXT 文件（确保每个 TXT 对应独立表格）
            for root, dirs, files in os.walk(self.folder_path):
                if root == self.folder_path:
                    continue
                # 新增：如果当前文件夹为"未识别"，且配置不解析未识别文件夹的文件，则跳过处理
                if not self.is_parse_unrecognized and os.path.basename(root) == "未识别":
                    self.log.emit(f"配置不解析未识别文件夹，跳过: {root}")
                    continue
                for file in files:
                    if file.lower().endswith('.txt'):
                        txt_files.append(os.path.join(root, file))
            total_files = len(txt_files)
            if total_files == 0:
                self.log.emit("没有找到TXT文件。")
                self.finished.emit(False, "没有找到TXT文件。")
                return
            log.info(f"开始处理 {total_files} 个文件。")
            self.log.emit(f"开始处理 {total_files} 个文件。")
            processed_files = 0

            with ThreadPoolExecutor(max_workers=min(32, os.cpu_count() + 4)) as executor:
                future_to_file = {
                    executor.submit(self.process_file, txt_file, os.path.basename(txt_file)): txt_file
                    for txt_file in txt_files
                }

                for future in as_completed(future_to_file):
                    txt_file = future_to_file[future]
                    try:
                        result = future.result()
                        if result:
                            log.info(f"成功处理文件：{txt_file}")
                            self.log.emit(f"成功处理文件：{txt_file}")
                        else:
                            log.warning(f"处理失败文件：{txt_file}")
                            self.log.emit(f"处理失败文件：{txt_file}")
                    except Exception as e:
                        log.error(f"文件 {txt_file} 处理过程中发生错误: {str(e)}")
                        self.log.emit(f"文件 {txt_file} 处理过程中发生错误: {str(e)}")
                    processed_files += 1
                    progress_percent = int((processed_files / total_files) * 100)
                    self.progress.emit(progress_percent)

            log.info("所有文件处理完成。")
            self.log.emit("所有文件处理完成。")

            self.log.emit("开始合并基础素材文件夹的操作表格...")
            merged_success = self.merge_all_base_folders()
            if merged_success:
                log.info("所有基础素材文件夹的操作表格已成功合并。")
                self.log.emit("所有基础素材文件夹的操作表格已成功合并。")
                self.finished.emit(True, "所有文件处理和合并完成。")
            else:
                log.warning("基础素材文件夹的操作表格合并过程中出现问题。")
                self.log.emit("基础素材文件夹的操作表格合并过程中出现问题。")
                self.finished.emit(False, "文件处理完成，但合并过程中出现问题。")
        except Exception as e:
            log.error(f"发生错误: {str(e)}")
            self.log.emit(f"发生错误: {str(e)}")
            self.finished.emit(False, f"发生错误: {str(e)}")

    def organize_files(self):
        """
        组织TXT文件到基础素材对应的文件夹中.
        如果文件名称无法匹配到从Supabase获取的配置中的子素材，
        则将文件统一归类到"未识别"文件夹中。
        """
        files_copied = False
        txt_files = [f for f in os.listdir(self.folder_path) if f.lower().endswith('.txt')]

        for txt_file in txt_files:
            full_path = os.path.join(self.folder_path, txt_file)
            matched_base = None
            for sub_texture, base in self.sub_to_base_texture.items():
                if sub_texture in txt_file:
                    matched_base = base
                    break
            # 如果未匹配到子素材，则统一归到"未识别"文件夹
            if not matched_base:
                matched_base = "未识别"

            base_folder = os.path.join(self.folder_path, matched_base)
            os.makedirs(base_folder, exist_ok=True)
            destination = os.path.join(base_folder, txt_file)
            if not os.path.exists(destination):
                try:
                    shutil.copy(full_path, destination)
                    self.log.emit(f"将文件 {txt_file} 复制到文件夹 {matched_base}")
                    files_copied = True
                except Exception as e:
                    log.error(f"复制文件 {txt_file} 到文件夹 {matched_base} 失败: {e}")
                    self.log.emit(f"复制文件 {txt_file} 到文件夹 {matched_base} 失败: {e}")
            else:
                self.log.emit(f"文件 {txt_file} 已存在于文件夹 {matched_base}，跳过复制。")
        return files_copied

    def process_file(self, file_path, txt_file):
        """
        针对单个 TXT 文件进行解析，使用独立的 OpenAI 会话，
        并行进行正则与 OpenAI 补充解析，将解析成功的数据写入对应 sheet。
        """
        excel_path = None
        try:
            base_texture = None
            for sub_texture, base in self.sub_to_base_texture.items():
                if sub_texture in txt_file:
                    base_texture = base
                    break
            if not base_texture:
                log.warning(f"文件 {txt_file} 未匹配到基础素材，使用默认宽度范围。")
                width_ranges = self.global_width_ranges
                sheet_mapping = self.global_sheet_mapping
            else:
                mappings = self.base_texture_mappings.get(base_texture)
                if mappings:
                    width_ranges = mappings['width_ranges']
                    sheet_mapping = mappings['sheet_mapping']
                else:
                    width_ranges = self.global_width_ranges
                    sheet_mapping = self.global_sheet_mapping

            # 定义错误 sheet 名称为 "数据异常"
            error_sheet = "数据异常"
            sheet_names = [sheet for _, _, sheet in width_ranges] + [error_sheet]
            # 初始化 sheet_data；错误 sheet 将记录详细错误信息。
            # 有效 sheet 记录正确数据，错误 sheet记录所有详细错误日志信息。
            sheet_data = {sheet: [] for sheet in sheet_names}
            cumulative_length_total = 0
            excel_path = Utils.get_excel_path(file_path)

            # 为当前文件创建独立的 OpenAI 会话，防止数据混乱
            local_openai_mgr = self.openai_mgr.clone()
            local_parser = LineParser(self.material_keywords, self.remove_words, openai_client_manager=local_openai_mgr)

            with open(file_path, 'r', encoding='utf-8') as file:
                lines = file.readlines()

            from concurrent.futures import ThreadPoolExecutor, as_completed
            line_results = []
            # 提交任务时同时保存行号和原始文本（去除空白字符）用于后续记录详细信息
            with ThreadPoolExecutor(max_workers=min(8, len(lines))) as executor:
                future_to_line = {
                    executor.submit(
                        local_parser.parse_line,
                        line,
                        idx,
                        os.path.basename(file_path),
                        lambda msg: (self.log.emit(msg), local_openai_mgr.status_update.emit(msg))
                    ): (idx, line.strip())
                    for idx, line in enumerate(lines, start=1) if line.strip()
                }
                for future in as_completed(future_to_line):
                    line_number, original_line = future_to_line[future]
                    try:
                        # result 为当前行解析后返回的列表数据（可能包含多个结果）
                        result = future.result()
                        line_results.append((line_number, result, original_line))
                    except Exception as e:
                        error_record = {
                            '原始数据': original_line,
                            '行号': line_number,
                            '错误原因': f"解析异常: {str(e)}",
                            '错误日志': "多线程任务抛出异常",
                            '数据处理流程': "规则解析与OpenAI补充解析",
                            '解析错误': f"解析异常: {str(e)}"
                        }
                        sheet_data[error_sheet].append(error_record)
                        self.log.emit(f"文件 {os.path.basename(file_path)}: 第 {line_number} 行解析失败: {str(e)}")

            # 将结果按行号排序
            line_results.sort(key=lambda x: x[0])

            for line_num, parsed_entries, original_line in line_results:
                # 筛选出没有 '解析错误' 标识的结果（这些可能来源于规则匹配或OpenAI补救解析）
                valid_entries = [entry for entry in parsed_entries if '解析错误' not in entry]

                # 进一步检查若有效结果中高、宽均为空（即未解析出关键数值），则判定为异常
                filtered_valid_entries = []
                for entry in valid_entries:
                    width = entry.get('宽cm')
                    length = entry.get('高cm')  # 修改：长cm改为高cm
                    if (not width and not length):
                        error_record = {
                            '原始数据': entry.get('原始数据', original_line),
                            '行号': line_num,
                            '错误原因': "OpenAI解析返回错误: 高、宽均为空值",  # 修改：长改为高
                            '错误日志': "OpenAI返回结果中的高、宽字段均为空",  # 修改：长改为高
                            '数据处理流程': entry.get('处理方式', '未知'),
                            '解析错误': "OpenAI解析返回错误: 高、宽均为空值"  # 修改：长改为高
                        }
                        sheet_data[error_sheet].append(error_record)
                    else:
                        filtered_valid_entries.append(entry)

                if filtered_valid_entries:
                    for entry in filtered_valid_entries:
                        width = entry.get('宽cm', 0)
                        length = entry.get('高cm', 0)  # 修改：长cm改为高cm
                        quantity = entry.get('数量', 1)
                        # 根据宽度、高度、数量和材质确定所属 sheet（使用新的三级优先逻辑）
                        sheet = self.determine_sheet(width, entry.get('材质', ''), width_ranges, sheet_mapping, height=length, quantity=quantity)

                        # 处理倍数优化的分割情况
                        if sheet and sheet.startswith("SPLIT:"):
                            # 解析分割信息：SPLIT:target_width:optimized_quantity:remaining_quantity
                            parts = sheet.split(":")
                            target_width = parts[1]
                            optimized_quantity = int(parts[2])
                            remaining_quantity = int(parts[3])

                            # 创建优化分配的记录
                            if optimized_quantity > 0:
                                optimized_entry = entry.copy()
                                optimized_entry['数量'] = optimized_quantity
                                optimized_cumulative = (length + 1) * optimized_quantity
                                self._add_record_to_sheet(optimized_entry, target_width, width, length, sheet_data, cumulative_length_total, file_path)
                                cumulative_length_total += optimized_cumulative
                                self.log.emit(f"倍数优化: {optimized_quantity}个图片分配到sheet {target_width}")

                            # 处理剩余数量，使用原始逻辑分配
                            if remaining_quantity > 0:
                                remaining_entry = entry.copy()
                                remaining_entry['数量'] = remaining_quantity
                                # 重新计算sheet，但不使用倍数优化（传入quantity=1避免再次触发倍数优化）
                                original_sheet = self.determine_sheet(width, entry.get('材质', ''), width_ranges, sheet_mapping, height=length, quantity=1)
                                if original_sheet and not original_sheet.startswith("SPLIT:"):
                                    remaining_cumulative = (length + 1) * remaining_quantity
                                    self._add_record_to_sheet(remaining_entry, original_sheet, width, length, sheet_data, cumulative_length_total, file_path)
                                    cumulative_length_total += remaining_cumulative
                                    self.log.emit(f"剩余数量: {remaining_quantity}个图片分配到sheet {original_sheet}")
                                else:
                                    self.log.emit(f"警告: 剩余{remaining_quantity}个图片无法分配")
                            continue

                        if sheet is None:
                            error_record = {
                                '原始数据': entry.get('原始数据', original_line),
                                '行号': line_num,
                                '错误原因': f"无法分配sheet: 材质 {entry.get('材质', '')}, 宽度 {width}, 高度 {length}",  # 修改：长度改为高度
                                '错误日志': "分配sheet失败",
                                '数据处理流程': entry.get('处理方式', '未知'),
                                '解析错误': f"无法分配sheet: 材质 {entry.get('材质', '')}, 宽度 {width}, 高度 {length}"  # 修改：长度改为高度
                            }
                            sheet_data[error_sheet].append(error_record)
                            self.log.emit(f"文件 {os.path.basename(file_path)}: 无法分配sheet：材质 {entry.get('材质', '')}, 宽度 {width}, 高度 {length}")  # 修改：长度改为高度
                            continue

                        # 针对宽=100，高=100的情况，且分配到160宽幅时，若配置中存在200宽幅，则移动至200宽幅，  # 修改：长改为高
                        # 累计长度计算方式更新为：每2个为一一组，若总数为偶数则累计长度为 数量/2*101，
                        # 若总数为奇数则为 ((数量-1)/2)*101 + 101
                        if width == 100 and length == 100 and sheet == "160" and 200 in sheet_mapping:
                            sheet = sheet_mapping[200]
                            if quantity % 2 == 0:
                                cumulative_length = (quantity // 2) * 101
                            else:
                                cumulative_length = ((quantity - 1) // 2) * 101 + 101
                        else:
                            if length in (180, 200) and length in sheet_mapping:
                                sheet = sheet_mapping[length]
                            try:
                                sheet_numeric = int(sheet)
                            except ValueError:
                                sheet_numeric = None
                            if sheet_numeric is not None and sheet_numeric == length:
                                cumulative_length = (width + 1) * quantity
                            else:
                                cumulative_length = (length + 1) * quantity

                        pattern_full_name = entry.get('图案全称', f"{entry.get('图案', '')}-{width}-{length}")

                        record = {
                            '材质': entry.get('材质', ''),
                            '图案': entry.get('图案', ''),
                            '宽cm': width,
                            '高cm': length,  # 修改：长cm改为高cm
                            '图案全称': pattern_full_name,
                            '数量': quantity,
                            '累计长度': cumulative_length,
                            '原始数据': entry.get('原始数据', original_line),
                            '处理方式': entry.get('处理方式', ''),
                            '快递单号': entry.get('快递单号', '无')
                        }

                        sheet_data[sheet].append(record)
                        cumulative_length_total += cumulative_length
                        self.log.emit(f"文件 {os.path.basename(file_path)}: 解析成功: {record}")
                else:
                    # 如果该行所有结果均为错误，则记录每个错误的详细信息
                    for entry in parsed_entries:
                        error_record = {
                            '原始数据': entry.get('原始数据', original_line),
                            '行号': line_num,
                            '错误原因': entry.get('解析错误', '未知错误'),
                            '错误日志': "OpenAI或规则解析返回错误",
                            '数据处理流程': entry.get('处理方式', '未知')
                        }
                        sheet_data[error_sheet].append(error_record)

            with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
                for sheet in sheet_names:
                    if sheet != error_sheet:
                        if sheet_data[sheet]:
                            df = pd.DataFrame(sheet_data[sheet], columns=[
                                '材质', '图案', '宽cm', '高cm', '图案全称', '数量', '累计长度', '原始数据', '处理方式', '快递单号'  # 修改：长cm改为高cm
                            ])
                            df = df.sort_values(by=['宽cm', '高cm'], ascending=[False, False])  # 修改：长cm改为高cm

                            # 修改：使用与总操作表格相同的分组逻辑
                            def get_group_key(row):
                                pattern = str(row['图案'])
                                pattern_full = str(row['图案全称'])
                                width = row['宽cm']

                                # 对于圆形图案，使用图案名称和宽度（直径）作为分组依据
                                if '圆形' in pattern_full or '直径' in pattern_full:
                                    return f"{pattern}_{width}_圆形"
                                # 对于非圆形图案，使用图案全称作为分组依据
                                return pattern_full

                            df['group_key'] = df.apply(get_group_key, axis=1)
                            df_grouped = df.groupby('group_key').agg({
                                '材质': 'first',
                                '图案': 'first',
                                '宽cm': 'first',
                                '高cm': 'first',  # 修改：长cm改为高cm
                                '图案全称': 'first',
                                '数量': 'sum',
                                '累计长度': 'sum',
                                '原始数据': lambda x: ' | '.join(x),
                                '处理方式': 'first',
                                '快递单号': lambda x: ' | '.join(x)
                            }).reset_index()

                            # 删除临时分组列
                            df_grouped = df_grouped.drop('group_key', axis=1)
                            df_grouped = df_grouped[['材质', '图案', '宽cm', '高cm', '图案全称', '数量', '累计长度', '原始数据', '处理方式', '快递单号']]  # 修改：长cm改为高cm
                            df_grouped = df_grouped.sort_values(by=['宽cm', '高cm'], ascending=[False, False])  # 修改：长cm改为高cm
                            df_grouped.to_excel(writer, sheet_name=sheet, index=False)
                    else:
                        if sheet_data[sheet]:
                            # 错误 sheet 记录详细错误信息，包括原始数据、行号、错误原因、错误日志、数据处理流程
                            df_failed = pd.DataFrame(sheet_data[sheet], columns=[
                                '原始数据', '行号', '错误原因', '错误日志', '数据处理流程'
                            ])
                            df_failed.to_excel(writer, sheet_name=sheet, index=False)
                df_summary = pd.DataFrame({'累计长度总和': [cumulative_length_total]})
                df_summary.to_excel(writer, sheet_name='累计长度', index=False)

            self.log.emit(f"文件 {os.path.basename(file_path)}: 成功生成 Excel 文件: {excel_path}")
            self.log.emit(f"文件 {os.path.basename(file_path)}: 累计长度总和: {cumulative_length_total}")
            return True
        except Exception as e:
            self.log.emit(f"文件 {os.path.basename(file_path)}: 发生错误: {str(e)}")
            if excel_path and os.path.exists(excel_path):
                os.remove(excel_path)
            return False

    def determine_sheet(self, width, material, width_ranges, sheet_mapping, height=None, quantity=1):
        """
        根据宽度和高度确定sheet名称，实现三级优先逻辑。

        倍数优化逻辑：宽或高通过倍数关系匹配宽幅≤200时，根据数量智能分配
        第一优先逻辑：宽 或 高 等于 宽幅，且宽幅 <= 200时，分到对应的 sheet 页
        第二优先逻辑：高 大于前一个宽幅，小于等于当前宽幅时，分到当前宽幅的sheet页

        Args:
            width: 图片宽度
            material: 材质（保留兼容性）
            width_ranges: 宽度范围配置
            sheet_mapping: sheet映射配置
            height: 图片高度
            quantity: 图片数量（用于倍数优化逻辑）
        """
        # 从width_ranges中提取宽幅值列表
        width_values = []
        for lower, upper, sheet in width_ranges:
            if upper != float('inf'):
                width_values.append(upper)
        width_values.sort()

        # 如果没有传入高度，使用宽度作为高度（向后兼容）
        if height is None:
            height = width
            log.debug(f"未传入高度参数，使用宽度{width}作为高度")

        log.debug(f"Sheet分配逻辑: 宽度={width}, 高度={height}, 数量={quantity}, 宽幅值={width_values}")

        # 倍数优化逻辑：宽或高通过倍数关系匹配宽幅≤200时，根据数量智能分配
        multiplier_result = self._check_multiplier_logic(width, height, width_values, quantity)
        if multiplier_result:
            log.info(f"倍数优化逻辑匹配: {multiplier_result['description']}，分配到sheet {multiplier_result['target_sheet']}")
            return multiplier_result['target_sheet']

        # 第一优先逻辑：宽 或 高 等于 宽幅，且宽幅 <= 200时，分到对应的 sheet 页
        for width_value in width_values:
            if width_value <= 200:  # 宽幅 <= 200的条件
                if width == width_value or height == width_value:
                    log.info(f"第一优先逻辑匹配: 宽{width}或高{height}等于宽幅{width_value}，分配到sheet {width_value}")
                    return str(width_value)

        # 第二优先逻辑：高 大于前一个宽幅，小于等于当前宽幅时，分到当前宽幅的sheet页
        previous_width = 0
        for width_value in width_values:
            if previous_width < height <= width_value:
                log.info(f"第二优先逻辑匹配: 高{height}在({previous_width}, {width_value}]范围内，分配到sheet {width_value}")
                return str(width_value)
            previous_width = width_value

        # 如果都不匹配，返回最大的宽幅或Other
        if width_values:
            if height > width_values[-1]:
                log.info(f"高度{height}超过最大宽幅{width_values[-1]}，分配到Other sheet")
                return 'Other'
            else:
                log.info(f"使用默认逻辑，分配到最小宽幅sheet {width_values[0]}")
                return str(width_values[0])  # 默认返回最小的宽幅

        log.warning(f"无法确定sheet分配，宽度={width}, 高度={height}")
        return None

    def _check_multiplier_logic(self, width, height, width_values, quantity):
        """
        检查倍数优化逻辑：宽或高通过倍数关系匹配宽幅≤200时，根据数量智能分配

        例如：
        - 宽或高是50时，4的倍数分配到200（50×4=200）
        - 宽或高是40时，5的倍数分到200（40×5=200）
        - 宽或高是60时，3的倍数分到180（60×3=180）

        智能分配规则：
        - 9个130×50的图片 → 8个分到200宽幅，1个分到原sheet
        - 3个100×100的图片 → 2个分到200宽幅，1个分到原sheet

        Args:
            width: 图片宽度
            height: 图片高度
            width_values: 宽幅值列表
            quantity: 图片数量

        Returns:
            dict: 匹配结果，包含target_sheet和description，如果不匹配返回None
        """
        # 只考虑宽幅≤200的情况
        target_widths = [w for w in width_values if w <= 200]
        target_widths.sort(reverse=True)  # 从大到小排序，优先选择更大的宽幅

        # 检查宽度和高度
        dimensions = [(width, "宽度"), (height, "高度")]

        best_result = None
        max_target_width = 0

        for dimension, dimension_type in dimensions:
            if dimension <= 0:
                continue

            # 对每个目标宽幅，检查是否存在整数倍数关系
            for target_width in target_widths:
                if target_width % dimension == 0:  # dimension是target_width的因子
                    multiplier = target_width // dimension
                    if multiplier >= 2:  # 至少是2倍，避免1倍的情况
                        # 计算可以分配到目标宽幅的数量
                        optimized_quantity = (quantity // multiplier) * multiplier
                        remaining_quantity = quantity % multiplier

                        if optimized_quantity > 0:  # 至少有一组可以优化
                            # 选择能达到的最大宽幅
                            if target_width > max_target_width:
                                max_target_width = target_width

                                if remaining_quantity == 0:
                                    # 全部数量都可以分配到目标宽幅
                                    description = f"{dimension_type}{dimension}的{multiplier}倍等于宽幅{target_width}，{quantity}个图片全部分配到sheet {target_width}"
                                    target_sheet = str(target_width)
                                else:
                                    # 部分数量分配到目标宽幅，剩余的需要特殊处理
                                    groups = quantity // multiplier
                                    description = f"{dimension_type}{dimension}的{multiplier}倍等于宽幅{target_width}，{optimized_quantity}个图片（{groups}组×{multiplier}个）分配到sheet {target_width}，剩余{remaining_quantity}个需要单独处理"
                                    # 对于有剩余的情况，我们返回一个特殊的标记，让调用方处理分割逻辑
                                    target_sheet = f"SPLIT:{target_width}:{optimized_quantity}:{remaining_quantity}"

                                best_result = {
                                    'target_sheet': target_sheet,
                                    'target_width': target_width,
                                    'dimension': dimension,
                                    'multiplier': multiplier,
                                    'dimension_type': dimension_type,
                                    'optimized_quantity': optimized_quantity,
                                    'remaining_quantity': remaining_quantity,
                                    'description': description
                                }

        return best_result

    def _add_record_to_sheet(self, entry, sheet, width, length, sheet_data, cumulative_length_total, file_path):
        """
        将记录添加到指定的sheet中

        Args:
            entry: 数据条目
            sheet: 目标sheet名称
            width: 图片宽度
            length: 图片高度
            sheet_data: sheet数据字典
            cumulative_length_total: 累计长度总和（引用传递）
            file_path: 文件路径
        """
        quantity = entry.get('数量', 1)

        # 计算累计长度
        cumulative_length = (length + 1) * quantity

        # 构建图案全称
        pattern_full_name = entry.get('图案全称', f"{entry.get('图案', '')}-{width}-{length}")

        record = {
            '材质': entry.get('材质', ''),
            '图案': entry.get('图案', ''),
            '宽cm': width,
            '高cm': length,  # 修改：长cm改为高cm
            '图案全称': pattern_full_name,
            '数量': quantity,
            '累计长度': cumulative_length,
            '原始数据': entry.get('原始数据', ''),
            '处理方式': entry.get('处理方式', ''),
            '快递单号': entry.get('快递单号', '无')
        }

        sheet_data[sheet].append(record)
        # 注意：这里无法直接修改cumulative_length_total，因为它是局部变量
        # 调用方需要自己累加

        self.log.emit(f"文件 {os.path.basename(file_path)}: 解析成功: {record}")

    def optimize_sheet_allocation(self, df_grouped, current_sheet, merged_data):
        """
        总操作表格优化逻辑：通过倍数计算优化图片分配

        支持跨图案组合优化：
        - 不同图案但相同尺寸的图片可以组合优化
        - 例如：1个图案A(130×50) + 2个图案B(130×50) + 3个图案C(130×50) + 4个图案D(130×50) = 10个130×50
        - 优化：8个分到200宽幅，2个留在160宽幅

        优化规则：
        - 宽或高是50时，4的倍数分配到200宽幅 (50×4=200)
        - 宽或高是40时，5的倍数分配到200宽幅 (40×5=200)
        - 宽或高是60时，3的倍数分配到180宽幅 (60×3=180)

        Args:
            df_grouped: 当前sheet的分组数据
            current_sheet: 当前sheet名称
            merged_data: 所有sheet的合并数据字典

        Returns:
            优化后的df_grouped
        """
        if df_grouped.empty or current_sheet == '数据异常':
            return df_grouped

        # 定义优化规则：{尺寸: (倍数, 目标宽幅)}
        optimization_rules = {
            50: (4, 200),   # 50×4=200
            40: (5, 200),   # 40×5=200
            60: (3, 180),   # 60×3=180
        }

        # 按尺寸分组，支持跨图案组合优化
        size_groups = {}  # {(width, height): [(idx, row, dimension, multiplier, target_width)]}

        # 第一步：识别所有可优化的尺寸组合
        for idx, row in df_grouped.iterrows():
            width = row.get('宽cm', 0)
            height = row.get('高cm', 0)
            quantity = row.get('数量', 0)

            if quantity <= 0:
                continue

            # 检查是否匹配优化规则
            for dimension, (multiplier, target) in optimization_rules.items():
                if (width == dimension or height == dimension) and target <= 200:
                    size_key = (width, height)
                    if size_key not in size_groups:
                        size_groups[size_key] = []
                    size_groups[size_key].append((idx, row, dimension, multiplier, target))
                    break

        # 第二步：对每个尺寸组合进行跨图案优化
        rows_to_remove = []
        rows_to_add = {}
        optimization_log = []

        for size_key, group_items in size_groups.items():
            width, height = size_key

            # 计算该尺寸的总数量和优化参数
            total_quantity = sum(item[1].get('数量', 0) for item in group_items)
            if not group_items:
                continue

            # 使用第一个项目的优化参数（同尺寸的优化参数相同）
            _, _, dimension, multiplier, target_width = group_items[0]

            if total_quantity >= multiplier:
                # 计算可以优化的总数量
                total_optimizable = (total_quantity // multiplier) * multiplier
                total_remaining = total_quantity - total_optimizable

                if total_optimizable > 0:
                    target_sheet = str(target_width)

                    # 第三步：分配优化数量到各个图案
                    remaining_to_optimize = total_optimizable

                    for idx, row, _, _, _ in group_items:
                        if remaining_to_optimize <= 0:
                            break

                        current_quantity = row.get('数量', 0)

                        # 计算当前图案可以分配的优化数量
                        optimized_for_this_pattern = min(current_quantity, remaining_to_optimize)
                        remaining_for_this_pattern = current_quantity - optimized_for_this_pattern

                        if optimized_for_this_pattern > 0:
                            # 创建优化后的记录
                            optimized_row = row.copy()
                            optimized_row['数量'] = optimized_for_this_pattern
                            optimized_row['累计长度'] = optimized_row['累计长度'] * (optimized_for_this_pattern / current_quantity)
                            optimized_row['原始数据'] = f"{row['原始数据']} [跨图案优化到{target_sheet}宽幅]"
                            optimized_row['处理方式'] = f"{row['处理方式']} + 跨图案倍数优化"

                            # 添加到目标sheet
                            if target_sheet not in rows_to_add:
                                rows_to_add[target_sheet] = []
                            rows_to_add[target_sheet].append(optimized_row)

                            remaining_to_optimize -= optimized_for_this_pattern

                        # 更新当前行（如果有剩余）
                        if remaining_for_this_pattern > 0:
                            df_grouped.at[idx, '数量'] = remaining_for_this_pattern
                            df_grouped.at[idx, '累计长度'] = row['累计长度'] * (remaining_for_this_pattern / current_quantity)
                            df_grouped.at[idx, '原始数据'] = f"{row['原始数据']} [部分跨图案优化]"
                        else:
                            # 如果没有剩余，标记为需要移除
                            rows_to_remove.append(idx)

                    optimization_log.append(
                        f"跨图案优化: {total_optimizable}个{width}×{height}(共{len(group_items)}种图案) → {target_sheet}宽幅 "
                        f"(剩余{total_remaining}个在{current_sheet})"
                    )

        # 移除完全转移的行
        if rows_to_remove:
            df_grouped = df_grouped.drop(rows_to_remove).reset_index(drop=True)

        # 将优化的数据添加到目标sheet
        for target_sheet, new_rows in rows_to_add.items():
            if target_sheet in merged_data:
                # 如果目标sheet已存在，添加到现有数据
                if not merged_data[target_sheet].empty:
                    new_df = pd.DataFrame(new_rows)
                    merged_data[target_sheet] = pd.concat([merged_data[target_sheet], new_df], ignore_index=True)
                else:
                    merged_data[target_sheet] = pd.DataFrame(new_rows)
            else:
                # 如果目标sheet不存在，创建新的
                merged_data[target_sheet] = pd.DataFrame(new_rows)

        # 记录优化日志
        if optimization_log:
            for log_msg in optimization_log:
                self.log.emit(f"总操作表格优化: {log_msg}")
                log.info(f"总操作表格优化: {log_msg}")

        return df_grouped

    def merge_all_base_folders(self):
        """
        合并每个基础素材文件夹中的操作表格为一个总操作表格。

        优化内容：
        1. 增强数据完整性保障，确保合并过程中不丢失数据
        2. 改进错误处理机制，单个文件错误不影响整体合并
        3. 添加数据验证步骤，确保合并后的数据与源数据一致
        4. 增加备份机制，防止意外数据丢失
        """
        try:
            for base_material, mappings in self.base_texture_mappings.items():
                base_folder = os.path.join(self.folder_path, base_material)
                if not os.path.isdir(base_folder):
                    continue

                excel_files = [
                    os.path.join(base_folder, f) for f in os.listdir(base_folder)
                    if f.lower().endswith('.xlsx') and not f.startswith('总操作表格')
                ]

                if not excel_files:
                    self.log.emit(f"基础素材文件夹 {base_material} 中没有操作表格可合并。")
                    continue

                # 记录源数据总行数，用于后续验证
                total_source_rows = 0
                source_files_info = []

                width_ranges = mappings.get('width_ranges', self.global_width_ranges)
                sheet_names = [sheet for _, _, sheet in width_ranges] + ['数据异常']

                merged_data = {sheet: [] for sheet in sheet_names}
                failed_files = []

                # 第一阶段：读取所有Excel文件
                for excel_file in excel_files:
                    try:
                        file_row_count = 0
                        with pd.ExcelFile(excel_file) as xls:
                            available_sheets = xls.sheet_names
                            for sheet in merged_data.keys():
                                if sheet in available_sheets:
                                    try:
                                        df = pd.read_excel(xls, sheet_name=sheet)
                                        if not df.empty:
                                            # 确保快递单号列是字符串类型
                                            if '快递单号' in df.columns:
                                                df['快递单号'] = df['快递单号'].astype(str)
                                            # 记录行数但不添加源文件列
                                            file_row_count += len(df)
                                            merged_data[sheet].append(df)
                                    except Exception as sheet_error:
                                        log.error(f"读取文件 {excel_file} 的工作表 {sheet} 时出错: {sheet_error}")
                                        self.log.emit(f"警告: 读取工作表 {sheet} 失败，尝试继续处理其他工作表")

                        total_source_rows += file_row_count
                        source_files_info.append({
                            'file': excel_file,
                            'row_count': file_row_count
                        })
                        self.log.emit(f"成功读取文件 {os.path.basename(excel_file)}，包含 {file_row_count} 行数据")
                    except Exception as e:
                        log.error(f"合并时读取文件 {excel_file} 失败: {e}")
                        self.log.emit(f"合并时读取文件 {excel_file} 失败: {e}")
                        failed_files.append(excel_file)
                        continue

                if failed_files:
                    self.log.emit(f"警告: {len(failed_files)} 个文件读取失败，这些文件将被跳过")

                # 第二阶段：合并数据框
                total_merged_rows = 0
                for sheet, df_list in merged_data.items():
                    if df_list:
                        try:
                            # 使用安全的合并方式
                            merged_data[sheet] = pd.concat(df_list, ignore_index=True, sort=False)
                            total_merged_rows += len(merged_data[sheet])
                        except Exception as concat_error:
                            log.error(f"合并工作表 {sheet} 数据时出错: {concat_error}")
                            # 尝试逐个添加数据框，跳过有问题的数据框
                            safe_df_list = []
                            for i, df in enumerate(df_list):
                                try:
                                    if not df.empty:
                                        safe_df_list.append(df)
                                except Exception as df_error:
                                    log.error(f"处理工作表 {sheet} 的第 {i+1} 个数据框时出错: {df_error}")

                            if safe_df_list:
                                try:
                                    merged_data[sheet] = pd.concat(safe_df_list, ignore_index=True, sort=False)
                                    total_merged_rows += len(merged_data[sheet])
                                    self.log.emit(f"已安全合并工作表 {sheet} 的部分数据")
                                except Exception as safe_concat_error:
                                    log.error(f"安全合并工作表 {sheet} 数据时仍然出错: {safe_concat_error}")
                                    merged_data[sheet] = pd.DataFrame()
                            else:
                                merged_data[sheet] = pd.DataFrame()
                    else:
                        merged_data[sheet] = pd.DataFrame()

                # 数据完整性验证
                if total_merged_rows < total_source_rows:
                    log.warning(f"数据合并过程中可能丢失了数据: 源数据总行数 {total_source_rows}，合并后总行数 {total_merged_rows}")
                    self.log.emit(f"警告: 合并过程中可能有 {total_source_rows - total_merged_rows} 行数据未被正确合并")

                # 第三阶段：保存合并后的数据
                total_excel_path = os.path.join(base_folder, '总操作表格.xlsx')
                backup_path = os.path.join(base_folder, f'总操作表格_备份_{int(time.time())}.xlsx')

                # 如果已存在总操作表格，先创建备份
                if os.path.exists(total_excel_path):
                    try:
                        shutil.copy2(total_excel_path, backup_path)
                        self.log.emit(f"已创建现有总操作表格的备份: {os.path.basename(backup_path)}")
                    except Exception as backup_error:
                        log.error(f"创建总操作表格备份时出错: {backup_error}")
                        self.log.emit(f"警告: 无法创建总操作表格备份: {backup_error}")

                try:
                    with pd.ExcelWriter(total_excel_path, engine='openpyxl') as writer:
                        for sheet, df in merged_data.items():
                            if not df.empty:
                                if sheet != '数据异常':
                                    # 排序前先保存一份原始数据，确保不丢失任何行
                                    original_row_count = len(df)

                                    # 确保快递单号列是字符串类型
                                    if '快递单号' in df.columns:
                                        df['快递单号'] = df['快递单号'].astype(str)

                                    # 排序
                                    df = df.sort_values(
                                        by=['宽cm', '高cm'],  # 修改：长cm改为高cm
                                        ascending=[False, False]
                                    )

                                    # 确保排序后行数不变
                                    if len(df) != original_row_count:
                                        log.error(f"排序后行数变化: 原始 {original_row_count}, 现在 {len(df)}")
                                        self.log.emit(f"警告: 工作表 {sheet} 排序后数据行数发生变化，尝试恢复")
                                        # 如果行数变化，使用原始数据
                                        df = merged_data[sheet].copy()

                                    # 修改：根据图案类型使用不同的分组策略
                                    def get_group_key(row):
                                        try:
                                            pattern = str(row['图案']) if pd.notna(row['图案']) else ''
                                            pattern_full = str(row['图案全称']) if pd.notna(row['图案全称']) else ''
                                            width = row['宽cm'] if pd.notna(row['宽cm']) else 0

                                            # 对于圆形图案，使用图案名称和宽度（直径）作为分组依据
                                            if '圆形' in pattern_full or '直径' in pattern_full:
                                                return f"{pattern}_{width}_圆形"
                                            # 对于非圆形图案，使用图案全称作为分组依据
                                            return pattern_full
                                        except Exception as key_error:
                                            log.error(f"生成分组键时出错: {key_error}, 使用默认键")
                                            return f"default_group_{random.randint(1, 10000)}"

                                    # 保存分组前的行数
                                    pre_group_row_count = len(df)

                                    # 添加分组键
                                    df['group_key'] = df.apply(get_group_key, axis=1)

                                    # 执行分组聚合
                                    try:
                                        # 修复快递单号列的处理，确保所有值都是字符串
                                        df_grouped = df.groupby('group_key').agg({
                                            '材质': 'first',
                                            '图案': 'first',
                                            '宽cm': 'first',
                                            '高cm': 'first',  # 修改：长cm改为高cm
                                            '图案全称': 'first',
                                            '数量': 'sum',
                                            '累计长度': 'sum',
                                            '原始数据': lambda x: ' | '.join(str(i) for i in x if pd.notna(i)),
                                            '处理方式': 'first',
                                            '快递单号': lambda x: ' | '.join(str(i) for i in x if pd.notna(i))  # 确保转换为字符串
                                        }).reset_index()
                                    except Exception as group_error:
                                        log.error(f"分组聚合时出错: {group_error}, 尝试安全聚合")
                                        # 如果标准聚合失败，尝试更安全的方式
                                        safe_columns = ['材质', '图案', '宽cm', '高cm', '图案全称', '数量']  # 修改：长cm改为高cm
                                        agg_dict = {col: 'first' for col in safe_columns if col in df.columns}
                                        if '数量' in df.columns:
                                            agg_dict['数量'] = 'sum'
                                        if '累计长度' in df.columns:
                                            agg_dict['累计长度'] = 'sum'
                                        if '原始数据' in df.columns:
                                            agg_dict['原始数据'] = lambda x: ' | '.join(str(i) for i in x if pd.notna(i))
                                        if '处理方式' in df.columns:
                                            agg_dict['处理方式'] = 'first'
                                        if '快递单号' in df.columns:
                                            agg_dict['快递单号'] = lambda x: ' | '.join(str(i) for i in x if pd.notna(i))  # 确保转换为字符串

                                        df_grouped = df.groupby('group_key').agg(agg_dict).reset_index()
                                        self.log.emit(f"警告: 工作表 {sheet} 使用了安全聚合方式，部分数据可能不完整")

                                    # 验证分组后的数据完整性
                                    total_grouped_quantity = df_grouped['数量'].sum() if '数量' in df_grouped.columns else 0
                                    total_original_quantity = df['数量'].sum() if '数量' in df.columns else 0

                                    if total_grouped_quantity != total_original_quantity:
                                        log.warning(f"分组前后数量不一致: 原始 {total_original_quantity}, 分组后 {total_grouped_quantity}")
                                        self.log.emit(f"警告: 工作表 {sheet} 分组前后数量不一致，差异: {total_original_quantity - total_grouped_quantity}")

                                    # 删除临时分组列
                                    if 'group_key' in df_grouped.columns:
                                        df_grouped = df_grouped.drop('group_key', axis=1)

                                    # 确保必要的列存在
                                    required_columns = ['材质', '图案', '宽cm', '高cm', '图案全称', '数量', '累计长度', '原始数据', '处理方式', '快递单号']  # 修改：长cm改为高cm
                                    for col in required_columns:
                                        if col not in df_grouped.columns:
                                            df_grouped[col] = '' if col not in ['宽cm', '高cm', '数量', '累计长度'] else 0  # 修改：长cm改为高cm

                                    # 选择最终输出的列
                                    df_grouped = df_grouped[required_columns]

                                    # 新增：总操作表格优化逻辑 - 通过倍数优化分配
                                    df_grouped = self.optimize_sheet_allocation(df_grouped, sheet, merged_data)

                                    # 最终排序
                                    df_grouped = df_grouped.sort_values(
                                        by=['宽cm', '高cm'],  # 修改：长cm改为高cm
                                        ascending=[False, False]
                                    )

                                    # 写入Excel
                                    df_grouped.to_excel(writer, sheet_name=sheet, index=False)
                                    self.log.emit(f"工作表 {sheet} 已合并 {len(df_grouped)} 组数据 (原始 {pre_group_row_count} 行)")
                                else:
                                    # 数据异常工作表不需要分组，直接写入
                                    # 确保快递单号列是字符串类型
                                    if '快递单号' in df.columns:
                                        df['快递单号'] = df['快递单号'].astype(str)
                                    df.to_excel(writer, sheet_name=sheet, index=False)
                                    self.log.emit(f"工作表 {sheet} 包含 {len(df)} 行异常数据")

                    self.log.emit(f"基础素材文件夹 {base_material} 的总操作表格已生成: {total_excel_path}")
                    log.info(f"基础素材文件夹 {base_material} 的总操作表格已生成: {total_excel_path}")

                    # 验证生成的Excel文件
                    try:
                        with pd.ExcelFile(total_excel_path) as verification_xls:
                            total_rows_in_file = sum(len(pd.read_excel(verification_xls, sheet_name=sheet))
                                                    for sheet in verification_xls.sheet_names)
                            self.log.emit(f"验证: 总操作表格包含 {total_rows_in_file} 组数据")
                    except Exception as verify_error:
                        log.error(f"验证总操作表格时出错: {verify_error}")
                        self.log.emit(f"警告: 无法验证总操作表格: {verify_error}")

                except Exception as e:
                    log.error(f"生成总操作表格 {total_excel_path} 失败: {e}")
                    self.log.emit(f"生成总操作表格 {total_excel_path} 失败: {e}")
                    # 如果生成失败但有备份，尝试恢复
                    if os.path.exists(backup_path):
                        try:
                            shutil.copy2(backup_path, total_excel_path)
                            self.log.emit(f"已从备份恢复总操作表格")
                        except Exception as restore_error:
                            log.error(f"从备份恢复总操作表格失败: {restore_error}")
                    continue

            return True
        except Exception as e:
            log.error(f"合并总操作表格时发生错误: {e}")
            self.log.emit(f"合并总操作表格时发生错误: {e}")
            return False


# ---------------------------
# UI层
# ---------------------------
class TxtToExcelApp(QWidget):
    def __init__(self):
        super().__init__()
        # 先进行登录验证
        self.show_login_dialog()

        self.openai_mgr = OpenAIClientManager()
        self.openai_mgr.initialized.connect(self.handle_openai_init)
        self.openai_mgr.status_update.connect(self.update_log)
        self.setWindowTitle(f'{ROBOT_SMART_NAME}{"-Beta" if ROBOT_BOT_TAG.startswith("robot_bot") else ""}-v{ROBOT_CURRENT_VERSION}')
        self.setGeometry(100, 100, 700, 600)
        self.init_ui()
        self.check_version()
        self.openai_mgr._init_thread.start()

    def show_login_dialog(self):
        """显示登录对话框并处理登录结果"""
        login_dialog = LoginDialog(self)
        result = login_dialog.exec()  # PyQt6: exec_() 改为 exec()

        # 如果用户取消登录，则退出应用
        if result != QDialog.DialogCode.Accepted:  # PyQt6: 使用完整枚举名
            log.info("用户取消登录，退出应用")
            sys.exit(0)

    def closeEvent(self, event):
        """处理窗口关闭事件，优雅退出程序"""
        try:
            # 停止所有正在运行的任务
            if hasattr(self, 'worker') and self.worker.isRunning():
                self.worker.terminate()
                self.worker.wait()  # 等待线程结束
            # 关闭 OpenAI 客户端
            if hasattr(self, 'openai_mgr'):
                if hasattr(self.openai_mgr, '_init_thread') and self.openai_mgr._init_thread.isRunning():
                    self.openai_mgr._init_thread.quit()
                    self.openai_mgr._init_thread.wait()

            # 退出登录
            try:
                # 检查是否有有效的用户会话
                if user_session["access_token"] and user_session["refresh_token"]:
                    # 创建 Supabase 客户端
                    auth_client = create_client(SUPABASE_URL, SUPABASE_API_KEY)
                    # 设置会话
                    auth_client.auth.set_session(user_session["access_token"], user_session["refresh_token"])
                    # 退出登录
                    auth_client.auth.sign_out()
                    log.info("用户已成功退出登录")

                    # 清空用户会话
                    user_session["user"] = None
                    user_session["access_token"] = None
                    user_session["refresh_token"] = None
            except Exception as logout_error:
                log.error(f"退出登录时发生错误: {logout_error}")

            # 确保所有日志都被写入
            for handler in log.handlers[:]:
                handler.close()
                log.removeHandler(handler)
            # 退出应用
            QApplication.quit()
        except Exception as e:
            log.error(f"关闭程序时发生错误: {e}")
            sys.exit(1)  # 如果优雅退出失败，强制退出

    def handle_openai_init(self, success, msg):
        """处理OpenAI初始化结果"""
        if not success:
            self.update_log("OpenAI初始化失败: " + msg)
            QMessageBox.critical(self, "初始化错误", msg)
        else:
            self.update_log("OpenAI初始化成功: " + msg)

    def init_ui(self):
        layout = QVBoxLayout()
        self.label = QLabel('请选择一个包含多个 TXT 文件的文件夹以转换为 RPA操作表格')
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)  # PyQt6: 使用完整枚举名
        layout.addWidget(self.label)
        self.button = QPushButton('选择包含 TXT 文件的文件夹')
        self.button.clicked.connect(self.select_folder)
        layout.addWidget(self.button)
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        self.setLayout(layout)

    def check_version(self):
        """
        检查应用版本是否为最新版本。
        """
        try:
            # 获取认证客户端
            auth_client = get_authenticated_client()

            # 根据 ROBOT_BOT_TAG 判断使用哪个版本字段
            version_field = 'beta_ver' if ROBOT_BOT_TAG.startswith('robot_bot') else 'app_ver'
            response = auth_client.table('kufang_robot_config').select(version_field).execute()
            if not response.data:
                raise ValueError("未能获取最新版本信息。")
            latest_version = response.data[0][version_field]
            log.info(f"当前应用版本: {ROBOT_CURRENT_VERSION}, 最新版本: {latest_version}")
            if ROBOT_CURRENT_VERSION != latest_version:
                msg = QMessageBox()
                msg.setIcon(QMessageBox.Icon.Warning)  # PyQt6: 使用完整枚举名
                msg.setText("版本不匹配")
                msg.setInformativeText(f"请升级到最新版本：{latest_version}")
                msg.setWindowTitle("升级提醒")
                msg.exec()  # PyQt6: exec_() 改为 exec()
                sys.exit()
        except Exception as e:
            log.error(f"版本检查失败: {e}")
            QMessageBox.critical(
                self,
                "版本检查失败",
                f"无法检查版本信息，请联系管理员。\n错误: {e}"
            )
            sys.exit()

    def select_folder(self):
        if not self.openai_mgr.client:
            QMessageBox.warning(self, "警告", "OpenAI客户端仍在初始化，请稍后")
            return

        # PyQt6: 直接使用枚举值，不需要实例化
        folder_path = QFileDialog.getExistingDirectory(
            self,
            "选择包含 TXT 文件的文件夹",
            "",
            options=QFileDialog.Option.ReadOnly  # PyQt6: 直接使用枚举值
        )
        if folder_path:
            self.log_text.clear()
            self.progress_bar.setValue(0)
            self.button.setEnabled(False)
            self.worker = Worker(folder_path, self.openai_mgr)
            self.worker.progress.connect(self.update_progress)
            self.worker.log.connect(self.update_log)
            self.worker.finished.connect(self.process_finished)
            self.worker.start()

    def update_progress(self, value):
        self.progress_bar.setValue(value)

    def update_log(self, message):
        self.log_text.append(message)

    def process_finished(self, success, message):
        if success:
            QMessageBox.information(
                self,
                "成功",
                message
            )
        else:
            QMessageBox.critical(
                self,
                "失败",
                message
            )
        self.button.setEnabled(True)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TxtToExcelApp()
    window.show()
    sys.exit(app.exec())  # PyQt6: exec_() 改为 exec()