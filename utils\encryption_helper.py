#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
加密辅助模块

提供文件加密和解密功能：
1. 清洗类型标记文件的加密保存
2. 清洗类型标记文件的解密读取
3. 基于AES-256-GCM的安全加密
"""

import os
import base64
import hashlib
import logging
from typing import Optional, Tuple
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

# 配置日志
log = logging.getLogger(__name__)


class EncryptionHelper:
    """加密辅助类，用于清洗类型标记文件的安全存储"""
    
    def __init__(self):
        """初始化加密辅助类"""
        # 使用固定的盐值和密钥派生参数，确保同一台机器上的一致性
        self._salt = b'dachuan_image_tool_salt_2024'
        self._iterations = 100000  # PBKDF2迭代次数
        
    def _derive_key(self, password: str) -> bytes:
        """从密码派生加密密钥
        
        Args:
            password: 用于派生密钥的密码
            
        Returns:
            bytes: 32字节的AES-256密钥
        """
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,  # AES-256需要32字节密钥
            salt=self._salt,
            iterations=self._iterations,
        )
        return kdf.derive(password.encode('utf-8'))
    
    def _get_machine_key(self) -> str:
        """获取基于机器特征的密钥
        
        Returns:
            str: 基于机器特征生成的密钥
        """
        try:
            # 获取机器特征信息
            import platform
            import getpass
            
            # 组合多个机器特征
            machine_info = [
                platform.node(),  # 计算机名
                platform.machine(),  # 机器类型
                platform.processor(),  # 处理器信息
                getpass.getuser(),  # 当前用户名
                str(os.path.getctime(os.path.expanduser('~'))),  # 用户目录创建时间
            ]
            
            # 过滤空值并组合
            machine_info = [info for info in machine_info if info]
            machine_string = '|'.join(machine_info)
            
            # 生成哈希作为密钥
            return hashlib.sha256(machine_string.encode('utf-8')).hexdigest()
            
        except Exception as e:
            log.warning(f"获取机器特征失败，使用默认密钥: {str(e)}")
            # 如果获取机器特征失败，使用默认密钥
            return "dachuan_image_tool_default_key_2024"
    
    def encrypt_clean_type(self, clean_type: str) -> bytes:
        """加密清洗类型
        
        Args:
            clean_type: 要加密的清洗类型（'数字', '字母', '中文'）
            
        Returns:
            bytes: 加密后的数据
        """
        try:
            # 获取机器密钥
            machine_key = self._get_machine_key()
            
            # 派生加密密钥
            key = self._derive_key(machine_key)
            
            # 创建AES-GCM加密器
            aesgcm = AESGCM(key)
            
            # 生成随机nonce
            nonce = os.urandom(12)  # GCM推荐12字节nonce
            
            # 加密数据
            plaintext = clean_type.encode('utf-8')
            ciphertext = aesgcm.encrypt(nonce, plaintext, None)
            
            # 组合nonce和密文
            encrypted_data = nonce + ciphertext
            
            log.debug(f"清洗类型加密成功: {clean_type}")
            return encrypted_data
            
        except Exception as e:
            log.error(f"清洗类型加密失败: {str(e)}")
            raise
    
    def decrypt_clean_type(self, encrypted_data: bytes) -> str:
        """解密清洗类型
        
        Args:
            encrypted_data: 加密的数据
            
        Returns:
            str: 解密后的清洗类型
        """
        try:
            # 获取机器密钥
            machine_key = self._get_machine_key()
            
            # 派生加密密钥
            key = self._derive_key(machine_key)
            
            # 创建AES-GCM解密器
            aesgcm = AESGCM(key)
            
            # 分离nonce和密文
            nonce = encrypted_data[:12]
            ciphertext = encrypted_data[12:]
            
            # 解密数据
            plaintext = aesgcm.decrypt(nonce, ciphertext, None)
            clean_type = plaintext.decode('utf-8')
            
            log.debug(f"清洗类型解密成功: {clean_type}")
            return clean_type
            
        except Exception as e:
            log.error(f"清洗类型解密失败: {str(e)}")
            raise
    
    def save_encrypted_clean_type(self, library_path: str, clean_type: str) -> bool:
        """保存加密的清洗类型到文件
        
        Args:
            library_path: 图库路径
            clean_type: 清洗类型
            
        Returns:
            bool: 是否保存成功
        """
        try:
            if not library_path or not clean_type:
                return False
            
            # 创建.index隐藏文件夹
            index_dir = os.path.join(library_path, '.index')
            os.makedirs(index_dir, exist_ok=True)
            
            # 加密清洗类型
            encrypted_data = self.encrypt_clean_type(clean_type)
            
            # 保存到文件（使用base64编码以便文本存储）
            marker_file = os.path.join(index_dir, 'clean_type_marker.enc')
            encoded_data = base64.b64encode(encrypted_data).decode('ascii')
            
            with open(marker_file, 'w', encoding='ascii') as f:
                f.write(encoded_data)
            
            log.info(f"已保存加密的清洗类型标记: {clean_type}")
            return True
            
        except Exception as e:
            log.error(f"保存加密清洗类型失败: {str(e)}")
            return False
    
    def load_encrypted_clean_type(self, library_path: str) -> Optional[str]:
        """从文件加载并解密清洗类型
        
        Args:
            library_path: 图库路径
            
        Returns:
            Optional[str]: 解密后的清洗类型，失败时返回None
        """
        try:
            if not library_path:
                return None
            
            # 检查加密标记文件是否存在
            marker_file = os.path.join(library_path, '.index', 'clean_type_marker.enc')
            if not os.path.exists(marker_file):
                # 尝试读取旧的明文文件并迁移
                return self._migrate_from_plaintext(library_path)
            
            # 读取加密文件
            with open(marker_file, 'r', encoding='ascii') as f:
                encoded_data = f.read().strip()
            
            # 解码base64
            encrypted_data = base64.b64decode(encoded_data)
            
            # 解密清洗类型
            clean_type = self.decrypt_clean_type(encrypted_data)
            
            # 验证清洗类型是否有效
            if clean_type in ['数字', '字母', '中文']:
                log.info(f"已加载加密的清洗类型标记: {clean_type}")
                return clean_type
            else:
                log.warning(f"无效的清洗类型: {clean_type}")
                return None
                
        except Exception as e:
            log.error(f"加载加密清洗类型失败: {str(e)}")
            return None
    
    def _migrate_from_plaintext(self, library_path: str) -> Optional[str]:
        """从明文文件迁移到加密文件
        
        Args:
            library_path: 图库路径
            
        Returns:
            Optional[str]: 迁移后的清洗类型
        """
        try:
            # 检查旧的明文文件
            old_marker_file = os.path.join(library_path, '.index', 'clean_type_marker.txt')
            if not os.path.exists(old_marker_file):
                return None
            
            # 读取明文文件
            with open(old_marker_file, 'r', encoding='utf-8') as f:
                clean_type = f.read().strip()
            
            # 验证清洗类型
            if clean_type not in ['数字', '字母', '中文']:
                return None
            
            # 保存为加密文件
            if self.save_encrypted_clean_type(library_path, clean_type):
                # 删除旧的明文文件
                try:
                    os.remove(old_marker_file)
                    log.info(f"已将明文清洗类型标记迁移为加密格式: {clean_type}")
                except Exception as e:
                    log.warning(f"删除旧明文文件失败: {str(e)}")
                
                return clean_type
            
            return None
            
        except Exception as e:
            log.error(f"从明文迁移失败: {str(e)}")
            return None


# 全局加密辅助实例
_encryption_helper = None

def get_encryption_helper() -> EncryptionHelper:
    """获取全局加密辅助实例
    
    Returns:
        EncryptionHelper: 加密辅助实例
    """
    global _encryption_helper
    if _encryption_helper is None:
        _encryption_helper = EncryptionHelper()
    return _encryption_helper
