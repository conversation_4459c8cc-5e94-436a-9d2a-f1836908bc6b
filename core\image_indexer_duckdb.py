#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图片索引器DuckDB实现模块

提供基于DuckDB的图片索引和检索功能：
1. 使用DuckDB作为后端存储
2. 统一使用UTF-8编码
3. 优化Windows路径处理
4. 多级索引策略
5. 规范化文件名处理
6. 支持进度显示和批量处理
7. 多线程并行处理，提高索引速度
8. 异步IO操作，减少等待时间
"""

import os
import sys
import logging
import duckdb
import time
import concurrent.futures
import threading
import queue
import warnings
import struct
import shutil
from datetime import datetime
from PyQt6.QtCore import QObject, pyqtSignal
from typing import Optional, List, Dict, Any, Tuple

# 使用统一的PIL配置管理
try:
    from utils.pil_config import PILConfigManager
    # 为图片索引器配置PIL（使用最大限制以处理超大图片）
    PILConfigManager.configure_pil(
        max_image_pixels=1_500_000_000,  # 1.5G像素
        load_truncated_images=True,
        ignore_decompression_warning=True,
        force_reconfigure=True  # 强制重新配置以确保使用正确的参数
    )
    log = logging.getLogger("ImageIndexerDuckDB")
    config = PILConfigManager.get_current_config()
    if config:
        log.info(f"PIL配置完成: MAX_IMAGE_PIXELS={config['max_image_pixels']:,}, "
                f"LOAD_TRUNCATED_IMAGES={config['load_truncated_images']}")
except ImportError:
    # 如果统一配置模块不可用，使用原来的配置方式
    try:
        from PIL import Image, ExifTags, ImageFile
        Image.MAX_IMAGE_PIXELS = 1_500_000_000
        ImageFile.LOAD_TRUNCATED_IMAGES = True
        warnings.filterwarnings("ignore", category=Image.DecompressionBombWarning)
        log = logging.getLogger("ImageIndexerDuckDB")
        log.info(f"PIL配置完成(备用方式): MAX_IMAGE_PIXELS={Image.MAX_IMAGE_PIXELS:,}")
    except ImportError:
        log = logging.getLogger("ImageIndexerDuckDB")
        log.warning("PIL库未安装，图片尺寸获取功能将受限")

# 导入自定义模块
from utils.parallel_manager import ParallelManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger("ImageIndexerDuckDB")

class ImageIndexerDuckDB(QObject):
    """图片索引器，用于为图片建立索引并快速检索

    优化特性：
    1. 使用DuckDB作为后端存储
    2. 统一使用UTF-8编码
    3. 优化Windows路径处理
    4. 多级索引策略
    5. 规范化文件名处理
    6. 支持进度显示和批量处理
    """

    # 进度信号
    progress_signal = pyqtSignal(int)  # 进度值 (0-100)
    status_signal = pyqtSignal(str)    # 状态信息
    error_signal = pyqtSignal(str)     # 错误信息

    # 批处理相关常量
    BATCH_SIZE = 100  # 每批处理的文件数
    COMMIT_INTERVAL = 1000  # 每多少条记录提交一次事务

    def __init__(self, fast_mode=False):
        """初始化图片索引器

        Args:
            fast_mode: 是否使用快速模式，跳过图片尺寸和EXIF数据的获取
        """
        super().__init__()
        self.library_path = None
        self._indexed = False
        self._supported_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif')
        self.db = None
        self._stop_requested = False
        self._lock = threading.Lock()
        self.fast_mode = fast_mode

        # 创建并行管理器
        self.parallel_manager = ParallelManager()

        # 批处理相关参数
        self.batch_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.worker_threads = []
        self.max_workers = min(32, os.cpu_count() * 2) if os.cpu_count() else 8

    def stop_indexing(self):
        """请求停止索引过程"""
        self._stop_requested = True
        self.status_signal.emit("正在停止索引...")
    
    def _is_transaction_active(self):
        """检查是否有活跃的事务"""
        try:
            # 尝试执行一个简单的查询来检查事务状态
            self.db.execute("SELECT 1")
            return True
        except Exception:
            return False
    
    def _safe_commit(self):
        """安全地提交事务"""
        try:
            self.db.commit()
            return True
        except Exception as e:
            if "no transaction is active" in str(e).lower():
                # 没有活跃事务，这是正常情况
                return True
            else:
                # 其他错误需要抛出
                raise e
    
    def _safe_rollback(self):
        """安全地回滚事务"""
        try:
            self.db.execute("ROLLBACK")
            return True
        except Exception as e:
            if "no transaction is active" in str(e).lower():
                # 没有活跃事务，这是正常情况
                return True
            else:
                # 其他错误记录但不抛出
                self.error_signal.emit(f"回滚事务失败: {str(e)}")
                return False

    def _init_db(self):
        """初始化DuckDB数据库"""
        try:
            if not self.library_path:
                self.error_signal.emit("图库路径未设置")
                return False

            # 规范化路径并创建.index目录
            index_dir = os.path.join(self.library_path, '.index')
            os.makedirs(index_dir, exist_ok=True)

            # 设置数据库文件路径
            db_path = os.path.join(index_dir, 'image_index.db')
            self.db_path = db_path  # 保存数据库路径供其他方法使用

            # 关闭已存在的连接
            if self.db:
                try:
                    self.db.close()
                except:
                    pass

            # 尝试连接数据库，处理编码错误
            try:
                self.db = duckdb.connect(db_path)
                # 测试数据库连接和基本功能
                self._test_database_health()
            except Exception as db_error:
                # 检查是否是编码相关错误
                error_msg = str(db_error).lower()
                if any(keyword in error_msg for keyword in ['utf-8', 'decode', 'encoding', 'invalid continuation byte']):
                    self.status_signal.emit("检测到数据库编码问题，正在尝试修复...")
                    return self._handle_encoding_error(db_path, db_error)
                else:
                    # 其他类型的数据库错误
                    raise db_error

            # 创建图片信息表
            self.db.execute("""
                CREATE SEQUENCE IF NOT EXISTS image_files_id_seq;
                CREATE TABLE IF NOT EXISTS image_files (
                    id INTEGER PRIMARY KEY DEFAULT nextval('image_files_id_seq'),
                    filename VARCHAR NOT NULL,
                    name_without_ext VARCHAR NOT NULL,
                    extension VARCHAR NOT NULL,
                    relative_path VARCHAR NOT NULL,
                    full_path VARCHAR NOT NULL,
                    has_chinese BOOLEAN NOT NULL,
                    normalized_filename VARCHAR NOT NULL,
                    normalized_name VARCHAR NOT NULL,
                    file_size BIGINT NOT NULL,
                    modified_time TIMESTAMP NOT NULL,
                    created_time TIMESTAMP NOT NULL,
                    indexed_time TIMESTAMP NOT NULL,
                    width INTEGER DEFAULT 0,
                    height INTEGER DEFAULT 0,
                    has_exif BOOLEAN DEFAULT FALSE,
                    orientation INTEGER DEFAULT 1,
                    UNIQUE(relative_path)
                )
            """)

            # 检查是否需要添加新列
            try:
                # 检查是否存在width列
                result = self.db.execute("SELECT width FROM image_files LIMIT 1").fetchone()
            except Exception:
                # 如果不存在，添加新列
                self.db.execute("ALTER TABLE image_files ADD COLUMN width INTEGER DEFAULT 0")
                self.db.execute("ALTER TABLE image_files ADD COLUMN height INTEGER DEFAULT 0")
                self.db.execute("ALTER TABLE image_files ADD COLUMN has_exif BOOLEAN DEFAULT FALSE")
                self.db.execute("ALTER TABLE image_files ADD COLUMN orientation INTEGER DEFAULT 1")
                self.status_signal.emit("已添加图像维度和EXIF数据列")

            # 检查是否需要添加宽高比列
            try:
                # 检查是否存在aspect_ratio列
                result = self.db.execute("SELECT aspect_ratio FROM image_files LIMIT 1").fetchone()
            except Exception:
                # 如果不存在，添加宽高比列
                self.db.execute("ALTER TABLE image_files ADD COLUMN aspect_ratio REAL DEFAULT 0")
                self.status_signal.emit("已添加图像宽高比列")

            # 检查是否需要添加重复文件标识列
            try:
                # 检查是否存在is_duplicate列
                result = self.db.execute("SELECT is_duplicate FROM image_files LIMIT 1").fetchone()
            except Exception:
                # 如果不存在，添加重复文件标识列
                self.db.execute("ALTER TABLE image_files ADD COLUMN is_duplicate BOOLEAN DEFAULT FALSE")
                self.status_signal.emit("已添加重复文件标识列")

            # 创建索引
            self.db.execute("CREATE INDEX IF NOT EXISTS idx_filename ON image_files(filename)")
            self.db.execute("CREATE INDEX IF NOT EXISTS idx_name_without_ext ON image_files(name_without_ext)")
            self.db.execute("CREATE INDEX IF NOT EXISTS idx_normalized_name ON image_files(normalized_name)")
            self.db.execute("CREATE INDEX IF NOT EXISTS idx_relative_path ON image_files(relative_path)")
            self.db.execute("CREATE INDEX IF NOT EXISTS idx_full_path ON image_files(full_path)")

            # 提交更改
            self.db.commit()

            self.status_signal.emit(f"成功初始化DuckDB数据库: {db_path}")
            return True

        except Exception as e:
            self.error_signal.emit(f"初始化数据库失败: {str(e)}")
            if self.db:
                try:
                    self.db.close()
                except:
                    pass
                self.db = None
            return False

    def _test_database_health(self):
        """测试数据库健康状态"""
        try:
            # 测试基本查询功能
            self.db.execute("SELECT 1").fetchone()

            # 如果表存在，测试读取功能
            tables = self.db.execute("SHOW TABLES").fetchall()
            table_names = [table[0] for table in tables]

            if 'image_files' in table_names:
                # 测试读取表数据，这里可能会遇到编码问题
                try:
                    result = self.db.execute("SELECT COUNT(*) FROM image_files").fetchone()
                    log.debug(f"数据库健康检查通过，当前记录数: {result[0] if result else 0}")
                except Exception as read_error:
                    # 如果读取数据时遇到编码错误，抛出异常让上层处理
                    if any(keyword in str(read_error).lower() for keyword in ['utf-8', 'decode', 'encoding']):
                        raise read_error
                    log.warning(f"读取数据时出现问题，但不是编码错误: {read_error}")

        except Exception as e:
            log.error(f"数据库健康检查失败: {str(e)}")
            raise e

    def _handle_encoding_error(self, db_path, original_error):
        """处理数据库编码错误"""
        try:
            self.status_signal.emit("数据库存在编码问题，正在备份并重建...")

            # 备份损坏的数据库文件
            backup_path = f"{db_path}.corrupted.{int(time.time())}"
            if os.path.exists(db_path):
                try:
                    shutil.move(db_path, backup_path)
                    self.status_signal.emit(f"已备份损坏的数据库到: {backup_path}")
                except Exception as backup_error:
                    log.warning(f"备份数据库失败: {backup_error}")
                    # 如果备份失败，直接删除损坏的数据库
                    try:
                        os.remove(db_path)
                        self.status_signal.emit("已删除损坏的数据库文件")
                    except:
                        pass

            # 创建新的数据库连接
            self.db = duckdb.connect(db_path)
            self.status_signal.emit("已创建新的数据库文件")

            return True

        except Exception as e:
            self.error_signal.emit(f"处理编码错误失败: {str(e)}")
            return False

    def normalize_path(self, path):
        """规范化路径，统一使用正斜杠，处理编码问题

        优化点：
        1. 更好地处理Windows路径分隔符
        2. 处理相对路径和绝对路径
        3. 处理中文路径的编码问题
        4. 移除路径中的非法字符
        """
        if not path:
            return path

        try:
            # 处理编码问题
            if isinstance(path, bytes):
                # 尝试多种编码方式
                for encoding in ['utf-8', 'gbk', 'gb2312', sys.getfilesystemencoding(), 'latin1']:
                    try:
                        path = path.decode(encoding)
                        break
                    except (UnicodeDecodeError, LookupError):
                        continue
                else:
                    # 如果所有编码都失败，使用错误处理方式
                    path = path.decode('utf-8', errors='replace')
                    log.warning(f"路径编码解码失败，使用替换字符: {path}")
            elif not isinstance(path, str):
                path = str(path)

            # 统一使用反斜杠，处理Windows路径
            path = path.replace('/', '\\')

            # 处理连续的反斜杠
            while '\\\\' in path:
                path = path.replace('\\\\', '\\')

            # 移除路径首尾的空白字符
            path = path.strip()

            # 移除路径末尾的反斜杠（除非是根路径）
            if path.endswith('\\') and len(path) > 3:  # 考虑C:\这样的根路径
                path = path.rstrip('\\')

            return path

        except Exception as e:
            log.error(f"路径规范化失败: {str(e)}")
            # 返回原始路径而不是None，避免完全失败
            return str(path) if path else None

    def normalize_filename(self, filename):
        """规范化文件名，处理大小写和特殊字符

        优化点：
        1. 更好地处理中文文件名
        2. 处理特殊字符和非法字符
        3. 统一编码和格式
        4. 保留原始文件名信息用于调试
        """
        if not filename:
            return filename

        try:
            # 确保文件名是字符串类型
            if not isinstance(filename, str):
                filename = str(filename)

            # 处理编码问题
            if isinstance(filename, bytes):
                # 尝试多种编码方式
                for encoding in ['utf-8', 'gbk', 'gb2312', sys.getfilesystemencoding(), 'latin1']:
                    try:
                        filename = filename.decode(encoding)
                        break
                    except (UnicodeDecodeError, LookupError):
                        continue
                else:
                    # 如果所有编码都失败，使用错误处理方式
                    filename = filename.decode('utf-8', errors='replace')
                    log.warning(f"文件名编码解码失败，使用替换字符: {filename}")

            # 保存原始文件名（用于日志和调试）
            original_filename = filename

            # 记录原始文件名（用于调试）
            log.debug(f"规范化文件名前: '{original_filename}'")

            # 转换为小写并去除首尾空格
            filename = filename.lower().strip()

            # 移除不必要的空格和控制字符
            filename = ' '.join(filename.split())

            # 移除Windows文件系统不允许的字符（< > : " / \ | ? *）
            for char in '<>:"/\\|?*':
                filename = filename.replace(char, '')

            # 如果规范化后文件名为空，使用原始文件名的哈希值
            if not filename:
                import hashlib
                filename = hashlib.md5(original_filename.encode('utf-8')).hexdigest()
                log.warning(f"文件名规范化后为空，使用哈希值代替: {original_filename} -> {filename}")

            # 记录规范化后的文件名（用于调试）
            log.debug(f"规范化文件名后: '{filename}'")

            # 对于中文文件名，保留原始文件名作为备用索引
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in original_filename)
            if has_chinese:
                # 返回规范化后的文件名，但在索引时会同时使用原始文件名
                log.debug(f"检测到中文文件名: '{original_filename}'")
                return filename, original_filename.lower().strip()

            return filename

        except Exception as e:
            log.error(f"文件名规范化失败: {str(e)}")
            # 返回原始文件名而不是None，避免完全失败
            return str(filename) if filename else None

    def set_library_path(self, path):
        """设置图片库路径"""
        self.library_path = self.normalize_path(path)
        if self.library_path:
            # 初始化数据库
            if self._init_db():
                self._indexed = True
                log.info(f"成功设置图库路径: {self.library_path}")
                return True
        return False

    def _get_file_info(self, file_path, fast_mode=None):
        """获取文件的详细信息，包括图像维度和EXIF数据

        Args:
            file_path: 文件的完整路径
            fast_mode: 是否使用快速模式，跳过图片尺寸和EXIF数据的获取

        Returns:
            dict: 文件信息字典，如果出错则返回None
        """
        try:
            # 获取文件基本信息
            stat = os.stat(file_path)
            filename = os.path.basename(file_path)
            name_without_ext = os.path.splitext(filename)[0]
            ext = os.path.splitext(filename)[1].lower()

            # 检查文件扩展名
            if ext not in self._supported_extensions:
                return None

            # 检测是否包含中文字符
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in filename)

            # 规范化文件名
            norm_filename = self.normalize_filename(filename)
            if isinstance(norm_filename, tuple):
                norm_filename = norm_filename[0]

            norm_name = self.normalize_filename(name_without_ext)
            if isinstance(norm_name, tuple):
                norm_name = norm_name[0]

            # 获取图像维度和EXIF数据
            width, height, has_exif, orientation = self._get_image_dimensions(file_path, fast_mode)

            # 构建文件信息
            file_info = {
                'filename': filename,
                'name_without_ext': name_without_ext,
                'extension': ext,
                'has_chinese': has_chinese,
                'normalized_filename': norm_filename,
                'normalized_name': norm_name,
                'file_size': stat.st_size,
                'modified_time': datetime.fromtimestamp(stat.st_mtime),
                'created_time': datetime.fromtimestamp(stat.st_mtime),  # 使用修改时间代替创建时间，避免弃用警告
                'indexed_time': datetime.now(),
                'width': width,
                'height': height,
                'has_exif': has_exif,
                'orientation': orientation
            }

            return file_info

        except Exception as e:
            log.error(f"获取文件信息失败 {file_path}: {str(e)}")
            return None

    def _get_image_dimensions(self, file_path, fast_mode=None):
        """获取图像维度和EXIF数据

        Args:
            file_path: 图像文件路径
            fast_mode: 是否使用快速模式，在快速模式下只获取基本尺寸，跳过详细EXIF数据

        Returns:
            Tuple[int, int, bool, int]: (宽度, 高度, 是否有EXIF数据, 方向)
        """
        # 如果未指定fast_mode，使用类的fast_mode属性
        if fast_mode is None:
            fast_mode = self.fast_mode

        try:
            # 默认值
            width = 0
            height = 0
            has_exif = False
            orientation = 1  # 1表示正常方向

            # 尝试使用PIL获取图像信息
            try:
                from PIL import Image, ExifTags, ImageFile

                # 获取文件大小用于日志记录
                file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                file_size_mb = file_size / (1024 * 1024)

                # 打开图像但不完全加载
                with Image.open(file_path) as img:
                    # 获取图像尺寸（这是最重要的，即使在快速模式下也要获取）
                    width, height = img.size

                    # 使用统一的PIL配置管理器记录图片信息
                    try:
                        from utils.pil_config import PILConfigManager
                        PILConfigManager.log_image_info(width, height, file_path)

                        # 验证图片尺寸是否在允许范围内
                        if not PILConfigManager.validate_image_size(width, height, file_path):
                            log.warning(f"图片尺寸超出PIL限制，但仍成功获取: {os.path.basename(file_path)}")
                    except ImportError:
                        # 如果统一配置模块不可用，使用原来的日志方式
                        total_pixels = width * height
                        if total_pixels > 100_000_000:  # 1亿像素以上
                            estimated_memory_mb = (total_pixels * 3) / (1024 * 1024)
                            log.info(f"处理超大图片: {os.path.basename(file_path)}, "
                                    f"尺寸: {width}x{height}, 像素: {total_pixels:,}, "
                                    f"文件大小: {file_size_mb:.1f}MB, 估算内存: {estimated_memory_mb:.1f}MB")
                        elif total_pixels > 50_000_000:  # 5000万像素以上
                            log.debug(f"处理大图片: {os.path.basename(file_path)}, "
                                     f"尺寸: {width}x{height}, 像素: {total_pixels:,}")
                        else:
                            log.debug(f"获取图片尺寸: {os.path.basename(file_path)} -> {width}x{height}")

                    # 在快速模式下，跳过EXIF数据获取以提高速度
                    if not fast_mode:
                        # 尝试获取EXIF数据
                        try:
                            # 使用更现代的方法获取EXIF数据
                            exif_dict = img.getexif()
                            if exif_dict:
                                has_exif = True

                                # 获取方向信息 (Orientation tag = 274)
                                if 274 in exif_dict:
                                    orientation = exif_dict[274]
                                    log.debug(f"获取EXIF方向信息: {orientation}")
                            else:
                                # 尝试旧方法作为备用
                                if hasattr(img, '_getexif') and callable(img._getexif):
                                    exif = img._getexif()
                                    if exif:
                                        has_exif = True

                                        # 获取方向信息
                                        orientation_key = None
                                        for key, value in ExifTags.TAGS.items():
                                            if value == 'Orientation':
                                                orientation_key = key
                                                break

                                        if orientation_key and orientation_key in exif:
                                            orientation = exif[orientation_key]
                                            log.debug(f"获取EXIF方向信息(旧方法): {orientation}")
                        except Exception as exif_error:
                            log.debug(f"获取EXIF数据失败，但图片尺寸已获取: {str(exif_error)}")
                            # EXIF获取失败不影响尺寸信息
                    else:
                        log.debug(f"快速模式：跳过EXIF数据获取，仅获取图片尺寸")

            except ImportError:
                log.warning("PIL库未安装，无法获取图像尺寸和EXIF数据")
            except Exception as pil_error:
                # 增强错误处理，提供更详细的错误信息
                error_msg = str(pil_error)
                file_name = os.path.basename(file_path)

                # 特殊处理decompression bomb错误
                if "decompression bomb" in error_msg.lower() or "exceeds limit" in error_msg.lower():
                    log.warning(f"图片过大被PIL安全限制阻止: {file_name}, 错误: {error_msg}")

                    # 使用统一的PIL配置管理器获取当前限制信息
                    try:
                        from utils.pil_config import PILConfigManager
                        config = PILConfigManager.get_current_config()
                        if config:
                            log.info(f"当前PIL限制: MAX_IMAGE_PIXELS={config['max_image_pixels']:,}")
                        else:
                            log.info("PIL配置信息不可用")
                    except ImportError:
                        # 如果统一配置模块不可用，使用原来的方式
                        try:
                            from PIL import Image
                            log.info(f"当前PIL限制: MAX_IMAGE_PIXELS={Image.MAX_IMAGE_PIXELS:,}")
                        except:
                            log.info("无法获取PIL限制信息")
                else:
                    log.warning(f"使用PIL获取图像信息失败: {file_name}, 错误: {error_msg}")

                # 如果PIL失败，尝试其他方法获取尺寸
                width, height = self._get_image_size_fallback(file_path)

            # 确保至少获取到了图片尺寸
            if width > 0 and height > 0:
                mode_text = "快速模式" if fast_mode else "完整模式"
                log.debug(f"成功获取图片信息({mode_text}): {file_path} -> {width}x{height}, EXIF: {has_exif}")
            else:
                log.warning(f"无法获取图片尺寸: {file_path}")

            return width, height, has_exif, orientation

        except Exception as e:
            log.error(f"获取图像维度失败: {str(e)}")
            return 0, 0, False, 1

    def _get_image_size_fallback(self, file_path):
        """备用方法获取图片尺寸（当PIL失败时）

        Args:
            file_path: 图像文件路径

        Returns:
            Tuple[int, int]: (宽度, 高度)
        """
        file_name = os.path.basename(file_path)
        log.info(f"尝试使用备用方法获取图片尺寸: {file_name}")

        try:
            # 检查文件是否存在和可读
            if not os.path.exists(file_path):
                log.warning(f"备用方法: 文件不存在 {file_name}")
                return 0, 0

            if not os.access(file_path, os.R_OK):
                log.warning(f"备用方法: 文件不可读 {file_name}")
                return 0, 0

            # 尝试使用文件扩展名判断格式
            file_ext = os.path.splitext(file_path)[1].lower()
            log.debug(f"备用方法检测到文件扩展名: {file_ext} for {file_name}")

            if file_ext in ['.jpg', '.jpeg']:
                width, height = self._get_jpeg_size(file_path)
            elif file_ext == '.png':
                width, height = self._get_png_size(file_path)
            elif file_ext in ['.bmp', '.tiff', '.tif']:
                width, height = self._get_basic_image_size(file_path)
            else:
                log.warning(f"备用方法: 不支持的图片格式 {file_ext} for {file_name}")
                return 0, 0

            if width > 0 and height > 0:
                log.info(f"备用方法成功获取尺寸: {file_name} -> {width}x{height}")
            else:
                log.warning(f"备用方法获取到无效尺寸: {file_name} -> {width}x{height}")

            return width, height

        except Exception as e:
            log.warning(f"备用方法获取图片尺寸失败: {file_name}, 错误: {str(e)}")
            return 0, 0

    def _get_jpeg_size(self, file_path):
        """获取JPEG图片尺寸"""
        try:
            with open(file_path, 'rb') as f:
                # 跳过JPEG文件头
                f.seek(2)
                while True:
                    marker = f.read(2)
                    if not marker:
                        break
                    if marker[0] != 0xFF:
                        break

                    # SOF markers (Start of Frame)
                    if marker[1] in [0xC0, 0xC1, 0xC2, 0xC3, 0xC5, 0xC6, 0xC7, 0xC9, 0xCA, 0xCB, 0xCD, 0xCE, 0xCF]:
                        f.seek(3, 1)  # 跳过长度和精度
                        height = struct.unpack('>H', f.read(2))[0]
                        width = struct.unpack('>H', f.read(2))[0]
                        return width, height
                    else:
                        # 跳过这个段
                        length = struct.unpack('>H', f.read(2))[0]
                        f.seek(length - 2, 1)
            return 0, 0
        except Exception:
            return 0, 0

    def _get_png_size(self, file_path):
        """获取PNG图片尺寸"""
        try:
            with open(file_path, 'rb') as f:
                # PNG文件头: 8字节签名 + IHDR块
                f.seek(16)  # 跳过PNG签名和IHDR块头
                width = struct.unpack('>I', f.read(4))[0]
                height = struct.unpack('>I', f.read(4))[0]
                return width, height
        except Exception:
            return 0, 0

    def _get_basic_image_size(self, file_path):
        """获取基本图片格式尺寸（BMP, TIFF等）"""
        try:
            # 这里可以添加更多格式的支持
            # 目前返回0，让PIL处理
            return 0, 0
        except Exception:
            return 0, 0

    def _check_duplicate_files(self, name_without_ext):
        """检查是否存在同名重复文件

        Args:
            name_without_ext: 不含扩展名的文件名

        Returns:
            bool: 如果存在同名重复文件则返回True
        """
        try:
            if not self.db:
                return False

            # 检查是否存在同名的其他文件
            # 包括原文件和_2文件的互相检查
            base_name = name_without_ext
            if base_name.endswith('_2'):
                # 如果当前是_2文件，检查是否存在原文件
                original_name = base_name[:-2]  # 去掉_2后缀
                result = self.db.execute("""
                    SELECT COUNT(*) FROM image_files
                    WHERE name_without_ext = ?
                """, [original_name]).fetchone()
                return result[0] > 0 if result else False
            else:
                # 如果当前是原文件，检查是否存在_2文件
                duplicate_name = base_name + '_2'
                result = self.db.execute("""
                    SELECT COUNT(*) FROM image_files
                    WHERE name_without_ext = ?
                """, [duplicate_name]).fetchone()
                return result[0] > 0 if result else False

        except Exception as e:
            log.debug(f"检查重复文件失败: {str(e)}")
            return False

    def _update_duplicate_status(self, name_without_ext):
        """更新重复文件状态

        当发现新的重复文件时，更新相关文件的重复状态

        Args:
            name_without_ext: 不含扩展名的文件名
        """
        try:
            if not self.db:
                return

            base_name = name_without_ext
            if base_name.endswith('_2'):
                # 如果当前是_2文件，更新原文件的重复状态
                original_name = base_name[:-2]
                self.db.execute("""
                    UPDATE image_files
                    SET is_duplicate = TRUE
                    WHERE name_without_ext = ?
                """, [original_name])
            else:
                # 如果当前是原文件，更新_2文件的重复状态
                duplicate_name = base_name + '_2'
                self.db.execute("""
                    UPDATE image_files
                    SET is_duplicate = TRUE
                    WHERE name_without_ext = ?
                """, [duplicate_name])

        except Exception as e:
            log.debug(f"更新重复文件状态失败: {str(e)}")

    def _check_duplicate_by_aspect_ratio(self, name_without_ext, aspect_ratio):
        """基于宽高比检查重复文件
        
        Args:
            name_without_ext: 不含扩展名的文件名
            aspect_ratio: 当前图片的宽高比
            
        Returns:
            bool: 如果存在相同宽高比的同名文件则返回True
        """
        try:
            if not self.db:
                return False
                
            # 移除可能的序号后缀，获取基础名称
            base_name = self._get_base_name(name_without_ext)
            
            # 查找所有同基础名称的文件
            result = self.db.execute("""
                SELECT aspect_ratio FROM image_files 
                WHERE name_without_ext LIKE ? OR name_without_ext = ?
                ORDER BY name_without_ext
            """, [f"{base_name}_%", base_name]).fetchall()
            
            if not result:
                return False
                
            # 检查是否存在相同宽高比的文件
            tolerance = 0.01  # 宽高比容差
            for row in result:
                existing_ratio = row[0] if row[0] is not None else 0
                if abs(existing_ratio - aspect_ratio) < tolerance:
                    return True
                    
            return False
            
        except Exception as e:
            log.debug(f"基于宽高比检查重复文件失败: {str(e)}")
            return False
            
    def _update_duplicate_status_by_aspect_ratio(self, name_without_ext):
        """基于宽高比更新重复文件状态
        
        Args:
            name_without_ext: 不含扩展名的文件名
        """
        try:
            if not self.db:
                return
                
            # 移除可能的序号后缀，获取基础名称
            base_name = self._get_base_name(name_without_ext)
            
            # 更新所有同基础名称文件的重复状态
            self.db.execute("""
                UPDATE image_files 
                SET is_duplicate = TRUE 
                WHERE name_without_ext LIKE ? OR name_without_ext = ?
            """, [f"{base_name}_%", base_name])
            
        except Exception as e:
            log.debug(f"基于宽高比更新重复文件状态失败: {str(e)}")
            
    def _get_base_name(self, name_without_ext):
        """获取文件的基础名称（移除序号后缀）
        
        Args:
            name_without_ext: 不含扩展名的文件名
            
        Returns:
            str: 基础文件名
        """
        import re
        # 匹配 _数字 格式的后缀
        match = re.match(r'^(.+?)(_\d+)?$', name_without_ext)
        if match:
            return match.group(1)
        return name_without_ext

    def index_image(self, rel_path, fast_mode=None):
        """索引单个图片

        Args:
            rel_path: 相对于图片库的路径
            fast_mode: 是否使用快速模式，跳过图片尺寸和EXIF数据的获取

        Returns:
            bool: 是否成功索引
        """
        # 如果未指定fast_mode，使用类的fast_mode属性
        if fast_mode is None:
            fast_mode = self.fast_mode

        if not self.library_path or not self.db:
            return False

        try:
            # 规范化路径
            rel_path = self.normalize_path(rel_path)
            full_path = os.path.join(self.library_path, rel_path)

            if not os.path.exists(full_path):
                return False

            # 获取文件信息
            file_info = self._get_file_info(full_path, fast_mode)
            if not file_info:
                return False

            # 添加路径信息，确保路径规范化
            file_info['relative_path'] = rel_path  # rel_path 已经在上面规范化过了
            file_info['full_path'] = self.normalize_path(full_path)

            # 在增量索引时不需要单独的事务，使用外部事务管理
            try:
                # 检查是否已存在相同路径的记录
                result = self.db.execute("""
                    SELECT id FROM image_files WHERE relative_path = ?
                """, [rel_path]).fetchone()

                if result:
                    # 计算宽高比和重复文件标识
                    width = file_info.get('width', 0)
                    height = file_info.get('height', 0)
                    aspect_ratio = width / height if height > 0 else 0

                    # 检查是否为重复文件（包括原图和_2图）
                    is_duplicate = (file_info['name_without_ext'].endswith('_2') or
                                  self._check_duplicate_files(file_info['name_without_ext']))

                    # 更新现有记录
                    self.db.execute("""
                        UPDATE image_files SET
                            filename = ?,
                            name_without_ext = ?,
                            extension = ?,
                            full_path = ?,
                            has_chinese = ?,
                            normalized_filename = ?,
                            normalized_name = ?,
                            file_size = ?,
                            modified_time = ?,
                            created_time = ?,
                            indexed_time = ?,
                            width = ?,
                            height = ?,
                            has_exif = ?,
                            orientation = ?,
                            aspect_ratio = ?,
                            is_duplicate = ?
                        WHERE relative_path = ?
                    """, [
                        file_info['filename'],
                        file_info['name_without_ext'],
                        file_info['extension'],
                        file_info['full_path'],
                        file_info['has_chinese'],
                        file_info['normalized_filename'],
                        file_info['normalized_name'],
                        file_info['file_size'],
                        file_info['modified_time'],
                        file_info['created_time'],
                        file_info['indexed_time'],
                        width,
                        height,
                        file_info.get('has_exif', False),
                        file_info.get('orientation', 1),
                        aspect_ratio,
                        is_duplicate,
                        rel_path
                    ])
                else:
                    # 插入新记录
                    # 检查是否需要手动管理ID
                    use_manual_id = hasattr(self, '_next_id') and self._next_id is not None

                    if use_manual_id:
                        # 计算宽高比和重复文件标识
                        width = file_info.get('width', 0)
                        height = file_info.get('height', 0)
                        aspect_ratio = width / height if height > 0 else 0

                        # 使用新的基于宽高比的重复检测逻辑
                        is_duplicate = self._check_duplicate_by_aspect_ratio(file_info['name_without_ext'], aspect_ratio)

                        # 手动分配ID
                        self.db.execute("""
                            INSERT INTO image_files (
                                id, filename, name_without_ext, extension, relative_path, full_path,
                                has_chinese, normalized_filename, normalized_name,
                                file_size, modified_time, created_time, indexed_time,
                                width, height, has_exif, orientation, aspect_ratio, is_duplicate
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, [
                            self._next_id,
                            file_info['filename'],
                            file_info['name_without_ext'],
                            file_info['extension'],
                            file_info['relative_path'],
                            file_info['full_path'],
                            file_info['has_chinese'],
                            file_info['normalized_filename'],
                            file_info['normalized_name'],
                            file_info['file_size'],
                            file_info['modified_time'],
                            file_info['created_time'],
                            file_info['indexed_time'],
                            width,
                            height,
                            file_info.get('has_exif', False),
                            file_info.get('orientation', 1),
                            aspect_ratio,
                            is_duplicate
                        ])
                        self._next_id += 1
                    else:
                        # 计算宽高比和重复文件标识
                        width = file_info.get('width', 0)
                        height = file_info.get('height', 0)
                        aspect_ratio = width / height if height > 0 else 0

                        # 使用新的基于宽高比的重复检测逻辑
                        is_duplicate = self._check_duplicate_by_aspect_ratio(file_info['name_without_ext'], aspect_ratio)

                        # 使用序列自动分配ID
                        self.db.execute("""
                            INSERT INTO image_files (
                                filename, name_without_ext, extension, relative_path, full_path,
                                has_chinese, normalized_filename, normalized_name,
                                file_size, modified_time, created_time, indexed_time,
                                width, height, has_exif, orientation, aspect_ratio, is_duplicate
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, [
                            file_info['filename'],
                            file_info['name_without_ext'],
                            file_info['extension'],
                            file_info['relative_path'],
                            file_info['full_path'],
                            file_info['has_chinese'],
                            file_info['normalized_filename'],
                            file_info['normalized_name'],
                            file_info['file_size'],
                            file_info['modified_time'],
                            file_info['created_time'],
                            file_info['indexed_time'],
                            width,
                            height,
                            file_info.get('has_exif', False),
                            file_info.get('orientation', 1),
                            aspect_ratio,
                            is_duplicate
                        ])

                    # 如果当前文件是重复文件，更新相关文件的重复状态
                    if is_duplicate:
                        self._update_duplicate_status_by_aspect_ratio(file_info['name_without_ext'])

                # 在增量索引时不需要单独提交，由外部管理
                return True

            except Exception as e:
                # 在增量索引时不需要单独回滚，由外部管理
                raise e

        except Exception as e:
            log.error(f"索引图片失败 {rel_path}: {str(e)}")
            return False

    def scan_library(self, library_path, fast_mode=None):
        """扫描图库目录，建立图案索引

        使用多线程并行处理，显著提高索引速度

        Args:
            library_path: 图库目录路径
            fast_mode: 是否使用快速模式，跳过图片尺寸和EXIF数据的获取

        Returns:
            (成功标志, 消息, 图片数量)
        """
        # 如果未指定fast_mode，使用类的fast_mode属性
        if fast_mode is None:
            fast_mode = self.fast_mode

        try:
            # 重置停止标志
            self._stop_requested = False

            # 设置图库路径并初始化数据库
            self.set_library_path(library_path)
            if not self.db:
                return False, "初始化数据库失败", 0

            # 开始事务，先备份现有数据
            self.db.execute("BEGIN TRANSACTION")

            try:
                # 获取当前最大ID，用于保持ID连续性
                max_id_result = self.db.execute("SELECT COALESCE(MAX(id), 0) FROM image_files").fetchone()
                current_max_id = max_id_result[0] if max_id_result else 0
                self.status_signal.emit(f"当前数据库最大ID: {current_max_id}")

                # 创建备份表
                self.db.execute("CREATE TEMPORARY TABLE image_files_backup AS SELECT * FROM image_files")

                # 清空现有数据
                self.db.execute("DELETE FROM image_files")

                # 由于DuckDB不支持ALTER SEQUENCE，我们将在插入时手动管理ID
                # 存储当前最大ID，用于后续插入时计算新ID
                self._next_id = current_max_id + 1
                self.status_signal.emit(f"下一个ID将从 {self._next_id} 开始")

            except Exception as e:
                self.db.execute("ROLLBACK")
                return False, f"准备数据库事务失败: {str(e)}", 0

            # 收集所有图片文件
            self.status_signal.emit(f"正在扫描图库: {library_path}")

            image_files = self._collect_image_files(library_path)

            total_files = len(image_files)
            if total_files == 0:
                self.status_signal.emit("未找到支持的图片文件")
                return False, "未找到支持的图片文件", 0

            # 开始索引
            mode_str = "快速模式" if fast_mode else "完整模式"
            self.status_signal.emit(f"开始并行索引 {total_files} 个图片文件（{mode_str}）...")

            # 确定最佳线程数
            optimal_workers = self.parallel_manager.get_optimal_workers(ParallelManager.TASK_TYPE_IO)
            workers_count = min(optimal_workers, self.max_workers)

            # 计算每个批次的大小
            batch_size = max(100, min(1000, total_files // (workers_count * 4)))

            self.status_signal.emit(f"使用 {workers_count} 个工作线程，批次大小: {batch_size}")

            # 初始化计数器
            success_count = 0
            error_count = 0
            processed_count = 0

            # 创建线程池
            with concurrent.futures.ThreadPoolExecutor(max_workers=workers_count) as executor:
                # 分批处理文件
                batches = [image_files[i:i+batch_size] for i in range(0, len(image_files), batch_size)]
                batch_count = len(batches)

                self.status_signal.emit(f"将 {total_files} 个文件分为 {batch_count} 个批次进行处理")

                # 提交所有批次任务
                futures = []
                for i, batch in enumerate(batches):
                    future = executor.submit(self._process_file_batch, batch, library_path, i+1, batch_count, fast_mode)
                    futures.append(future)

                # 处理结果
                for future in concurrent.futures.as_completed(futures):
                    if self._stop_requested:
                        for f in futures:
                            f.cancel()
                        self.db.commit()
                        message = f"用户取消索引，已处理 {processed_count}/{total_files} 个文件"
                        self.status_signal.emit(message)
                        return False, "用户取消索引", success_count

                    try:
                        batch_success, batch_error = future.result()
                        success_count += batch_success
                        error_count += batch_error
                        processed_count = success_count + error_count

                        # 更新进度
                        progress = int(processed_count / total_files * 100)
                        self.progress_signal.emit(progress)

                        # 定期提交事务
                        if processed_count % self.COMMIT_INTERVAL == 0:
                            with self._lock:
                                if self._safe_commit():
                                    # 重新开始事务以继续处理
                                    try:
                                        self.db.execute("BEGIN TRANSACTION")
                                    except Exception as begin_error:
                                        self.error_signal.emit(f"重新开始事务失败: {str(begin_error)}")
                                else:
                                    self.error_signal.emit("定期提交事务失败")
                            self.status_signal.emit(f"已处理 {processed_count}/{total_files} 个文件...")

                    except Exception as e:
                        error_count += batch_size  # 估计值
                        self.error_signal.emit(f"批次处理失败: {str(e)}")

            # 最终提交事务
            try:
                with self._lock:
                    if success_count > 0:
                        # 如果有成功的记录，提交事务
                        if not self._safe_commit():
                            # 如果提交失败，记录错误但继续清理
                            self.error_signal.emit("最终提交事务失败，但将继续清理资源")
                        
                        # 删除备份表
                        self.db.execute("DROP TABLE IF EXISTS image_files_backup")
                        # 清理手动ID管理状态
                        if hasattr(self, '_next_id'):
                            delattr(self, '_next_id')
                        self._indexed = True
                        message = (f"索引完成: 成功 {success_count} 个，失败 {error_count} 个")
                        self.status_signal.emit(message)
                        return True, message, success_count
                    else:
                        # 如果没有成功的记录，回滚到原始状态
                        self._safe_rollback()
                        
                        # 恢复备份数据
                        try:
                            self.db.execute("INSERT INTO image_files SELECT * FROM image_files_backup")
                            self.db.execute("DROP TABLE IF EXISTS image_files_backup")
                            self.db.commit()
                        except:
                            pass  # 如果恢复失败，至少保持数据库一致性

                        message = "索引失败: 没有成功处理任何文件，已恢复原始数据"
                        self.status_signal.emit(message)
                        return False, message, 0

            except Exception as e:
                # 如果提交失败，尝试回滚
                self._safe_rollback()
                message = f"索引事务提交失败: {str(e)}"
                self.status_signal.emit(message)
                return False, message, 0

        except Exception as e:
            # 发生异常时，尝试回滚事务
            try:
                if self.db:
                    self._safe_rollback()
                    
                    # 尝试恢复备份数据
                    try:
                        self.db.execute("INSERT INTO image_files SELECT * FROM image_files_backup")
                        self.db.execute("DROP TABLE IF EXISTS image_files_backup")
                        self.db.commit()
                        self.status_signal.emit("发生错误，已恢复原始索引数据")
                    except Exception as restore_error:
                        self.status_signal.emit(f"发生错误，无法恢复原始索引数据: {str(restore_error)}")

                    # 清理手动ID管理状态
                    if hasattr(self, '_next_id'):
                        delattr(self, '_next_id')
            except Exception as cleanup_error:
                self.error_signal.emit(f"清理资源时出错: {str(cleanup_error)}")

            self.error_signal.emit(f"扫描图库失败: {str(e)}")
            return False, f"扫描图库失败: {str(e)}", 0

    def _collect_image_files(self, library_path):
        """收集图库中的所有图片文件

        Args:
            library_path: 图库路径

        Returns:
            List[str]: 相对路径列表
        """
        image_files = []

        for root, _, files in os.walk(library_path):
            if self._stop_requested:
                break

            for file in files:
                if file.lower().endswith(self._supported_extensions):
                    rel_path = os.path.relpath(os.path.join(root, file), library_path)
                    # 规范化路径，统一使用反斜杠
                    rel_path = self.normalize_path(rel_path)
                    image_files.append(rel_path)

        return image_files

    def _process_file_batch(self, file_batch, library_path, batch_index, total_batches, fast_mode=None):
        """处理一批文件

        Args:
            file_batch: 文件相对路径列表
            library_path: 图库路径
            batch_index: 批次索引
            total_batches: 总批次数
            fast_mode: 是否使用快速模式，跳过图片尺寸和EXIF数据的获取

        Returns:
            Tuple[int, int]: (成功数, 失败数)
        """
        # 如果未指定fast_mode，使用类的fast_mode属性
        if fast_mode is None:
            fast_mode = self.fast_mode

        success_count = 0
        error_count = 0
        batch_data = []

        try:
            for rel_path in file_batch:
                if self._stop_requested:
                    break

                try:
                    # 获取文件信息
                    full_path = os.path.join(library_path, rel_path)
                    file_info = self._get_file_info(full_path, fast_mode)

                    if file_info:
                        # 添加路径信息，确保路径规范化
                        file_info['relative_path'] = self.normalize_path(rel_path)
                        file_info['full_path'] = self.normalize_path(full_path)
                        batch_data.append(file_info)
                        success_count += 1
                    else:
                        error_count += 1

                except Exception as e:
                    error_count += 1
                    log.error(f"处理文件失败 {rel_path}: {str(e)}")

            # 批量插入数据
            if batch_data:
                with self._lock:
                    self._batch_insert(batch_data)

            mode_str = "快速模式" if fast_mode else "完整模式"
            log.info(f"批次 {batch_index}/{total_batches} 处理完成({mode_str}): 成功 {success_count}, 失败 {error_count}")
            return success_count, error_count

        except Exception as e:
            log.error(f"批次 {batch_index}/{total_batches} 处理失败: {str(e)}")
            return success_count, error_count + (len(file_batch) - success_count)

    def validate_library(self):
        """验证图库是否已经建立索引"""
        if not self.db or not self._indexed:
            return False

        try:
            # 检查是否有图片记录
            result = self.db.execute("SELECT COUNT(*) FROM image_files").fetchone()
            return result[0] > 0
        except Exception as e:
            log.error(f"验证图库索引失败: {str(e)}")
            return False

    def is_indexed(self, library_path):
        """检查指定图库路径是否已经建立索引

        Args:
            library_path: 图库路径

        Returns:
            bool: 是否已建立索引
        """
        try:
            # 检查索引数据库是否存在
            index_dir = os.path.join(library_path, '.index')
            db_path = os.path.join(index_dir, 'image_index.db')

            if not os.path.exists(db_path):
                return False

            # 如果当前连接的是别的库，先关闭连接
            if self.library_path != library_path and self.db:
                try:
                    self.db.close()
                    self.db = None
                except:
                    pass

            # 如果没有连接，尝试连接数据库
            if not self.db:
                self.set_library_path(library_path)

            # 验证索引
            return self.validate_library()

        except Exception as e:
            log.error(f"检查图库索引状态失败: {str(e)}")
            return False

    def find_image(self, pattern_name: str, exact_match: bool = True, table_aspect_ratio: float = None) -> Optional[str]:
        """查找指定图案号的图片，支持智能选择同名图片

        Args:
            pattern_name: 图案号或文件名
            exact_match: 是否进行精确匹配
            table_aspect_ratio: 表格中的宽高比，用于智能选择同名图片

        Returns:
            图片完整路径，如果找不到则返回None
        """
        if not self.db or not pattern_name:
            return None

        try:
            # 检查数据库连接状态
            if not hasattr(self.db, 'execute'):
                log.error("数据库连接已失效")
                return None

            # 规范化搜索词
            search_term = self.normalize_filename(pattern_name)
            if isinstance(search_term, tuple):
                search_term, _ = search_term  # 忽略第二个返回值

            if exact_match:
                # 精确匹配，查找所有匹配的图片，包含重复文件标识和变体图片
                results = self.db.execute("""
                    SELECT full_path, aspect_ratio, name_without_ext, is_duplicate FROM image_files
                    WHERE name_without_ext = ?
                       OR name_without_ext LIKE ? || '_%'
                       OR normalized_name = ?
                       OR filename = ?
                       OR normalized_filename = ?
                    ORDER BY name_without_ext, full_path
                """, [pattern_name, pattern_name, search_term, pattern_name, search_term]).fetchall()
            else:
                # 模糊匹配，包含重复文件标识
                pattern_name_like = f"%{pattern_name}%"
                search_term_like = f"%{search_term}%"
                results = self.db.execute("""
                    SELECT full_path, aspect_ratio, name_without_ext, is_duplicate FROM image_files
                    WHERE name_without_ext LIKE ?
                       OR normalized_name LIKE ?
                       OR filename LIKE ?
                       OR normalized_filename LIKE ?
                    ORDER BY name_without_ext, full_path
                """, [pattern_name_like, search_term_like, pattern_name_like, search_term_like]).fetchall()

            if not results:
                log.warning(f"未找到图片: {pattern_name}")
                return None

            # 智能选择逻辑
            selected_path = self._select_best_image(results, table_aspect_ratio, pattern_name)

            if selected_path and os.path.exists(selected_path):
                log.info(f"找到图片: {selected_path}")
                return selected_path

            log.warning(f"未找到有效图片: {pattern_name}")
            return None

        except Exception as e:
            # 尝试重新连接数据库
            if "database connection" in str(e).lower() or "closed" in str(e).lower():
                log.warning(f"数据库连接问题，尝试重新连接: {str(e)}")
                try:
                    self._reconnect_database()
                    # 重试查找
                    return self.find_image(pattern_name, exact_match, table_aspect_ratio)
                except Exception as retry_e:
                    log.error(f"重新连接数据库失败: {str(retry_e)}")

            log.error(f"查找图片失败 {pattern_name}: {str(e)}")
            return None

    def _select_best_image(self, results: list, table_aspect_ratio: float, pattern_name: str) -> Optional[str]:
        """智能选择最佳图片

        Args:
            results: 数据库查询结果列表 [(full_path, aspect_ratio, name_without_ext, is_duplicate), ...]
            table_aspect_ratio: 表格中的宽高比
            pattern_name: 图案名称

        Returns:
            最佳图片路径
        """
        if not results:
            return None

        # 如果只有一个结果，直接返回
        if len(results) == 1:
            return results[0][0]

        # 检查是否有重复文件标识
        has_duplicates = any(is_duplicate for _, _, _, is_duplicate in results)

        if has_duplicates and table_aspect_ratio is not None:
            log.info(f"检测到重复文件: 图案 {pattern_name}，表格宽高比: {table_aspect_ratio:.3f}")

            # 直接对所有匹配的图片应用智能宽高比相似度选择
            best_path = self._select_by_aspect_ratio_similarity_from_results(results, table_aspect_ratio, pattern_name)
            if best_path:
                return best_path

        # 按图案名称分组，查找同名图片（保持向后兼容性）
        image_groups = {}
        for full_path, aspect_ratio, name_without_ext, is_duplicate in results:
            # 处理同名图片的分组逻辑：去掉数字后缀进行分组
            base_name = name_without_ext

            # 更智能的后缀处理：去掉 _数字 后缀
            if '_' in base_name:
                parts = base_name.rsplit('_', 1)
                if len(parts) == 2 and parts[1].isdigit():
                    base_name = parts[0]

            if base_name not in image_groups:
                image_groups[base_name] = []
            image_groups[base_name].append((full_path, aspect_ratio, is_duplicate))

        # 对每个分组应用智能选择逻辑
        best_path = None
        best_similarity = float('inf')

        for base_name, paths in image_groups.items():
            if len(paths) == 1:
                # 只有一个图片，直接选择
                if not best_path:
                    best_path = paths[0][0]
            else:
                # 有多个同名图片，应用智能选择逻辑
                selected = self._apply_duplicate_selection_logic(paths, table_aspect_ratio, base_name)
                if selected:
                    # 如果有表格宽高比，选择最接近的
                    if table_aspect_ratio is not None:
                        for full_path, aspect_ratio, is_duplicate in paths:
                            if full_path == selected and aspect_ratio is not None and aspect_ratio > 0:
                                similarity = abs(table_aspect_ratio - aspect_ratio)
                                if similarity < best_similarity:
                                    best_similarity = similarity
                                    best_path = selected
                                break
                    else:
                        best_path = selected
                    break

        # 如果没有找到最佳匹配，返回第一个有效路径
        if not best_path:
            best_path = results[0][0]

        return best_path

    def _apply_duplicate_selection_logic(self, paths: list, table_aspect_ratio: float, name: str) -> Optional[str]:
        """应用重复图片选择逻辑

        Args:
            paths: 同名图片路径、宽高比和重复标识列表 [(full_path, aspect_ratio, is_duplicate), ...]
            table_aspect_ratio: 表格中的宽高比
            name: 图案名称

        Returns:
            选择的图片路径
        """
        if not paths:
            return None

        # 检查是否有重复文件标识
        has_duplicates = any(is_duplicate for _, _, is_duplicate in paths)

        if has_duplicates and table_aspect_ratio is not None:
            log.info(f"检测到重复文件: 图案 {name}，表格宽高比: {table_aspect_ratio:.3f}")

            # 当存在重复文件时，根据宽高比相似度智能选择最接近的图片
            best_path = self._select_by_aspect_ratio_similarity(paths, table_aspect_ratio, name)
            if best_path:
                return best_path

        # 分离原图和_2图（保持向后兼容性）
        original_images = []
        duplicate_images = []

        for full_path, aspect_ratio, is_duplicate in paths:
            filename = os.path.basename(full_path)
            name_without_ext = os.path.splitext(filename)[0]

            if name_without_ext.endswith('_2'):
                duplicate_images.append((full_path, aspect_ratio, is_duplicate))
            else:
                original_images.append((full_path, aspect_ratio, is_duplicate))

        # 传统的智能选择逻辑（向后兼容）
        if has_duplicates:
            # 如果存在_2图片 AND 表格宽高比 < 2，则选择_2图片
            if duplicate_images and table_aspect_ratio is not None and table_aspect_ratio < 2:
                selected_path = duplicate_images[0][0]
                log.info(f"传统选择: 图案 {name} 表格宽高比 {table_aspect_ratio:.2f} < 2，选择_2版本图片: {os.path.basename(selected_path)}")
                return selected_path
            elif original_images:
                selected_path = original_images[0][0]
                log.info(f"传统选择: 图案 {name} 表格宽高比 {table_aspect_ratio:.2f} >= 2，选择原版图片: {os.path.basename(selected_path)}")
                return selected_path
            elif duplicate_images:
                # 如果没有原图，但有_2图，则选择_2图
                selected_path = duplicate_images[0][0]
                log.info(f"传统选择: 图案 {name} 只有_2版本，选择_2版本图片: {os.path.basename(selected_path)}")
                return selected_path
        else:
            # 没有重复文件，按原有逻辑选择
            if original_images:
                selected_path = original_images[0][0]
                log.info(f"选择: 图案 {name} 无重复文件，选择原版图片: {os.path.basename(selected_path)}")
                return selected_path
            elif duplicate_images:
                selected_path = duplicate_images[0][0]
                log.info(f"选择: 图案 {name} 无重复文件，选择_2版本图片: {os.path.basename(selected_path)}")
                return selected_path

        return None

    def _select_by_aspect_ratio_similarity(self, paths: list, table_aspect_ratio: float, name: str) -> Optional[str]:
        """根据宽高比相似度选择最佳图片

        Args:
            paths: 图片路径、宽高比和重复标识列表 [(full_path, aspect_ratio, is_duplicate), ...]
            table_aspect_ratio: 表格中的宽高比
            name: 图案名称

        Returns:
            最佳匹配的图片路径
        """
        if not paths or table_aspect_ratio is None or table_aspect_ratio <= 0:
            return None

        best_path = None
        best_similarity = float('inf')  # 最小差值表示最佳匹配

        for full_path, aspect_ratio, is_duplicate in paths:
            # 跳过无效的宽高比数据
            if aspect_ratio is None or aspect_ratio <= 0:
                continue

            # 计算宽高比差值（绝对值）
            similarity = abs(table_aspect_ratio - aspect_ratio)

            filename = os.path.basename(full_path)
            log.debug(f"图片 {filename}: 数据库宽高比={aspect_ratio:.3f}, 表格宽高比={table_aspect_ratio:.3f}, 差值={similarity:.3f}")

            # 选择差值最小的图片
            if similarity < best_similarity:
                best_similarity = similarity
                best_path = full_path

        if best_path:
            filename = os.path.basename(best_path)
            log.info(f"智能选择: 图案 {name} 根据宽高比相似度选择图片: {filename} (差值: {best_similarity:.3f})")
            return best_path

        return None

    def _select_by_aspect_ratio_similarity_from_results(self, results: list, table_aspect_ratio: float, name: str) -> Optional[str]:
        """从查询结果中根据宽高比相似度选择最佳图片

        Args:
            results: 数据库查询结果列表 [(full_path, aspect_ratio, name_without_ext, is_duplicate), ...]
            table_aspect_ratio: 表格中的宽高比
            name: 图案名称

        Returns:
            最佳匹配的图片路径
        """
        if not results or table_aspect_ratio is None or table_aspect_ratio <= 0:
            return None

        best_path = None
        best_similarity = float('inf')  # 最小差值表示最佳匹配

        for full_path, aspect_ratio, name_without_ext, is_duplicate in results:
            # 跳过无效的宽高比数据
            if aspect_ratio is None or aspect_ratio <= 0:
                continue

            # 计算宽高比差值（绝对值）
            similarity = abs(table_aspect_ratio - aspect_ratio)

            filename = os.path.basename(full_path)
            log.debug(f"图片 {filename}: 数据库宽高比={aspect_ratio:.3f}, 表格宽高比={table_aspect_ratio:.3f}, 差值={similarity:.3f}")

            # 选择差值最小的图片
            if similarity < best_similarity:
                best_similarity = similarity
                best_path = full_path

        if best_path:
            filename = os.path.basename(best_path)
            log.info(f"智能选择: 图案 {name} 根据宽高比相似度选择图片: {filename} (差值: {best_similarity:.3f})")
            return best_path

        return None

    def _reconnect_database(self):
        """重新连接数据库"""
        try:
            if self.db:
                self.db.close()
            self.db = duckdb.connect(self.db_path)
            log.info("数据库重新连接成功")
        except Exception as e:
            log.error(f"重新连接数据库失败: {str(e)}")
            raise

    def _batch_insert(self, batch):
        """批量插入记录

        Args:
            batch: 文件信息字典列表
        """
        if not batch:
            return

        try:
            # 检查是否需要手动管理ID（在scan_library过程中）
            use_manual_id = hasattr(self, '_next_id') and self._next_id is not None

            # 准备批量插入的数据
            values = []
            duplicate_files = []  # 记录重复文件，用于后续更新状态

            for file_info in batch:
                # 计算宽高比和重复文件标识
                width = file_info.get('width', 0)
                height = file_info.get('height', 0)
                aspect_ratio = width / height if height > 0 else 0

                # 检查是否为重复文件（包括原图和_2图）
                is_duplicate = (file_info['name_without_ext'].endswith('_2') or
                              self._check_duplicate_files(file_info['name_without_ext']))

                # 记录重复文件，用于后续更新
                if is_duplicate:
                    duplicate_files.append(file_info['name_without_ext'])

                if use_manual_id:
                    # 手动分配ID
                    values.append((
                        self._next_id,
                        file_info['filename'],
                        file_info['name_without_ext'],
                        file_info['extension'],
                        file_info['relative_path'],
                        file_info['full_path'],
                        file_info['has_chinese'],
                        file_info['normalized_filename'],
                        file_info['normalized_name'],
                        file_info['file_size'],
                        file_info['modified_time'],
                        file_info['created_time'],
                        file_info['indexed_time'],
                        width,
                        height,
                        file_info.get('has_exif', False),
                        file_info.get('orientation', 1),
                        aspect_ratio,
                        is_duplicate
                    ))
                    self._next_id += 1
                else:
                    # 使用序列自动分配ID
                    values.append((
                        file_info['filename'],
                        file_info['name_without_ext'],
                        file_info['extension'],
                        file_info['relative_path'],
                        file_info['full_path'],
                        file_info['has_chinese'],
                        file_info['normalized_filename'],
                        file_info['normalized_name'],
                        file_info['file_size'],
                        file_info['modified_time'],
                        file_info['created_time'],
                        file_info['indexed_time'],
                        width,
                        height,
                        file_info.get('has_exif', False),
                        file_info.get('orientation', 1),
                        aspect_ratio,
                        is_duplicate
                    ))

            # 执行批量插入
            if use_manual_id:
                # 手动指定ID的插入
                self.db.executemany("""
                    INSERT INTO image_files (
                        id, filename, name_without_ext, extension, relative_path, full_path,
                        has_chinese, normalized_filename, normalized_name,
                        file_size, modified_time, created_time, indexed_time,
                        width, height, has_exif, orientation, aspect_ratio, is_duplicate
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ON CONFLICT (relative_path) DO UPDATE SET
                        filename = excluded.filename,
                        name_without_ext = excluded.name_without_ext,
                        extension = excluded.extension,
                        full_path = excluded.full_path,
                        has_chinese = excluded.has_chinese,
                        normalized_filename = excluded.normalized_filename,
                        normalized_name = excluded.normalized_name,
                        file_size = excluded.file_size,
                        modified_time = excluded.modified_time,
                        created_time = excluded.created_time,
                        indexed_time = excluded.indexed_time,
                        width = excluded.width,
                        height = excluded.height,
                        has_exif = excluded.has_exif,
                        orientation = excluded.orientation,
                        aspect_ratio = excluded.aspect_ratio,
                        is_duplicate = excluded.is_duplicate
                """, values)
            else:
                # 使用序列自动分配ID的插入
                self.db.executemany("""
                    INSERT INTO image_files (
                        filename, name_without_ext, extension, relative_path, full_path,
                        has_chinese, normalized_filename, normalized_name,
                        file_size, modified_time, created_time, indexed_time,
                        width, height, has_exif, orientation, aspect_ratio, is_duplicate
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ON CONFLICT (relative_path) DO UPDATE SET
                        filename = excluded.filename,
                        name_without_ext = excluded.name_without_ext,
                        extension = excluded.extension,
                        full_path = excluded.full_path,
                        has_chinese = excluded.has_chinese,
                        normalized_filename = excluded.normalized_filename,
                        normalized_name = excluded.normalized_name,
                        file_size = excluded.file_size,
                        modified_time = excluded.modified_time,
                        created_time = excluded.created_time,
                        indexed_time = excluded.indexed_time,
                        width = excluded.width,
                        height = excluded.height,
                        has_exif = excluded.has_exif,
                        orientation = excluded.orientation,
                        aspect_ratio = excluded.aspect_ratio,
                        is_duplicate = excluded.is_duplicate
                """, values)

            # 更新重复文件状态
            for name_without_ext in duplicate_files:
                self._update_duplicate_status(name_without_ext)

        except Exception as e:
            self.error_signal.emit(f"批量插入失败: {str(e)}")
            raise e

    def remove_images_by_paths(self, relative_paths: List[str]) -> Tuple[bool, str, int]:
        """根据相对路径列表删除图片记录（增量删除）

        Args:
            relative_paths: 要删除的图片相对路径列表

        Returns:
            Tuple[bool, str, int]: (是否成功, 消息, 删除的记录数)
        """
        if not self.db or not relative_paths:
            return False, "数据库未初始化或路径列表为空", 0

        try:
            # 规范化路径
            normalized_paths = [self.normalize_path(path) for path in relative_paths]

            deleted_count = 0
            not_found_count = 0
            error_count = 0

            total_paths = len(normalized_paths)
            self.status_signal.emit(f"开始删除 {total_paths} 条索引记录...")

            # 使用更安全的事务处理
            max_retries = 3
            for retry in range(max_retries):
                try:
                    # 开始事务
                    self.db.execute("BEGIN TRANSACTION")

                    # 重置计数器（重试时）
                    if retry > 0:
                        deleted_count = 0
                        not_found_count = 0
                        error_count = 0

                    for i, rel_path in enumerate(normalized_paths):
                        try:
                            # 进度反馈
                            if i % 10 == 0 or i == total_paths - 1:
                                progress = int((i + 1) / total_paths * 100)
                                self.status_signal.emit(f"删除进度: {progress}% ({i + 1}/{total_paths})")

                            # 检查记录是否存在
                            result = self.db.execute(
                                "SELECT id FROM image_files WHERE relative_path = ?",
                                [rel_path]
                            ).fetchone()

                            if result:
                                # 删除记录
                                self.db.execute(
                                    "DELETE FROM image_files WHERE relative_path = ?",
                                    [rel_path]
                                )
                                deleted_count += 1
                                log.debug(f"已删除索引记录: {rel_path}")
                            else:
                                not_found_count += 1
                                log.debug(f"索引中未找到记录: {rel_path}")

                        except Exception as e:
                            error_count += 1
                            log.error(f"删除记录失败 {rel_path}: {str(e)}")
                            continue

                    # 提交事务
                    self.db.commit()
                    break  # 成功提交，跳出重试循环

                except Exception as e:
                    # 回滚事务
                    try:
                        self.db.rollback()
                    except:
                        pass

                    error_msg = str(e)
                    if "write-write conflict" in error_msg or "TransactionContext Error" in error_msg:
                        if retry < max_retries - 1:
                            import time
                            wait_time = (retry + 1) * 0.5
                            log.warning(f"数据库冲突，等待 {wait_time} 秒后重试 (第 {retry + 1}/{max_retries} 次)")
                            self.status_signal.emit(f"数据库冲突，等待 {wait_time} 秒后重试...")
                            time.sleep(wait_time)
                            continue
                        else:
                            raise Exception(f"索引事务提交失败: {error_msg}")
                    else:
                        raise Exception(f"索引删除失败: {error_msg}")

            # 生成详细的结果消息
            result_parts = []
            if deleted_count > 0:
                result_parts.append(f"成功删除 {deleted_count} 条")
            if not_found_count > 0:
                result_parts.append(f"未找到 {not_found_count} 条")
            if error_count > 0:
                result_parts.append(f"失败 {error_count} 条")

            message = "索引删除完成: " + ", ".join(result_parts)
            self.status_signal.emit(message)

            # 如果有删除成功的记录，返回成功
            return deleted_count > 0, message, deleted_count

        except Exception as e:
            # 确保事务回滚
            try:
                self.db.rollback()
            except:
                pass

            error_msg = str(e)
            self.error_signal.emit(error_msg)
            log.error(f"删除索引记录失败: {error_msg}")
            return False, error_msg, 0

    def add_images_by_paths(self, relative_paths: List[str], fast_mode: bool = True) -> Tuple[bool, str, int]:
        """根据相对路径列表增量添加图片记录

        Args:
            relative_paths: 要添加的图片相对路径列表
            fast_mode: 是否使用快速模式

        Returns:
            Tuple[bool, str, int]: (是否成功, 消息, 添加的记录数)
        """
        if not self.db or not relative_paths:
            return False, "数据库未初始化或路径列表为空", 0

        try:
            # 开始事务
            self.db.execute("BEGIN TRANSACTION")

            # 获取当前最大ID，用于新记录的ID分配
            max_id_result = self.db.execute("SELECT COALESCE(MAX(id), 0) FROM image_files").fetchone()
            next_id = (max_id_result[0] if max_id_result else 0) + 1

            added_count = 0
            skipped_count = 0
            error_count = 0

            total_paths = len(relative_paths)
            self.status_signal.emit(f"开始添加 {total_paths} 条索引记录...")

            for i, rel_path in enumerate(relative_paths):
                try:
                    # 进度反馈
                    if i % 10 == 0 or i == total_paths - 1:
                        progress = int((i + 1) / total_paths * 100)
                        self.status_signal.emit(f"添加进度: {progress}% ({i + 1}/{total_paths})")

                    # 规范化路径
                    normalized_path = self.normalize_path(rel_path)
                    full_path = os.path.join(self.library_path, normalized_path)

                    # 检查文件是否存在
                    if not os.path.exists(full_path):
                        skipped_count += 1
                        log.debug(f"文件不存在，跳过: {rel_path}")
                        continue

                    # 检查是否已存在相同路径的记录
                    result = self.db.execute(
                        "SELECT id FROM image_files WHERE relative_path = ?",
                        [normalized_path]
                    ).fetchone()

                    if result:
                        skipped_count += 1
                        log.debug(f"记录已存在，跳过: {normalized_path}")
                        continue

                    # 获取文件信息
                    file_info = self._get_file_info(full_path, fast_mode)
                    if not file_info:
                        error_count += 1
                        log.warning(f"获取文件信息失败，跳过: {normalized_path}")
                        continue

                    # 添加路径信息
                    file_info['relative_path'] = normalized_path
                    file_info['full_path'] = self.normalize_path(full_path)

                    # 处理图片尺寸
                    width = file_info.get('width', 0)
                    height = file_info.get('height', 0)
                    aspect_ratio = round(width / height, 3) if height > 0 else 0.0

                    # 插入新记录
                    self.db.execute("""
                        INSERT INTO image_files (
                            id, filename, name_without_ext, extension, relative_path, full_path,
                            has_chinese, normalized_filename, normalized_name,
                            file_size, modified_time, created_time, indexed_time,
                            width, height, has_exif, orientation, aspect_ratio, is_duplicate
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, [
                        next_id,
                        file_info['filename'],
                        file_info['name_without_ext'],
                        file_info['extension'],
                        file_info['relative_path'],
                        file_info['full_path'],
                        file_info['has_chinese'],
                        file_info['normalized_filename'],
                        file_info['normalized_name'],
                        file_info['file_size'],
                        file_info['modified_time'],
                        file_info['created_time'],
                        file_info['indexed_time'],
                        width,
                        height,
                        file_info.get('has_exif', False),
                        file_info.get('orientation', 1),
                        aspect_ratio,
                        file_info.get('is_duplicate', False)
                    ])

                    next_id += 1
                    added_count += 1
                    log.debug(f"已添加索引记录: {normalized_path}")

                except Exception as e:
                    error_count += 1
                    log.error(f"添加索引记录失败 {rel_path}: {str(e)}")
                    continue

            # 提交事务
            self.db.commit()

            # 生成详细的结果消息
            result_parts = []
            if added_count > 0:
                result_parts.append(f"成功添加 {added_count} 条")
            if skipped_count > 0:
                result_parts.append(f"跳过 {skipped_count} 条")
            if error_count > 0:
                result_parts.append(f"失败 {error_count} 条")

            message = "索引添加完成: " + ", ".join(result_parts)
            self.status_signal.emit(message)

            # 如果有添加成功的记录，返回成功
            return added_count > 0, message, added_count

        except Exception as e:
            # 回滚事务
            try:
                self.db.rollback()
            except:
                pass
            error_msg = f"添加索引记录失败: {str(e)}"
            self.error_signal.emit(error_msg)
            return False, error_msg, 0

    def get_image_count(self) -> int:
        """获取当前索引中的图片数量

        Returns:
            int: 图片数量，失败时返回0
        """
        if not self.db:
            return 0

        try:
            result = self.db.execute("SELECT COUNT(*) FROM image_files").fetchone()
            return result[0] if result else 0
        except Exception as e:
            log.error(f"获取图片数量失败: {str(e)}")
            return 0

    def close(self):
        """显式关闭数据库连接"""
        if hasattr(self, 'db') and self.db:
            try:
                self.db.close()
                self.db = None
                self._indexed = False
                self.status_signal.emit("数据库连接已关闭")
            except Exception as e:
                log.error(f"关闭数据库连接失败: {str(e)}")

    def __del__(self):
        """析构函数，确保数据库连接被正确关闭"""
        self.close()