# -*- coding: utf-8 -*-
"""
日志信息映射模块

提供敏感技术信息的用户友好替换功能：
1. 隐藏技术实现细节
2. 提供用户友好的描述
3. 保护核心技术信息
4. 统一日志信息格式
"""

import re
from typing import Dict, List, Tuple

# 技术术语映射表 - 只隐藏核心技术术语，保留用户熟知的词语
TECH_TERM_MAPPING = {
    # 核心算法相关 - 需要隐藏
    'rectpack': '图像排列算法',
    'RectPack': '图像排列算法',
    'RECTPACK': '图像排列算法',
    'rect pack': '图像排列算法',
    'Rect Pack': '图像排列算法',
    'RectPack库': '图像排列算法库',
    'RectPack算法': '图像排列算法',
    'RectPack统一布局算法': '图像排列算法',
    'RectPack统一装箱算法': '图像排列算法',
    'Bottom-Left Fill': '智能排列算法',
    'BNF': '智能排列算法',
    'BSSF': '智能排列算法',
    'Best Short Side Fit': '智能排列算法',
    'SORT_AREA': '按面积排序',

    # 数据库相关 - 需要隐藏
    'supabase': '云端数据库',
    'Supabase': '云端数据库',
    'SUPABASE': '云端数据库',
    'duckdb': '本地数据库',
    'DuckDB': '本地数据库',
    'DUCKDB': '本地数据库',

    # 技术接口相关 - 需要隐藏
    'photoshop-python-api': '图像编辑接口',
    'Photoshop API': '图像编辑接口',
    'PhotoshopHelper': '图像编辑助手',

    # 其他核心技术术语 - 需要隐藏
    'packer': '排列器',
    'Packer': '排列器',
    # 注意：bin/Bin 不再映射，避免影响参数名称如 rectpack_bin_selection_strategy
    'API': '接口',
    'api': '接口',
    'JavaScript': '脚本',
    'javascript': '脚本',

    # 注意：Photoshop、Excel等用户熟知的词语不再映射，保持原样
}

# 敏感信息模式匹配 - 只隐藏核心技术实现细节
SENSITIVE_PATTERNS = [
    # API密钥和URL - 需要隐藏
    (r'https://[a-zA-Z0-9.-]+\.baseapi\.memfiredb\.com', '云端服务地址'),
    (r'eyJ[a-zA-Z0-9._-]+', 'API密钥'),

    # 核心算法技术实现细节 - 需要隐藏
    (r'rectpack库', '图像排列算法库'),
    (r'RectPack库', '图像排列算法库'),
    (r'newPacker', '初始化排列器'),
    (r'add_bin', '添加容器'),
    (r'photoshop-python-api', '图像编辑接口'),

    # 数据库连接信息 - 需要隐藏
    (r'create_client', '创建连接'),
    (r'sign_in_with_password', '用户登录'),
    (r'refresh_session', '刷新会话'),
    (r'DuckDB', '本地数据库'),
    (r'duckdb', '本地数据库'),
    (r'supabase', '云端数据库'),
    (r'Supabase', '云端数据库'),

    # 算法参数细节 - 需要隐藏
    (r'利用率阈值: \d+%', '利用率优化'),
    (r'内存限制: \d+MB', '内存优化'),
    (r'最大重试次数: \d+', '重试机制'),
    (r'当前PPI: \d+', 'PPI设置'),

    # 技术模式描述 - 需要隐藏
    (r'cm直接转换为px', '单位转换'),
    (r'真实cm转px转换', '单位转换'),
    (r'高精度浮点运算', '精确计算'),

    # 注意：不再隐藏PhotoshopHelper等，因为Photoshop是用户熟知的词语
]

# 无用日志过滤模式
USELESS_LOG_PATTERNS = [
    # 重复的成功信息
    r'^✅.*成功.*✅.*成功',
    # 空白或只有符号的日志
    r'^[\s\-=_*]+$',
    # 过于详细的调试信息
    r'DEBUG.*trace.*stack',
    # 重复的警告信息
    r'^⚠️.*⚠️.*同样.*问题',
    # 重复的配置信息
    r'^.*已启用.*已启用.*',
    r'^.*已初始化.*已初始化.*',
    # 过于技术性的详细信息
    r'.*PPI.*精度.*浮点.*',
    r'.*缓存机制.*已启用.*',
    r'.*最大重试次数.*降级策略.*',
    # 空的或无意义的状态更新
    r'^状态:?\s*$',
    r'^进度:?\s*$',
    r'^完成:?\s*$',
    # 重复的模式信息
    r'^.*模式.*模式.*',
    # 过于详细的参数信息
    r'.*转换精度.*统一单位.*',
]

# 日志级别映射
LOG_LEVEL_MAPPING = {
    0: '静默',
    1: '基础',
    2: '详细',
    3: '调试'
}

class LogMessageMapper:
    """日志信息映射器"""

    def __init__(self):
        """初始化映射器"""
        self.tech_mapping = TECH_TERM_MAPPING.copy()
        self.sensitive_patterns = SENSITIVE_PATTERNS.copy()
        self.useless_patterns = [re.compile(pattern) for pattern in USELESS_LOG_PATTERNS]

    def map_message(self, message: str) -> str:
        """映射日志消息，隐藏敏感信息

        Args:
            message: 原始日志消息

        Returns:
            映射后的日志消息
        """
        if not message or not isinstance(message, str):
            return message

        # 过滤无用日志
        if self.is_useless_log(message):
            return None

        mapped_message = message

        # 替换技术术语
        for tech_term, friendly_term in self.tech_mapping.items():
            mapped_message = mapped_message.replace(tech_term, friendly_term)

        # 替换敏感模式
        for pattern, replacement in self.sensitive_patterns:
            mapped_message = re.sub(pattern, replacement, mapped_message)

        # 清理重复的表情符号和标点
        mapped_message = self._clean_duplicates(mapped_message)

        return mapped_message

    def is_useless_log(self, message: str) -> bool:
        """检查是否为无用日志

        Args:
            message: 日志消息

        Returns:
            是否为无用日志
        """
        if not message or not isinstance(message, str):
            return True

        # 检查是否匹配无用模式
        for pattern in self.useless_patterns:
            if pattern.match(message.strip()):
                return True

        # 检查是否为空白或过短
        if len(message.strip()) < 3:
            return True

        return False

    def _clean_duplicates(self, message: str) -> str:
        """清理重复的符号和表情

        Args:
            message: 消息

        Returns:
            清理后的消息
        """
        # 清理重复的表情符号
        message = re.sub(r'([✅❌⚠️🎉🖼️])\1+', r'\1', message)

        # 清理重复的标点符号
        message = re.sub(r'([!.?])\1{2,}', r'\1', message)

        # 清理多余的空格
        message = re.sub(r'\s+', ' ', message).strip()

        return message

    def add_custom_mapping(self, tech_term: str, friendly_term: str):
        """添加自定义映射

        Args:
            tech_term: 技术术语
            friendly_term: 友好术语
        """
        self.tech_mapping[tech_term] = friendly_term

    def add_sensitive_pattern(self, pattern: str, replacement: str):
        """添加敏感信息模式

        Args:
            pattern: 正则表达式模式
            replacement: 替换文本
        """
        self.sensitive_patterns.append((pattern, replacement))

# 全局映射器实例
_global_mapper = LogMessageMapper()

def map_log_message(message: str) -> str:
    """映射日志消息的便捷函数

    Args:
        message: 原始日志消息

    Returns:
        映射后的日志消息，如果是无用日志则返回None
    """
    return _global_mapper.map_message(message)

def is_useless_log(message: str) -> bool:
    """检查是否为无用日志的便捷函数

    Args:
        message: 日志消息

    Returns:
        是否为无用日志
    """
    return _global_mapper.is_useless_log(message)

def get_log_level_name(level: int) -> str:
    """获取日志级别名称

    Args:
        level: 日志级别数字

    Returns:
        日志级别名称
    """
    return LOG_LEVEL_MAPPING.get(level, '未知')