#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高精度厘米-像素转换器

解决传统转换中的精度损失问题，确保转换的可逆性。
"""

import logging
from decimal import Decimal, ROUND_HALF_UP
from typing import Tuple, Optional

log = logging.getLogger(__name__)

class PrecisionConverter:
    """
    高精度厘米-像素转换器
    
    解决传统转换中的精度损失问题，确保转换的可逆性。
    核心原理：使用高精度计算和智能舍入策略，确保反向转换能回到原始值。
    """
    
    # 默认容差（厘米）- 根据尺寸动态调整
    DEFAULT_TOLERANCE_CM = 0.02  # 0.02cm = 0.2mm
    MIN_TOLERANCE_CM = 0.005     # 最小容差
    MAX_TOLERANCE_CM = 0.05      # 最大容差
    
    @staticmethod
    def _calculate_dynamic_tolerance(cm_value: float) -> float:
        """
        根据尺寸大小计算动态容差
        
        Args:
            cm_value: 厘米值
            
        Returns:
            float: 动态容差值
        """
        # 基础容差：尺寸越大，允许的绝对误差稍大
        # 但相对误差保持较小
        base_tolerance = PrecisionConverter.DEFAULT_TOLERANCE_CM
        
        # 对于大尺寸，适当放宽容差
        if cm_value > 100:
            tolerance = min(base_tolerance * 1.5, PrecisionConverter.MAX_TOLERANCE_CM)
        elif cm_value > 50:
            tolerance = base_tolerance * 1.2
        else:
            tolerance = max(base_tolerance, PrecisionConverter.MIN_TOLERANCE_CM)
            
        return tolerance
    
    @staticmethod
    def cm_to_px_precise(cm_value: float, dpi: int = 72, tolerance_cm: float = None, return_float: bool = False) -> float:
        """
        高精度厘米转像素转换

        Args:
            cm_value: 厘米值
            dpi: DPI值
            tolerance_cm: 容差（厘米），默认0.005cm
            return_float: 是否返回浮点数像素值，默认False返回整数

        Returns:
            float: 像素值（如果return_float=True则返回精确浮点数，否则返回整数）
        """
        if tolerance_cm is None:
            tolerance_cm = PrecisionConverter._calculate_dynamic_tolerance(cm_value)

        if cm_value <= 0:
            return 0.0

        try:
            # 使用Decimal进行高精度计算
            cm_decimal = Decimal(str(cm_value))
            dpi_decimal = Decimal(str(dpi))
            inch_per_cm = Decimal('2.54')

            # 精确计算：像素 = 厘米 * DPI / 2.54
            exact_px = cm_decimal * dpi_decimal / inch_per_cm

            # 如果要求返回浮点数，需要确保反向转换不超过原值，同时尽可能接近
            if return_float:
                # 使用二分查找找到最接近但不超过原值的像素值
                exact_float = float(exact_px)

                # 验证精确值是否超过原值（使用严格的比较，考虑浮点精度）
                back_cm = PrecisionConverter.px_to_cm_precise(exact_float, dpi)
                tolerance = 1e-9  # 非常小的容差，用于处理浮点精度问题

                if back_cm <= cm_value + tolerance:
                    # 精确值不超过（考虑浮点精度），但需要进一步验证
                    result_px = round(exact_float, 3)  # 保留3位小数
                    final_back_cm = PrecisionConverter.px_to_cm_precise(result_px, dpi)

                    if final_back_cm <= cm_value + tolerance:
                        log.debug(f"浮点转换(精确值不超过): {cm_value}cm → {result_px}px → {final_back_cm:.9f}cm")
                        return result_px
                    else:
                        # 四舍五入后超过了，需要向下调整
                        log.debug(f"四舍五入后超过，需要调整: {exact_float}px → {result_px}px")
                        # 继续执行二分查找

                # 精确值超过原值，使用二分查找找到最优值
                low_px = 0.0
                high_px = exact_float
                best_px = float(int(exact_px))  # 初始值为向下取整

                # 验证初始值
                initial_back_cm = PrecisionConverter.px_to_cm_precise(best_px, dpi)
                if initial_back_cm > cm_value:
                    # 如果向下取整仍然超过，继续向下调整
                    best_px = best_px - 1.0

                # 二分查找，精度到0.001像素
                tolerance = 1e-9  # 浮点精度容差

                for _ in range(30):  # 增加迭代次数确保精度
                    mid_px = (low_px + high_px) / 2
                    mid_back_cm = PrecisionConverter.px_to_cm_precise(mid_px, dpi)

                    if mid_back_cm <= cm_value + tolerance:
                        # 不超过原值（考虑浮点精度），这是一个有效候选
                        best_px = mid_px
                        low_px = mid_px
                    else:
                        # 超过原值，缩小搜索范围
                        high_px = mid_px

                    # 如果搜索范围足够小，停止
                    if high_px - low_px < 0.0001:  # 提高精度要求
                        break

                # 最终验证和微调
                result_px = round(best_px, 3)
                final_back_cm = PrecisionConverter.px_to_cm_precise(result_px, dpi)

                # 如果四舍五入后仍然超过，向下微调
                adjustment_count = 0
                while final_back_cm > cm_value + tolerance and result_px > 0 and adjustment_count < 1000:
                    result_px -= 0.001
                    result_px = round(result_px, 3)  # 保持精度
                    final_back_cm = PrecisionConverter.px_to_cm_precise(result_px, dpi)
                    adjustment_count += 1

                # 最终安全检查
                if final_back_cm > cm_value + tolerance:
                    # 如果仍然超过，强制使用向下取整
                    result_px = float(int(exact_px))
                    final_back_cm = PrecisionConverter.px_to_cm_precise(result_px, dpi)
                    log.warning(f"强制使用向下取整: {cm_value}cm → {result_px}px → {final_back_cm:.9f}cm")
                else:
                    log.debug(f"浮点转换(二分查找最优): {cm_value}cm → {result_px}px → {final_back_cm:.9f}cm (误差: {cm_value - final_back_cm:.9f}cm)")

                return result_px

            # 获取候选像素值（向下取整和向上取整）
            px_floor = int(exact_px)
            px_ceil = px_floor + 1

            # 测试候选值，优先选择不超过原值且最接近的
            candidates = [px_floor, px_ceil]

            # 分析所有候选值
            valid_candidates = []
            for px_candidate in candidates:
                back_cm = PrecisionConverter.px_to_cm_precise(px_candidate, dpi)
                error = abs(back_cm - cm_value)
                exceeds = back_cm > cm_value

                valid_candidates.append({
                    'px': px_candidate,
                    'back_cm': back_cm,
                    'error': error,
                    'exceeds': exceeds,
                    'distance_from_original': abs(back_cm - cm_value)  # 与原值的距离
                })

            # 强制优先选择不超过原值的候选值，确保像素转回厘米不大于原始厘米值
            not_exceeding = [c for c in valid_candidates if not c['exceeds']]
            if not_exceeding:
                # 在不超过原值的候选值中，优先选择满足精度的，其次选择最接近的
                precise_not_exceeding = [c for c in not_exceeding if c['error'] <= tolerance_cm]
                if precise_not_exceeding:
                    best = min(precise_not_exceeding, key=lambda x: x['distance_from_original'])
                    log.debug(f"精确转换(不超过且满足精度): {cm_value}cm → {best['px']}px → {best['back_cm']:.6f}cm (误差: {best['error']:.6f}cm)")
                else:
                    best = min(not_exceeding, key=lambda x: x['distance_from_original'])
                    log.debug(f"选择不超过且最接近: {cm_value}cm → {best['px']}px → {best['back_cm']:.6f}cm (距离: {best['distance_from_original']:.6f}cm)")
                return float(best['px'])

            # 如果所有候选值都超过原值，强制使用向下取整确保不超过原值
            safe_px = px_floor
            safe_back_cm = PrecisionConverter.px_to_cm_precise(safe_px, dpi)
            log.warning(f"所有候选值都超过原值，强制使用向下取整: {cm_value}cm → {safe_px}px → {safe_back_cm:.6f}cm")
            return float(safe_px)

        except Exception as e:
            log.error(f"高精度转换失败，回退到传统方法: {e}")
            # 回退到传统方法
            if return_float:
                return cm_value * dpi / 2.54
            else:
                return float(round(cm_value * dpi / 2.54))
    
    @staticmethod
    def px_to_cm_precise(px_value: float, dpi: int = 72) -> float:
        """
        高精度像素转厘米转换

        Args:
            px_value: 像素值（支持浮点数）
            dpi: DPI值

        Returns:
            float: 厘米值
        """
        if px_value <= 0:
            return 0.0

        try:
            # 使用Decimal进行高精度计算
            px_decimal = Decimal(str(px_value))
            dpi_decimal = Decimal(str(dpi))
            inch_per_cm = Decimal('2.54')

            # 精确计算：厘米 = 像素 * 2.54 / DPI
            exact_cm = px_decimal * inch_per_cm / dpi_decimal

            # 保留足够的精度（6位小数）
            return float(exact_cm.quantize(Decimal('0.000001'), rounding=ROUND_HALF_UP))

        except Exception as e:
            log.error(f"高精度反向转换失败，回退到传统方法: {e}")
            # 回退到传统方法
            return round(px_value * 2.54 / dpi, 6)
    
    @staticmethod
    def validate_conversion_precision(cm_value: float, dpi: int = 72, tolerance_cm: float = None) -> Tuple[bool, int, float, float]:
        """
        验证转换精度
        
        Args:
            cm_value: 原始厘米值
            dpi: DPI值
            tolerance_cm: 容差（厘米）
            
        Returns:
            tuple: (是否精确, 像素值, 反向转换厘米值, 误差)
        """
        if tolerance_cm is None:
            tolerance_cm = PrecisionConverter.DEFAULT_TOLERANCE_CM
            
        px_value = PrecisionConverter.cm_to_px_precise(cm_value, dpi, tolerance_cm)
        back_cm = PrecisionConverter.px_to_cm_precise(px_value, dpi)
        error = abs(back_cm - cm_value)
        is_precise = error <= tolerance_cm
        
        return is_precise, px_value, back_cm, error
    
    @staticmethod
    def batch_convert_cm_to_px(cm_values: list, dpi: int = 72, tolerance_cm: float = None) -> list:
        """
        批量厘米转像素转换
        
        Args:
            cm_values: 厘米值列表
            dpi: DPI值
            tolerance_cm: 容差（厘米）
            
        Returns:
            list: 像素值列表
        """
        return [PrecisionConverter.cm_to_px_precise(cm, dpi, tolerance_cm) for cm in cm_values]
    
    @staticmethod
    def get_conversion_report(cm_value: float, dpi: int = 72) -> str:
        """
        生成转换精度报告
        
        Args:
            cm_value: 厘米值
            dpi: DPI值
            
        Returns:
            str: 转换报告
        """
        # 传统方法
        traditional_px = round(cm_value * dpi / 2.54)
        traditional_back_cm = round(traditional_px * 2.54 / dpi, 2)
        traditional_error = abs(traditional_back_cm - cm_value)
        
        # 高精度方法
        precise_px = PrecisionConverter.cm_to_px_precise(cm_value, dpi)
        precise_back_cm = PrecisionConverter.px_to_cm_precise(precise_px, dpi)
        precise_error = abs(precise_back_cm - cm_value)
        
        report = f"""
转换精度对比报告
==================
原始值: {cm_value} cm
DPI: {dpi}

传统方法:
  {cm_value} cm → {traditional_px} px → {traditional_back_cm} cm
  误差: {traditional_error:.6f} cm

高精度方法:
  {cm_value} cm → {precise_px} px → {precise_back_cm:.6f} cm
  误差: {precise_error:.6f} cm

精度提升: {((traditional_error - precise_error) / traditional_error * 100) if traditional_error > 0 else 0:.2f}%
"""
        return report

# 便捷函数
def cm_to_px_precise(cm_value: float, dpi: int = 72) -> float:
    """高精度厘米转像素便捷函数（返回整数像素）"""
    return PrecisionConverter.cm_to_px_precise(cm_value, dpi, return_float=False)

def cm_to_px_float(cm_value: float, dpi: int = 72) -> float:
    """高精度厘米转像素便捷函数（返回浮点数像素）"""
    return PrecisionConverter.cm_to_px_precise(cm_value, dpi, return_float=True)

def px_to_cm_precise(px_value: float, dpi: int = 72) -> float:
    """高精度像素转厘米便捷函数"""
    return PrecisionConverter.px_to_cm_precise(px_value, dpi)

def validate_precision(cm_value: float, dpi: int = 72) -> bool:
    """验证转换精度便捷函数"""
    is_precise, _, _, _ = PrecisionConverter.validate_conversion_precision(cm_value, dpi)
    return is_precise