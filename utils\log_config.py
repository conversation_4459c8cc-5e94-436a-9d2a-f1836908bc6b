#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
日志配置模块

提供统一的日志配置和格式化功能：
1. 统一日志格式
2. 集中日志配置
3. 安全的日志格式化
4. 自定义日志处理器
"""

import sys
import logging
from .log_message_mapper import map_log_message, is_useless_log

# 默认日志格式
DEFAULT_LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# 自定义日志格式化器，处理特殊字符和格式化问题，并映射敏感信息
class SafeFormatter(logging.Formatter):
    """安全的日志格式化器，处理特殊字符、格式化问题并映射敏感信息"""
    
    def __init__(self, fmt=None, datefmt=None, style='%', validate=True, enable_mapping=True):
        """初始化格式化器
        
        Args:
            fmt: 日志格式
            datefmt: 日期格式
            style: 格式样式
            validate: 是否验证格式
            enable_mapping: 是否启用敏感信息映射
        """
        super().__init__(fmt, datefmt, style, validate)
        self.enable_mapping = enable_mapping
    
    def format(self, record):
        """安全格式化日志记录并映射敏感信息"""
        # 保存原始消息
        original_msg = record.msg
        original_args = record.args
        
        try:
            # 预处理消息，映射敏感信息
            if self.enable_mapping and isinstance(record.msg, str):
                # 检查是否为无用日志
                if is_useless_log(record.msg):
                    return None  # 返回None表示跳过此日志
                
                # 映射敏感信息
                mapped_msg = map_log_message(record.msg)
                if mapped_msg is None:
                    return None  # 跳过无用日志
                
                record.msg = mapped_msg
            
            # 尝试使用原始格式化方法
            result = super().format(record)
            return result
        except Exception:
            # 如果格式化失败，使用安全的方式处理
            try:
                # 尝试将消息转换为字符串
                if isinstance(record.msg, (dict, list, tuple)):
                    record.msg = str(record.msg)

                # 如果有参数，尝试安全格式化
                if record.args:
                    try:
                        # 尝试正常格式化
                        record.msg = record.msg % record.args
                    except:
                        # 如果格式化失败，简单连接
                        record.msg = f"{record.msg} {str(record.args)}"
                    # 清空参数，防止再次格式化
                    record.args = ()

                # 再次尝试格式化
                result = super().format(record)
                return result
            except Exception as inner_e:
                # 如果仍然失败，返回一个安全的错误消息
                error_msg = f"日志处理错误: {str(inner_e)}"
                record.msg = error_msg
                record.args = ()
                return super().format(record)
            finally:
                # 恢复原始消息和参数
                record.msg = original_msg
                record.args = original_args

# 自定义日志处理器，捕获所有异常并过滤无用日志
class SafeHandler(logging.StreamHandler):
    """安全的日志处理器，捕获所有异常并过滤无用日志"""

    def __init__(self, stream=None):
        """初始化安全日志处理器

        Args:
            stream: 输出流，如果为None则使用安全的默认流
        """
        # 确保有一个安全的输出流
        if stream is None:
            # 在PyInstaller环境中，sys.stdout可能为None，需要安全处理
            if hasattr(sys, 'stdout') and sys.stdout is not None:
                stream = sys.stdout
            elif hasattr(sys, 'stderr') and sys.stderr is not None:
                stream = sys.stderr
            else:
                # 如果标准流都不可用，创建一个空的流对象
                import io
                stream = io.StringIO()

        super().__init__(stream)

    def emit(self, record):
        """安全发送日志记录，自动过滤无用日志"""
        try:
            # 检查流是否可用
            if self.stream is None:
                return

            # 格式化记录
            formatted_msg = self.format(record)

            # 如果格式化器返回None，跳过此日志
            if formatted_msg is None:
                return

            # 检查流是否有write方法
            if not hasattr(self.stream, 'write'):
                return

            # 发送日志
            self.stream.write(formatted_msg + self.terminator)

            # 安全刷新
            if hasattr(self.stream, 'flush'):
                self.stream.flush()

        except (AttributeError, OSError, IOError) as e:
            # 流相关错误，尝试使用备用输出
            try:
                if hasattr(sys, 'stderr') and sys.stderr is not None and hasattr(sys.stderr, 'write'):
                    sys.stderr.write(f"日志处理器流错误: {str(e)}\n")
            except:
                # 如果连stderr都不可用，则静默忽略
                pass
        except Exception as e:
            # 其他错误，尝试输出到stderr
            try:
                if hasattr(sys, 'stderr') and sys.stderr is not None and hasattr(sys.stderr, 'write'):
                    sys.stderr.write(f"日志处理器错误: {str(e)}\n")
            except:
                # 如果连stderr都不可用，则静默忽略
                pass

def configure_logging(level=logging.INFO, log_format=None, log_file=None, enable_mapping=True):
    """配置日志系统
    
    Args:
        level: 日志级别
        log_format: 日志格式
        log_file: 日志文件路径
        enable_mapping: 是否启用敏感信息映射
    """
    # 使用默认格式如果未指定
    if log_format is None:
        log_format = DEFAULT_LOG_FORMAT
    
    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建安全的格式化器，支持敏感信息映射
    formatter = SafeFormatter(log_format, enable_mapping=enable_mapping)
    
    # 创建并配置控制台处理器
    console_handler = SafeHandler()
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 如果指定了日志文件，创建文件处理器
    if log_file:
        try:
            # 文件处理器也使用安全处理器，确保一致的日志过滤
            file_stream = open(log_file, 'a', encoding='utf-8')
            file_handler = SafeHandler(file_stream)
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
        except Exception as e:
            # 安全地输出错误信息
            try:
                if hasattr(sys, 'stderr') and sys.stderr is not None and hasattr(sys.stderr, 'write'):
                    sys.stderr.write(f"无法创建日志文件 {log_file}: {str(e)}\n")
            except:
                # 如果连stderr都不可用，则静默忽略
                pass
    
    return root_logger

def get_logger(name):
    """获取指定名称的日志记录器
    
    Args:
        name: 日志记录器名称
    
    Returns:
        Logger: 日志记录器实例
    """
    return logging.getLogger(name)

# 默认配置
configure_logging()
