#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
PIL配置统一管理模块

提供统一的PIL配置管理，确保所有使用PIL的模块都使用相同的配置参数：
1. 统一设置图片大小限制
2. 统一配置警告过滤
3. 统一配置图片加载选项
4. 提供配置验证和日志记录
"""

import logging
import warnings
import os

# 配置日志
log = logging.getLogger("PILConfig")

class PILConfigManager:
    """PIL配置管理器"""
    
    # 默认配置参数
    DEFAULT_MAX_IMAGE_PIXELS = 1_500_000_000  # 1.5G像素，约38000x38000
    DEFAULT_LOAD_TRUNCATED_IMAGES = True
    DEFAULT_IGNORE_DECOMPRESSION_WARNING = True
    
    _configured = False
    _current_config = {}
    
    @classmethod
    def configure_pil(cls, 
                     max_image_pixels=None, 
                     load_truncated_images=None,
                     ignore_decompression_warning=None,
                     force_reconfigure=False):
        """配置PIL参数
        
        Args:
            max_image_pixels: 最大图片像素数限制
            load_truncated_images: 是否允许加载截断的图片
            ignore_decompression_warning: 是否忽略解压缩炸弹警告
            force_reconfigure: 是否强制重新配置
            
        Returns:
            bool: 配置是否成功
        """
        try:
            # 如果已经配置过且不强制重新配置，则跳过
            if cls._configured and not force_reconfigure:
                log.debug("PIL已配置，跳过重复配置")
                return True
            
            # 导入PIL
            from PIL import Image, ImageFile
            
            # 设置参数（使用提供的值或默认值）
            max_pixels = max_image_pixels if max_image_pixels is not None else cls.DEFAULT_MAX_IMAGE_PIXELS
            load_truncated = load_truncated_images if load_truncated_images is not None else cls.DEFAULT_LOAD_TRUNCATED_IMAGES
            ignore_warning = ignore_decompression_warning if ignore_decompression_warning is not None else cls.DEFAULT_IGNORE_DECOMPRESSION_WARNING
            
            # 应用配置
            Image.MAX_IMAGE_PIXELS = max_pixels
            ImageFile.LOAD_TRUNCATED_IMAGES = load_truncated
            
            if ignore_warning:
                warnings.filterwarnings("ignore", category=Image.DecompressionBombWarning)
            
            # 记录当前配置
            cls._current_config = {
                'max_image_pixels': max_pixels,
                'load_truncated_images': load_truncated,
                'ignore_decompression_warning': ignore_warning,
                'configured_at': log.name
            }
            
            cls._configured = True
            
            # 记录配置信息
            log.info(f"PIL配置完成:")
            log.info(f"  MAX_IMAGE_PIXELS: {max_pixels:,}")
            log.info(f"  LOAD_TRUNCATED_IMAGES: {load_truncated}")
            log.info(f"  忽略解压缩炸弹警告: {ignore_warning}")
            
            return True
            
        except ImportError:
            log.warning("PIL库未安装，无法配置PIL参数")
            return False
        except Exception as e:
            log.error(f"配置PIL时出错: {str(e)}")
            return False
    
    @classmethod
    def get_current_config(cls):
        """获取当前PIL配置
        
        Returns:
            dict: 当前配置信息
        """
        if not cls._configured:
            return None
        return cls._current_config.copy()
    
    @classmethod
    def is_configured(cls):
        """检查PIL是否已配置
        
        Returns:
            bool: 是否已配置
        """
        return cls._configured
    
    @classmethod
    def validate_image_size(cls, width, height, file_path=None):
        """验证图片尺寸是否在允许范围内
        
        Args:
            width: 图片宽度
            height: 图片高度
            file_path: 文件路径（用于日志）
            
        Returns:
            bool: 尺寸是否有效
        """
        if not cls._configured:
            log.warning("PIL未配置，无法验证图片尺寸")
            return True
        
        total_pixels = width * height
        max_pixels = cls._current_config.get('max_image_pixels', cls.DEFAULT_MAX_IMAGE_PIXELS)
        
        if total_pixels > max_pixels:
            file_name = os.path.basename(file_path) if file_path else "未知文件"
            log.warning(f"图片尺寸超出限制: {file_name}, "
                       f"尺寸: {width}x{height}, 像素: {total_pixels:,}, "
                       f"限制: {max_pixels:,}")
            return False
        
        return True
    
    @classmethod
    def get_memory_estimate(cls, width, height, channels=3):
        """估算图片内存占用
        
        Args:
            width: 图片宽度
            height: 图片高度
            channels: 颜色通道数（默认RGB=3）
            
        Returns:
            dict: 内存估算信息
        """
        total_pixels = width * height
        bytes_per_pixel = channels
        total_bytes = total_pixels * bytes_per_pixel
        
        return {
            'total_pixels': total_pixels,
            'bytes_per_pixel': bytes_per_pixel,
            'total_bytes': total_bytes,
            'mb': total_bytes / (1024 * 1024),
            'gb': total_bytes / (1024 * 1024 * 1024)
        }
    
    @classmethod
    def log_image_info(cls, width, height, file_path=None, level=logging.INFO):
        """记录图片信息日志
        
        Args:
            width: 图片宽度
            height: 图片高度
            file_path: 文件路径
            level: 日志级别
        """
        file_name = os.path.basename(file_path) if file_path else "未知文件"
        total_pixels = width * height
        memory_info = cls.get_memory_estimate(width, height)
        
        # 根据图片大小选择不同的日志级别和信息详细程度
        if total_pixels > 500_000_000:  # 5亿像素以上
            log.log(level, f"超大图片: {file_name}, "
                          f"尺寸: {width}x{height}, 像素: {total_pixels:,}, "
                          f"估算内存: {memory_info['mb']:.1f}MB")
        elif total_pixels > 100_000_000:  # 1亿像素以上
            log.log(level, f"大图片: {file_name}, "
                          f"尺寸: {width}x{height}, 像素: {total_pixels:,}")
        else:
            log.log(logging.DEBUG, f"图片: {file_name}, 尺寸: {width}x{height}")


def configure_pil_for_large_images():
    """为处理大图片配置PIL（便捷函数）"""
    return PILConfigManager.configure_pil()


def configure_pil_for_ultra_large_images():
    """为处理超大图片配置PIL（便捷函数）"""
    return PILConfigManager.configure_pil(
        max_image_pixels=3_000_000_000,  # 3G像素
        load_truncated_images=True,
        ignore_decompression_warning=True
    )


def configure_pil_conservative():
    """保守的PIL配置（便捷函数）"""
    return PILConfigManager.configure_pil(
        max_image_pixels=500_000_000,  # 500M像素
        load_truncated_images=False,
        ignore_decompression_warning=False
    )


# 自动配置（当模块被导入时）
if not PILConfigManager.is_configured():
    configure_pil_for_large_images()
