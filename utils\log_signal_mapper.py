# -*- coding: utf-8 -*-
"""
日志信号映射器

为PyQt信号系统提供日志信息映射功能：
1. 拦截和映射log_signal.emit()消息
2. 过滤无用和重复的日志信息
3. 统一日志信息格式
4. 保护敏感技术信息
"""

from PyQt6.QtCore import QObject, pyqtSignal
from typing import Optional, Callable
from .log_message_mapper import map_log_message, is_useless_log

class LogSignalMapper(QObject):
    """日志信号映射器"""
    
    # 映射后的日志信号
    mapped_log_signal = pyqtSignal(str)
    
    def __init__(self, parent=None, enable_mapping=True):
        """初始化日志信号映射器
        
        Args:
            parent: 父对象
            enable_mapping: 是否启用映射功能
        """
        super().__init__(parent)
        self.enable_mapping = enable_mapping
        self._last_message = None
        self._repeat_count = 0
        self._max_repeats = 3  # 最大重复次数
    
    def emit_mapped_log(self, message: str):
        """发送映射后的日志消息
        
        Args:
            message: 原始日志消息
        """
        if not message or not isinstance(message, str):
            return
        
        # 如果禁用映射，直接发送
        if not self.enable_mapping:
            self.mapped_log_signal.emit(message)
            return
        
        # 检查是否为无用日志
        if is_useless_log(message):
            return
        
        # 映射敏感信息
        mapped_message = map_log_message(message)
        if mapped_message is None:
            return
        
        # 检查重复消息
        if self._is_duplicate_message(mapped_message):
            return
        
        # 发送映射后的消息
        self.mapped_log_signal.emit(mapped_message)
        self._update_last_message(mapped_message)
    
    def _is_duplicate_message(self, message: str) -> bool:
        """检查是否为重复消息
        
        Args:
            message: 消息内容
            
        Returns:
            是否为重复消息
        """
        if self._last_message == message:
            self._repeat_count += 1
            if self._repeat_count >= self._max_repeats:
                return True
        else:
            self._repeat_count = 0
        
        return False
    
    def _update_last_message(self, message: str):
        """更新最后一条消息
        
        Args:
            message: 消息内容
        """
        if self._last_message != message:
            self._last_message = message
            self._repeat_count = 0
    
    def set_mapping_enabled(self, enabled: bool):
        """设置是否启用映射
        
        Args:
            enabled: 是否启用
        """
        self.enable_mapping = enabled
    
    def reset_duplicate_tracking(self):
        """重置重复消息跟踪"""
        self._last_message = None
        self._repeat_count = 0

class MappedLogSignal:
    """映射的日志信号包装器"""
    
    def __init__(self, original_signal=None, enable_mapping=True):
        """初始化映射的日志信号
        
        Args:
            original_signal: 原始信号对象
            enable_mapping: 是否启用映射
        """
        self.original_signal = original_signal
        self.mapper = LogSignalMapper(enable_mapping=enable_mapping)
        
        # 如果有原始信号，连接到映射器
        if original_signal:
            self.mapper.mapped_log_signal.connect(original_signal.emit)
    
    def emit(self, message: str):
        """发送日志消息
        
        Args:
            message: 日志消息
        """
        self.mapper.emit_mapped_log(message)
    
    def connect(self, slot):
        """连接信号槽
        
        Args:
            slot: 槽函数
        """
        self.mapper.mapped_log_signal.connect(slot)
    
    def disconnect(self, slot=None):
        """断开信号槽
        
        Args:
            slot: 槽函数，如果为None则断开所有连接
        """
        if slot:
            self.mapper.mapped_log_signal.disconnect(slot)
        else:
            self.mapper.mapped_log_signal.disconnect()

def create_mapped_log_signal(original_signal=None, enable_mapping=True) -> MappedLogSignal:
    """创建映射的日志信号
    
    Args:
        original_signal: 原始信号对象
        enable_mapping: 是否启用映射
        
    Returns:
        映射的日志信号对象
    """
    return MappedLogSignal(original_signal, enable_mapping)

def wrap_log_signal(worker_instance, signal_name='log_signal', enable_mapping=True):
    """包装工作器实例的日志信号
    
    Args:
        worker_instance: 工作器实例
        signal_name: 信号名称
        enable_mapping: 是否启用映射
    """
    if hasattr(worker_instance, signal_name):
        original_signal = getattr(worker_instance, signal_name)
        mapped_signal = create_mapped_log_signal(original_signal, enable_mapping)
        setattr(worker_instance, f'mapped_{signal_name}', mapped_signal)
        return mapped_signal
    return None