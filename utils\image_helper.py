#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
图像处理辅助类
提供简化的图像处理接口，减少重复代码
"""

import os
from typing import Dict, Any, List, Tuple, Optional
from utils.log_config import get_logger
log = get_logger("ImageHelper")


class ImageHelper:
    """图像处理辅助类

    提供简化的图像处理接口，避免重复的图像处理代码
    """

    @staticmethod
    def validate_image_path(image_path: str) -> bool:
        """验证图像路径是否有效

        Args:
            image_path: 图像路径

        Returns:
            bool: 路径是否有效
        """
        if not image_path:
            return False

        if not os.path.exists(image_path):
            return False

        # 检查文件扩展名
        valid_extensions = {'.jpg', '.jpeg', '.png', '.tiff', '.tif', '.bmp', '.gif'}
        _, ext = os.path.splitext(image_path.lower())
        return ext in valid_extensions

    @staticmethod
    def get_image_info(image_path: str) -> Optional[Dict[str, Any]]:
        """获取图像基本信息

        Args:
            image_path: 图像路径

        Returns:
            Dict: 图像信息，包含width, height, size等
        """
        if not ImageHelper.validate_image_path(image_path):
            return None

        try:
            # 获取文件大小
            file_size = os.path.getsize(image_path)

            # 获取文件名
            filename = os.path.basename(image_path)
            name, ext = os.path.splitext(filename)

            return {
                'path': image_path,
                'filename': filename,
                'name': name,
                'extension': ext,
                'file_size': file_size,
                'exists': True
            }
        except Exception as e:
            log.error(f"获取图像信息失败: {image_path}, {str(e)}")
            return None

    @staticmethod
    def prepare_image_data(images_info: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """准备图像数据，统一格式，确保路径和尺寸数据的一致性

        Args:
            images_info: 原始图像信息列表

        Returns:
            List[Dict]: 标准化的图像数据列表，保证数据一致性
        """
        prepared_data = []

        for i, img_info in enumerate(images_info):
            try:
                # 获取图片路径，优先级：path > image_path > 其他
                path = img_info.get('path', img_info.get('image_path', ''))

                # 如果有路径，验证图片实际存在并获取真实尺寸
                if path and os.path.exists(path):
                    # 获取图片的真实信息，确保数据一致性
                    real_info = get_image_info_with_size(path)
                    if real_info:
                        # 使用真实的图片信息
                        width = real_info['width']
                        height = real_info['height']
                        file_size = real_info['file_size']
                        file_size_mb = real_info['file_size_mb']
                        name = img_info.get('name', img_info.get('filename', real_info['name']))
                    else:
                        log.warning(f"无法读取图片信息: {path}")
                        continue
                else:
                    # 如果没有有效路径，使用提供的尺寸数据
                    width = img_info.get('width', img_info.get('width_cm', 0))
                    height = img_info.get('height', img_info.get('height_cm', 0))
                    file_size = img_info.get('file_size', 0)
                    file_size_mb = format_file_size_mb(file_size) if file_size > 0 else "0M"
                    name = img_info.get('name', img_info.get('filename', f'Image_{i+1}'))

                # 验证必要字段
                if width <= 0 or height <= 0:
                    log.warning(f"图像 {name} 尺寸无效: {width}x{height}")
                    continue

                # 标准化数据，确保所有字段的一致性
                standard_data = {
                    'id': str(i + 1),
                    'name': name,
                    'path': path,
                    'width': float(width),
                    'height': float(height),
                    'area': float(width * height),
                    'aspect_ratio': float(width / height) if height > 0 else 1.0,
                    'file_size': file_size,
                    'file_size_mb': file_size_mb,
                    'original_index': i,
                    'data_source': 'verified' if path and os.path.exists(path) else 'provided'
                }

                # 保留其他字段，但确保不覆盖已标准化的关键字段
                for key, value in img_info.items():
                    if key not in standard_data:
                        standard_data[key] = value

                prepared_data.append(standard_data)

            except Exception as e:
                log.error(f"处理图像数据时出错 {i}: {str(e)}")
                continue

        log.info(f"成功准备 {len(prepared_data)} 个图像数据项")
        return prepared_data

    @staticmethod
    def convert_cm_to_px(cm_value: float, ppi: int = 72) -> int:
        """将厘米转换为像素（高精度版本）

        Args:
            cm_value: 厘米值
            ppi: 像素密度

        Returns:
            int: 像素值（确保反向转换精度）
        """
        try:
            from core.precision_converter import PrecisionConverter
            return PrecisionConverter.cm_to_px_precise(cm_value, ppi)
        except ImportError:
            # 回退到传统方法
            return int(round(cm_value * ppi / 2.54))

    @staticmethod
    def convert_px_to_cm(px_value: int, ppi: int = 72) -> float:
        """将像素转换为厘米（高精度版本）

        Args:
            px_value: 像素值
            ppi: 像素密度

        Returns:
            float: 厘米值（高精度）
        """
        try:
            from core.precision_converter import PrecisionConverter
            return PrecisionConverter.px_to_cm_precise(px_value, ppi)
        except ImportError:
            # 回退到传统方法
            return round(px_value * 2.54 / ppi, 2)

    @staticmethod
    def calculate_utilization_rate(used_area: float, total_area: float) -> float:
        """计算利用率

        Args:
            used_area: 已使用面积
            total_area: 总面积

        Returns:
            float: 利用率百分比
        """
        if total_area <= 0:
            return 0.0
        return round((used_area / total_area) * 100, 2)

    @staticmethod
    def sort_images_by_strategy(images: List[Dict[str, Any]], strategy: str = 'area') -> List[Dict[str, Any]]:
        """按策略排序图像

        Args:
            images: 图像列表
            strategy: 排序策略 ('area', 'width', 'height', 'ratio')

        Returns:
            List[Dict]: 排序后的图像列表
        """
        try:
            if strategy == 'area':
                return sorted(images, key=lambda x: x.get('area', 0), reverse=True)
            elif strategy == 'width':
                return sorted(images, key=lambda x: x.get('width', 0), reverse=True)
            elif strategy == 'height':
                return sorted(images, key=lambda x: x.get('height', 0), reverse=True)
            elif strategy == 'ratio':
                return sorted(images, key=lambda x: x.get('aspect_ratio', 1.0), reverse=True)
            else:
                log.warning(f"未知排序策略: {strategy}，使用面积排序")
                return sorted(images, key=lambda x: x.get('area', 0), reverse=True)
        except Exception as e:
            log.error(f"图像排序失败: {str(e)}")
            return images

    @staticmethod
    def filter_valid_images(images: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤有效图像

        Args:
            images: 图像列表

        Returns:
            List[Dict]: 有效图像列表
        """
        valid_images = []

        for img in images:
            # 检查必要字段
            if not all(key in img for key in ['width', 'height']):
                continue

            # 检查尺寸有效性
            if img['width'] <= 0 or img['height'] <= 0:
                continue

            # 检查路径（如果提供）
            path = img.get('path', '')
            if path and not ImageHelper.validate_image_path(path):
                log.warning(f"图像路径无效: {path}")
                # 不跳过，可能是测试数据

            valid_images.append(img)

        log.info(f"过滤后有效图像: {len(valid_images)}/{len(images)}")
        return valid_images

    @staticmethod
    def create_placement_info(x: float, y: float, width: float, height: float,
                            image_id: str, rotated: bool = False, **kwargs) -> Dict[str, Any]:
        """创建标准的放置信息

        Args:
            x: X坐标
            y: Y坐标
            width: 宽度
            height: 高度
            image_id: 图像ID
            rotated: 是否旋转
            **kwargs: 其他参数

        Returns:
            Dict: 标准放置信息
        """
        placement = {
            'x': float(x),
            'y': float(y),
            'width': float(width),
            'height': float(height),
            'image_id': str(image_id),
            'rotated': bool(rotated),
            'area': float(width * height)
        }

        # 添加其他参数
        placement.update(kwargs)

        return placement


# 便捷函数
def validate_image_path(image_path: str) -> bool:
    """验证图像路径的便捷函数"""
    return ImageHelper.validate_image_path(image_path)


def prepare_image_data(images_info: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """准备图像数据的便捷函数"""
    return ImageHelper.prepare_image_data(images_info)


def convert_cm_to_px(cm_value: float, ppi: int = 72) -> int:
    """厘米转像素的便捷函数"""
    return ImageHelper.convert_cm_to_px(cm_value, ppi)


def convert_px_to_cm(px_value: int, ppi: int = 72) -> float:
    """像素转厘米的便捷函数"""
    return ImageHelper.convert_px_to_cm(px_value, ppi)


def calculate_utilization_rate(used_area: float, total_area: float) -> float:
    """计算利用率的便捷函数"""
    return ImageHelper.calculate_utilization_rate(used_area, total_area)


def format_file_size_mb(file_size_bytes: int) -> str:
    """
    格式化文件大小显示格式

    Args:
        file_size_bytes: 文件大小（字节）

    Returns:
        格式化后的文件大小字符串，小于1M用KB单位，大于等于1M用MB单位，保留两位小数
        例如："123.45K" 或 "2.30M"
    """
    if file_size_bytes <= 0:
        return "0.00K"

    # 小于1MB的用K为单位，保留两位小数
    if file_size_bytes < 1024 * 1024:
        size_kb = file_size_bytes / 1024
        return f"{size_kb:.2f}K"

    # 大于等于1MB的用M为单位，保留两位小数
    size_mb = file_size_bytes / (1024 * 1024)
    return f"{size_mb:.2f}M"


def get_image_info_with_size(image_path: str) -> Optional[Dict[str, Any]]:
    """
    获取图片完整信息，包括尺寸和文件大小

    Args:
        image_path: 图片路径

    Returns:
        包含完整信息的字典，确保路径和尺寸数据的一致性
    """
    if not image_path or not os.path.exists(image_path):
        return None

    try:
        # 获取基本文件信息
        file_size = os.path.getsize(image_path)
        filename = os.path.basename(image_path)
        name, ext = os.path.splitext(filename)

        # 确保PIL已正确配置
        try:
            from utils.pil_config import PILConfigManager
            if not PILConfigManager.is_configured():
                PILConfigManager.configure_pil()
        except ImportError:
            pass  # 如果统一配置模块不可用，继续使用默认配置

        # 获取图片尺寸
        from PIL import Image
        with Image.open(image_path) as img:
            width, height = img.size

        # 使用统一的PIL配置管理器记录图片信息（如果可用）
        try:
            from utils.pil_config import PILConfigManager
            PILConfigManager.log_image_info(width, height, image_path, level=logging.DEBUG)
        except ImportError:
            pass  # 如果统一配置模块不可用，跳过日志记录

        # 构建完整信息字典，确保数据一致性
        image_info = {
            'path': image_path,
            'filename': filename,
            'name': name,
            'extension': ext,
            'file_size': file_size,
            'file_size_mb': format_file_size_mb(file_size),
            'width': width,
            'height': height,
            'exists': True,
            'aspect_ratio': width / height if height > 0 else 1.0,
            'area': width * height
        }

        return image_info

    except Exception as e:
        # 增强错误处理
        error_msg = str(e)
        if "decompression bomb" in error_msg.lower() or "exceeds limit" in error_msg.lower():
            log.warning(f"图片过大被PIL安全限制阻止: {os.path.basename(image_path)}, 错误: {error_msg}")
            try:
                from utils.pil_config import PILConfigManager
                config = PILConfigManager.get_current_config()
                if config:
                    log.info(f"当前PIL限制: MAX_IMAGE_PIXELS={config['max_image_pixels']:,}")
            except ImportError:
                pass
        else:
            log.error(f"获取图片信息失败: {image_path}, {error_msg}")
        return None


def validate_image_data_consistency(images_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    验证和修复图片数据的一致性，确保路径和尺寸数据匹配

    Args:
        images_data: 图片数据列表

    Returns:
        验证和修复后的图片数据列表
    """
    validated_data = []
    inconsistent_count = 0

    for i, img_data in enumerate(images_data):
        try:
            path = img_data.get('path', img_data.get('image_path', ''))

            # 如果有有效路径，验证数据一致性
            if path and path != '未入库' and os.path.exists(path):
                # 获取真实的图片信息
                real_info = get_image_info_with_size(path)
                if real_info:
                    # 检查尺寸是否一致
                    provided_width = img_data.get('width', img_data.get('width_cm', 0))
                    provided_height = img_data.get('height', img_data.get('height_cm', 0))

                    real_width = real_info['width']
                    real_height = real_info['height']

                    # 如果尺寸不一致，使用真实尺寸并记录警告
                    if abs(provided_width - real_width) > 1 or abs(provided_height - real_height) > 1:
                        log.warning(f"图片 {path} 数据不一致: 提供尺寸 {provided_width}x{provided_height}, 实际尺寸 {real_width}x{real_height}")
                        inconsistent_count += 1

                        # 使用真实尺寸更新数据
                        img_data.update({
                            'width': real_width,
                            'height': real_height,
                            'width_cm': img_data.get('width_cm', real_width),
                            'height_cm': img_data.get('height_cm', real_height),
                            'file_size': real_info['file_size'],
                            'file_size_mb': real_info['file_size_mb'],
                            'data_corrected': True
                        })
                    else:
                        # 数据一致，补充文件大小信息
                        img_data.update({
                            'file_size': real_info['file_size'],
                            'file_size_mb': real_info['file_size_mb'],
                            'data_verified': True
                        })

            validated_data.append(img_data)

        except Exception as e:
            log.error(f"验证图片数据一致性时出错 {i}: {str(e)}")
            validated_data.append(img_data)  # 保留原数据

    if inconsistent_count > 0:
        log.warning(f"发现并修复了 {inconsistent_count} 个数据不一致的图片")

    log.info(f"数据一致性验证完成，处理了 {len(validated_data)} 个图片")
    return validated_data
