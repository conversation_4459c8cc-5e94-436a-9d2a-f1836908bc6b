#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高精度厘米-像素转换器

解决传统转换中的精度损失问题，确保转换的可逆性。
"""

import logging
from decimal import Decimal, ROUND_HALF_UP
from typing import Tuple, Optional

log = logging.getLogger(__name__)

class PrecisionConverter:
    """
    高精度厘米-像素转换器
    
    解决传统转换中的精度损失问题，确保转换的可逆性。
    核心原理：使用高精度计算和智能舍入策略，确保反向转换能回到原始值。
    """
    
    # 默认容差（厘米）- 根据尺寸动态调整
    DEFAULT_TOLERANCE_CM = 0.02  # 0.02cm = 0.2mm
    MIN_TOLERANCE_CM = 0.005     # 最小容差
    MAX_TOLERANCE_CM = 0.05      # 最大容差
    
    @staticmethod
    def _calculate_dynamic_tolerance(cm_value: float) -> float:
        """
        根据尺寸大小计算动态容差
        
        Args:
            cm_value: 厘米值
            
        Returns:
            float: 动态容差值
        """
        # 基础容差：尺寸越大，允许的绝对误差稍大
        # 但相对误差保持较小
        base_tolerance = PrecisionConverter.DEFAULT_TOLERANCE_CM
        
        # 对于大尺寸，适当放宽容差
        if cm_value > 100:
            tolerance = min(base_tolerance * 1.5, PrecisionConverter.MAX_TOLERANCE_CM)
        elif cm_value > 50:
            tolerance = base_tolerance * 1.2
        else:
            tolerance = max(base_tolerance, PrecisionConverter.MIN_TOLERANCE_CM)
            
        return tolerance
    
    @staticmethod
    def cm_to_px_precise(cm_value: float, dpi: int = 72, tolerance_cm: float = None) -> int:
        """
        高精度厘米转像素转换
        
        Args:
            cm_value: 厘米值
            dpi: DPI值
            tolerance_cm: 容差（厘米），默认0.005cm
            
        Returns:
            int: 像素值（确保反向转换精度在容差范围内）
        """
        if tolerance_cm is None:
            tolerance_cm = PrecisionConverter._calculate_dynamic_tolerance(cm_value)
            
        if cm_value <= 0:
            return 0
            
        try:
            # 使用Decimal进行高精度计算
            cm_decimal = Decimal(str(cm_value))
            dpi_decimal = Decimal(str(dpi))
            inch_per_cm = Decimal('2.54')
            
            # 精确计算：像素 = 厘米 * DPI / 2.54
            exact_px = cm_decimal * dpi_decimal / inch_per_cm
            
            # 获取候选像素值（向下取整和向上取整）
            px_floor = int(exact_px)
            px_ceil = px_floor + 1
            
            # 测试两个候选值的反向转换精度
            candidates = [px_floor, px_ceil]
            
            for px_candidate in candidates:
                # 计算反向转换的厘米值
                back_cm = PrecisionConverter.px_to_cm_precise(px_candidate, dpi)
                error = abs(back_cm - cm_value)
                
                if error <= tolerance_cm:
                    log.debug(f"精确转换: {cm_value}cm → {px_candidate}px → {back_cm}cm (误差: {error:.6f}cm)")
                    return px_candidate
            
            # 如果两个候选值都不满足精度要求，选择误差较小的
            errors = []
            for px_candidate in candidates:
                back_cm = PrecisionConverter.px_to_cm_precise(px_candidate, dpi)
                error = abs(back_cm - cm_value)
                errors.append((error, px_candidate))
            
            # 选择误差最小的
            best_error, best_px = min(errors)
            log.warning(f"无法达到目标精度，选择最佳: {cm_value}cm → {best_px}px (误差: {best_error:.6f}cm > {tolerance_cm}cm)")
            return best_px
            
        except Exception as e:
            log.error(f"高精度转换失败，回退到传统方法: {e}")
            # 回退到传统方法
            return round(cm_value * dpi / 2.54)
    
    @staticmethod
    def px_to_cm_precise(px_value: int, dpi: int = 72) -> float:
        """
        高精度像素转厘米转换
        
        Args:
            px_value: 像素值
            dpi: DPI值
            
        Returns:
            float: 厘米值
        """
        if px_value <= 0:
            return 0.0
            
        try:
            # 使用Decimal进行高精度计算
            px_decimal = Decimal(str(px_value))
            dpi_decimal = Decimal(str(dpi))
            inch_per_cm = Decimal('2.54')
            
            # 精确计算：厘米 = 像素 * 2.54 / DPI
            exact_cm = px_decimal * inch_per_cm / dpi_decimal
            
            # 保留足够的精度（6位小数）
            return float(exact_cm.quantize(Decimal('0.000001'), rounding=ROUND_HALF_UP))
            
        except Exception as e:
            log.error(f"高精度反向转换失败，回退到传统方法: {e}")
            # 回退到传统方法
            return round(px_value * 2.54 / dpi, 6)
    
    @staticmethod
    def validate_conversion_precision(cm_value: float, dpi: int = 72, tolerance_cm: float = None) -> Tuple[bool, int, float, float]:
        """
        验证转换精度
        
        Args:
            cm_value: 原始厘米值
            dpi: DPI值
            tolerance_cm: 容差（厘米）
            
        Returns:
            tuple: (是否精确, 像素值, 反向转换厘米值, 误差)
        """
        if tolerance_cm is None:
            tolerance_cm = PrecisionConverter.DEFAULT_TOLERANCE_CM
            
        px_value = PrecisionConverter.cm_to_px_precise(cm_value, dpi, tolerance_cm)
        back_cm = PrecisionConverter.px_to_cm_precise(px_value, dpi)
        error = abs(back_cm - cm_value)
        is_precise = error <= tolerance_cm
        
        return is_precise, px_value, back_cm, error
    
    @staticmethod
    def batch_convert_cm_to_px(cm_values: list, dpi: int = 72, tolerance_cm: float = None) -> list:
        """
        批量厘米转像素转换
        
        Args:
            cm_values: 厘米值列表
            dpi: DPI值
            tolerance_cm: 容差（厘米）
            
        Returns:
            list: 像素值列表
        """
        return [PrecisionConverter.cm_to_px_precise(cm, dpi, tolerance_cm) for cm in cm_values]
    
    @staticmethod
    def get_conversion_report(cm_value: float, dpi: int = 72) -> str:
        """
        生成转换精度报告
        
        Args:
            cm_value: 厘米值
            dpi: DPI值
            
        Returns:
            str: 转换报告
        """
        # 传统方法
        traditional_px = round(cm_value * dpi / 2.54)
        traditional_back_cm = round(traditional_px * 2.54 / dpi, 2)
        traditional_error = abs(traditional_back_cm - cm_value)
        
        # 高精度方法
        precise_px = PrecisionConverter.cm_to_px_precise(cm_value, dpi)
        precise_back_cm = PrecisionConverter.px_to_cm_precise(precise_px, dpi)
        precise_error = abs(precise_back_cm - cm_value)
        
        report = f"""
转换精度对比报告
==================
原始值: {cm_value} cm
DPI: {dpi}

传统方法:
  {cm_value} cm → {traditional_px} px → {traditional_back_cm} cm
  误差: {traditional_error:.6f} cm

高精度方法:
  {cm_value} cm → {precise_px} px → {precise_back_cm:.6f} cm
  误差: {precise_error:.6f} cm

精度提升: {((traditional_error - precise_error) / traditional_error * 100) if traditional_error > 0 else 0:.2f}%
"""
        return report

# 便捷函数
def cm_to_px_precise(cm_value: float, dpi: int = 72) -> int:
    """高精度厘米转像素便捷函数"""
    return PrecisionConverter.cm_to_px_precise(cm_value, dpi)

def px_to_cm_precise(px_value: int, dpi: int = 72) -> float:
    """高精度像素转厘米便捷函数"""
    return PrecisionConverter.px_to_cm_precise(px_value, dpi)

def validate_precision(cm_value: float, dpi: int = 72) -> bool:
    """验证转换精度便捷函数"""
    is_precise, _, _, _ = PrecisionConverter.validate_conversion_precision(cm_value, dpi)
    return is_precise