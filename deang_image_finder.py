"""
DeAI图片查找工具 - 主模块
提供TXT文件解析、图片检索、尺寸调整等功能的集成工具
"""

import json
import logging
import os
import queue
import re
import shutil
import sys
import threading
import time
import traceback
import warnings
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Any, Dict, List, Optional, Tuple
import ctypes

import pandas as pd
from openai import OpenAI
from openpyxl.styles import PatternFill
from PyQt6.QtCore import QObject, QThread, pyqtSignal
from PyQt6.QtGui import QCloseEvent, QTextCursor
from PyQt6.QtWidgets import (
    QApplication, QFileDialog, QFrame, QGroupBox, QHBoxLayout, QLabel,
    QLineEdit, QMainWindow, QMessageBox, QProgressBar, QPushButton,
    QTextEdit, QVBoxLayout, QWidget, QComboBox, QCheckBox
)

# 导入主项目模块
from ui.login_dialog import LoginDialog
from utils.advanced_settings_auth import AdvancedSettingsAuth
from utils.supabase_helper import SupabaseHelper
from core.deang_precision_coverter import PrecisionConverter 

# 导入图片索引相关模块
try:
    from core.image_indexer_duckdb import ImageIndexerDuckDB
    HAS_IMAGE_INDEXER = True
except ImportError:
    HAS_IMAGE_INDEXER = False
    logging.warning("图库索引器模块未找到，图片索引功能将被禁用")

# 导入图片查找器配置管理模块
try:
    from modules.image_finder_config import get_image_finder_config
    HAS_IMAGE_FINDER_CONFIG = True
except ImportError:
    HAS_IMAGE_FINDER_CONFIG = False
    logging.warning("图片查找器配置模块未找到，将使用默认配置")

# 常量定义
DEFAULT_DPI = 72
DEFAULT_WIDTH_VALUES = [160, 180, 200, 240, 300]
KCX_WIDTH_VALUES = [160, 200, 240, 300]
DEFAULT_WIDTH = 163
MAX_IMAGE_PIXELS = 500_000_000
MAX_WORKERS = 10
RATE_LIMIT_DELAY = 2
MAX_RETRIES = 3
BATCH_SIZE = 100
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600

# 应用信息
ROBOT_SMART_NAME = "DeAI-一键找图-表格解析AI助手"
ROBOT_CURRENT_VERSION = "0.0.3"
ROBOT_BOT_TAG = 'robot_carpet_first'
IMAGE_FINDER_BOT_TAG = 'deai_robot_smart'

# 配置PIL图片处理
try:
    from PIL import Image
    Image.MAX_IMAGE_PIXELS = MAX_IMAGE_PIXELS
    warnings.filterwarnings("ignore", category=Image.DecompressionBombWarning)
    logging.info("PIL图片大小限制已设置为500M像素")
except ImportError:
    logging.warning("PIL模块未找到，无法设置图片大小限制")

# 设置日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
log = logging.getLogger(__name__)

# 全局变量
main_window_instance = None

def set_main_window_instance(instance):
    """设置主窗口实例的引用"""
    global main_window_instance
    main_window_instance = instance

def log_network_request(message: str):
    """记录网络请求日志"""
    logging.info(f"网络请求: {message}")
    if main_window_instance:
        try:
            main_window_instance.update_log(message)
        except Exception as e:
            logging.error(f"更新UI日志时出错: {e}")

# ---------------------------
# 图片尺寸修改工具类
# ---------------------------
class ImageResizeProcessor:
    """处理图片尺寸修改的工具类"""

    @staticmethod
    def process_retrieved_file(file_path, target_folder, log_callback=None, progress_callback=None, default_dpi=DEFAULT_DPI):
        """处理单个已检索表格文件的图片尺寸修改

        Args:
            file_path: 已检索表格文件路径
            target_folder: 图片输出文件夹
            log_callback: 日志回调函数
            progress_callback: 进度回调函数 (current, total, percentage)
            default_dpi: 默认DPI值

        Returns:
            tuple: (success, processed_count, found_count, processing_stats)
        """
        try:
            import pandas as pd
            import time

            start_time = time.time()
            if log_callback:
                log_callback(f"🚀 开始处理已检索表格: {os.path.basename(file_path)}")

            filename = os.path.basename(file_path)
            base_name = os.path.splitext(filename)[0]

            # 移除'_已检索'后缀获取原始名称
            if base_name.endswith('_已检索'):
                base_name = base_name[:-len('_已检索')]

            # 读取Excel文件的所有sheet
            try:
                if log_callback:
                    log_callback(f"📖 正在读取表格文件...")
                xls = pd.ExcelFile(file_path)
                sheet_names = xls.sheet_names
                if log_callback:
                    log_callback(f"  ✅ 检测到 {len(sheet_names)} 个sheet: {', '.join(sheet_names)}")
            except Exception as e:
                if log_callback:
                    log_callback(f"  ❌ 读取表格失败: {str(e)}")
                return False, 0, 0, {}

            if not sheet_names:
                if log_callback:
                    log_callback(f"  ⚠️ 表格中没有sheet")
                return False, 0, 0, {}

            # 统计总行数用于进度计算
            total_rows = 0
            sheet_row_counts = {}
            for sheet_name in sheet_names:
                if sheet_name != '数据异常':
                    try:
                        df = pd.read_excel(file_path, sheet_name=sheet_name)
                        row_count = len(df) if not df.empty else 0
                        sheet_row_counts[sheet_name] = row_count
                        total_rows += row_count
                    except Exception:
                        sheet_row_counts[sheet_name] = 0

            if log_callback:
                log_callback(f"📊 总计需要处理 {total_rows} 行数据")

            total_processed = 0
            total_found = 0
            processed_rows = 0
            processing_stats = {
                'start_time': start_time,
                'total_rows': total_rows,
                'sheets_processed': 0,
                'images_processed': 0,
                'images_found': 0,
                'errors': 0,
                'skipped': 0
            }

            # 存储所有处理后的sheet数据
            processed_sheets = {}

            # 处理每个sheet
            for _, sheet_name in enumerate(sheet_names):
                try:
                    # 跳过数据异常sheet，但保留原始数据
                    if sheet_name == '数据异常':
                        if log_callback:
                            log_callback(f"    ⏭️ 跳过数据异常sheet: {sheet_name}")
                        # 保留原始数据异常sheet
                        processed_sheets[sheet_name] = pd.read_excel(file_path, sheet_name=sheet_name)
                        continue

                    # 读取sheet数据
                    if log_callback:
                        log_callback(f"    📋 正在处理sheet: {sheet_name}...")
                    df = pd.read_excel(file_path, sheet_name=sheet_name)

                    if df.empty:
                        if log_callback:
                            log_callback(f"    ⚠️ Sheet {sheet_name} 为空，跳过")
                        processed_sheets[sheet_name] = df  # 保留空sheet
                        continue

                    # 检查必要的列
                    required_columns = ['图案', '图片路径']
                    missing_columns = [col for col in required_columns if col not in df.columns]
                    if missing_columns:
                        if log_callback:
                            log_callback(f"    ❌ Sheet {sheet_name} 缺少必要列: {missing_columns}，跳过")
                        processing_stats['errors'] += 1
                        processed_sheets[sheet_name] = df  # 保留原始数据
                        continue

                    sheet_rows = len(df)
                    if log_callback:
                        log_callback(f"    🔄 处理sheet: {sheet_name} ({sheet_rows} 行)")

                    # 创建sheet级别的进度回调
                    def sheet_progress_callback(current, total, percentage):
                        global_current = processed_rows + current
                        global_percentage = int((global_current / total_rows) * 100) if total_rows > 0 else 0
                        if progress_callback:
                            progress_callback(global_current, total_rows, global_percentage)
                        if log_callback and current % max(1, total // 10) == 0:  # 每10%显示一次进度
                            log_callback(f"      📈 Sheet进度: {percentage}% ({current}/{total})")

                    # 处理该sheet的图片
                    sheet_processed, sheet_found, sheet_stats = ImageResizeProcessor._process_sheet_images(
                        df, base_name, sheet_name, target_folder, log_callback, sheet_progress_callback, default_dpi
                    )
                    
                    total_processed += sheet_processed
                    total_found += sheet_found
                    processed_rows += sheet_rows
                    processing_stats['sheets_processed'] += 1
                    processing_stats['images_processed'] += sheet_processed
                    processing_stats['images_found'] += sheet_found
                    processing_stats['errors'] += sheet_stats.get('errors', 0)
                    processing_stats['skipped'] += sheet_stats.get('skipped', 0)

                    # 计算处理速度
                    elapsed_time = time.time() - start_time
                    speed = processed_rows / elapsed_time if elapsed_time > 0 else 0
                    
                    if log_callback:
                        log_callback(f"    ✅ Sheet {sheet_name} 完成: 处理 {sheet_processed} 行，找到 {sheet_found} 个图片")
                        # 显示实际的文件夹名称
                        folder_suffix = "数据异常" if sheet_name == '数据异常' else sheet_name
                        log_callback(f"      📁 图片保存到文件夹: {base_name}_{folder_suffix}")
                        log_callback(f"      📊 处理速度: {speed:.1f} 行/秒，已用时: {elapsed_time:.1f}秒")

                    # 保存处理后的sheet数据
                    processed_sheets[sheet_name] = df

                    # 更新全局进度
                    global_percentage = int((processed_rows / total_rows) * 100) if total_rows > 0 else 0
                    if progress_callback:
                        progress_callback(processed_rows, total_rows, global_percentage)

                except Exception as e:
                    if log_callback:
                        log_callback(f"    ❌ 处理sheet {sheet_name} 时出错: {str(e)}")
                    processing_stats['errors'] += 1
                    # 即使出错也保留原始数据
                    try:
                        processed_sheets[sheet_name] = pd.read_excel(file_path, sheet_name=sheet_name)
                    except Exception:
                        pass
                    continue

            # 计算最终统计信息
            end_time = time.time()
            total_time = end_time - start_time
            processing_stats['end_time'] = end_time
            processing_stats['total_time'] = total_time
            processing_stats['average_speed'] = processed_rows / total_time if total_time > 0 else 0

            # 保存更新后的Excel文件
            try:
                if log_callback:
                    log_callback(f"  💾 正在保存更新后的Excel文件...")

                with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                    for sheet_name, sheet_df in processed_sheets.items():
                        sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)

                # 为未入库数据设置黄色背景
                Utils.apply_yellow_background_for_missing_images(file_path)

                if log_callback:
                    log_callback(f"  ✅ Excel文件已更新，包含处理状态信息")

            except Exception as e:
                if log_callback:
                    log_callback(f"  ⚠️ 保存Excel文件时出错: {str(e)}")

            if log_callback:
                log_callback(f"🎉 文件 {filename} 处理完成!")
                log_callback(f"  📈 统计信息:")
                log_callback(f"    • 总计处理: {total_processed} 行")
                log_callback(f"    • 找到图片: {total_found} 个")
                log_callback(f"    • 处理sheet: {processing_stats['sheets_processed']} 个")
                log_callback(f"    • 跳过记录: {processing_stats['skipped']} 个")
                log_callback(f"    • 错误数量: {processing_stats['errors']} 个")
                log_callback(f"    • 总用时: {total_time:.2f} 秒")
                log_callback(f"    • 平均速度: {processing_stats['average_speed']:.1f} 行/秒")
                log_callback(f"  📁 文件夹结构: 按宽幅分类，格式为 {base_name}_宽幅")

            return True, total_processed, total_found, processing_stats

        except Exception as e:
            if log_callback:
                log_callback(f"  ❌ 处理文件 {filename} 时出错: {str(e)}")
            return False, 0, 0, {'errors': 1}

    @staticmethod
    def _initialize_processing_stats(df):
        """初始化处理统计信息"""
        import time

        # 添加处理状态列（如果不存在）
        if '处理状态' not in df.columns:
            df['处理状态'] = ''

        return {
            'processed': 0,
            'found': 0,
            'skipped': 0,
            'errors': 0,
            'start_time': time.time(),
            'total_rows': len(df)
        }



    @staticmethod
    def _create_task_folder(base_name, sheet_name, target_folder, log_callback=None):
        """创建任务文件夹"""
        # 创建基于宽幅的任务图库文件夹
        if sheet_name == '数据异常':
            task_folder_name = f"{base_name}_数据异常"
        else:
            task_folder_name = f"{base_name}_{sheet_name}"

        task_folder_path = os.path.join(target_folder, task_folder_name)

        if not os.path.exists(task_folder_path):
            os.makedirs(task_folder_path)
            if log_callback:
                log_callback(f"  📁 创建任务文件夹: {task_folder_name}")

        return task_folder_path

    @staticmethod
    def _process_sheet_images(df, base_name, sheet_name, target_folder, log_callback=None, progress_callback=None, default_dpi=DEFAULT_DPI):
        """处理单个sheet中的图片数据"""
        try:
            # 初始化统计信息
            stats = ImageResizeProcessor._initialize_processing_stats(df)

            # 初始化必要的变量
            import time
            total_rows = len(df)
            processed_count = 0
            found_count = 0
            skipped_count = 0
            error_count = 0
            start_time = time.time()

            # 创建任务文件夹
            task_folder_path = ImageResizeProcessor._create_task_folder(
                base_name, sheet_name, target_folder, log_callback
            )

            # 先统计总的图片数量（按数量列累加）
            total_expected_images = 0
            for _, row in df.iterrows():
                image_path = row.get('图片路径', '')
                if pd.isna(image_path) or str(image_path).strip() in ['', '未入库', '图案为空']:
                    continue
                quantity = 1
                possible_quantity_fields = ['数量', 'quantity', '份数', 'count', 'num']
                for field in possible_quantity_fields:
                    if field in row and pd.notna(row[field]):
                        try:
                            raw_quantity = float(row[field])
                            quantity = int(raw_quantity)
                            if quantity <= 0:
                                quantity = 1
                            elif quantity > 1000:
                                quantity = 100
                            break
                        except (ValueError, TypeError):
                            continue
                total_expected_images += quantity

            # 处理每一行数据
            for index, row in df.iterrows():
                try:
                    current_row = index + 1
                    
                    # 更新进度
                    if progress_callback and current_row % max(1, total_rows // 20) == 0:  # 每5%更新一次
                        percentage = int((current_row / total_rows) * 100)
                        progress_callback(current_row, total_rows, percentage)
                    
                    # 获取图片名称
                    image_name = None
                    possible_name_fields = ['图案', '图片名称', 'image_name', 'filename', '文件名', '素材名称']
                    for field in possible_name_fields:
                        if field in row and pd.notna(row[field]) and str(row[field]).strip():
                            image_name = str(row[field]).strip()
                            break

                    # 直接从表格中获取图片路径
                    image_path = None
                    possible_path_fields = ['图片路径', 'image_path', 'path', '路径']
                    for field in possible_path_fields:
                        if field in row and pd.notna(row[field]) and str(row[field]).strip():
                            image_path = str(row[field]).strip()
                            break

                    # 跳过无图和未入库的记录
                    if (not image_name or pd.isna(image_name) or
                        str(image_name).strip() == "" or str(image_name) == "无图" or
                        not image_path or pd.isna(image_path) or
                        str(image_path).strip() == "" or str(image_path) == "未入库"):
                        df.at[index, '处理状态'] = '跳过'
                        skipped_count += 1
                        stats['skipped'] += 1
                        continue

                    # 获取数量
                    quantity = 1
                    if '数量' in row and pd.notna(row['数量']):
                        try:
                            quantity = int(float(row['数量']))
                            if quantity <= 0:
                                quantity = 1
                        except (ValueError, TypeError):
                            quantity = 1

                    # 验证图片文件是否存在
                    if not os.path.exists(image_path):
                        if log_callback and found_count < 5:
                            log_callback(f"        ⚠️ 图片文件不存在: {os.path.basename(image_path)}")
                        df.at[index, '处理状态'] = '文件不存在'
                        skipped_count += 1
                        stats['skipped'] += 1
                        continue

                    # 获取目标尺寸（cm单位）
                    target_width_cm = 0
                    target_height_cm = 0

                    # 尝试从多个可能的字段获取宽度
                    possible_width_fields = ['宽cm', '宽度', 'width', '宽', 'w']
                    for field in possible_width_fields:
                        if field in row and pd.notna(row[field]):
                            try:
                                target_width_cm = float(row[field])
                                break
                            except (ValueError, TypeError):
                                continue

                    # 尝试从多个可能的字段获取高度
                    possible_height_fields = ['高cm', '高度', 'height', '高', 'h']
                    for field in possible_height_fields:
                        if field in row and pd.notna(row[field]):
                            try:
                                target_height_cm = float(row[field])
                                break
                            except (ValueError, TypeError):
                                continue

                    # 数据验证：确保尺寸数据有效
                    if target_width_cm <= 0 or target_height_cm <= 0:
                        if log_callback:
                            log_callback(f"        警告: {image_name} 尺寸数据无效 (宽:{target_width_cm}cm, 高:{target_height_cm}cm)，跳过处理")
                        skipped_count += 1
                        continue

                    # 使用高精度DPI将cm转换为px，确保转换后的整数像素值转回厘米不超过原始厘米值
                    try:
                        
                        # 使用整数转换，确保像素转回厘米不大于原始厘米值
                        target_width = PrecisionConverter.cm_to_px_precise(target_width_cm, default_dpi, return_float=False)
                        target_height = PrecisionConverter.cm_to_px_precise(target_height_cm, default_dpi, return_float=False)

                        # 验证转换精度（可选，用于调试）
                        if log_callback and processed_count < 3:  # 只对前3个图片显示精度验证
                            width_precise, _, _, width_error = PrecisionConverter.validate_conversion_precision(target_width_cm, default_dpi)
                            height_precise, _, _, height_error = PrecisionConverter.validate_conversion_precision(target_height_cm, default_dpi)
                            if not width_precise or not height_precise:
                                log_callback(f"        ⚠️ 转换精度警告: {image_name} 宽度误差{width_error:.4f}cm, 高度误差{height_error:.4f}cm")
                            else:
                                log_callback(f"        ✅ 高精度整数转换: {target_width_cm}x{target_height_cm}cm → {int(target_width)}x{int(target_height)}px")
                    except ImportError:
                        # 回退到传统方法，使用向下取整确保不超过原值
                        target_width = int(target_width_cm * default_dpi / 2.54)
                        target_height = int(target_height_cm * default_dpi / 2.54)
                        if log_callback and processed_count < 3:
                            log_callback(f"        ⚠️ 使用传统整数转换方法: {target_width_cm}x{target_height_cm}cm → {target_width}x{target_height}px")

                    # 验证转换后的像素尺寸
                    if target_width <= 0 or target_height <= 0:
                        if log_callback:
                            log_callback(f"        错误: {image_name} 转换后像素尺寸无效 ({target_width}x{target_height})，跳过处理")
                        skipped_count += 1
                        continue

                    # 复制并调整图片尺寸
                    result = ImageResizeProcessor._copy_and_resize_image(
                        image_path,
                        task_folder_path,
                        image_name,
                        target_width,
                        target_height,
                        quantity,
                        target_width_cm,
                        target_height_cm,
                        log_callback,
                        default_dpi
                    )

                    # 处理返回值（兼容新旧格式）
                    if len(result) == 3:
                        success, _, actual_saved_count = result
                    else:
                        success, _ = result
                        actual_saved_count = quantity if success else 0

                    if success and actual_saved_count > 0:
                        # 修复：统计实际生成的图片数量
                        processed_count += actual_saved_count
                        stats['processed'] += actual_saved_count
                        # 修复：按数量累加找到的图片数
                        found_count += quantity  # 这里仍然用quantity，因为这是期望的数量
                        stats['found'] += quantity

                        # 更新处理状态
                        if actual_saved_count == quantity:
                            df.at[index, '处理状态'] = '已处理'
                        else:
                            df.at[index, '处理状态'] = f'部分成功({actual_saved_count}/{quantity})'

                        # 显示处理进度（每处理10个图片或重要图片时显示）
                        if log_callback and (processed_count % 10 == 0 or processed_count <= 3):
                            elapsed = time.time() - start_time
                            speed = processed_count / elapsed if elapsed > 0 else 0
                            log_callback(f"        ⚡ 已处理 {processed_count}/{found_count} 个图片 (速度: {speed:.1f} 图/秒)")

                        # 如果部分失败，记录警告
                        if actual_saved_count < quantity:
                            partial_failed = quantity - actual_saved_count
                            if log_callback:
                                log_callback(f"        ⚠️ {image_name} 部分成功: 保存{actual_saved_count}个，失败{partial_failed}个")
                            stats['errors'] += partial_failed
                    else:
                        df.at[index, '处理状态'] = '处理失败'
                        if log_callback and error_count < 3:  # 只显示前3个错误
                            log_callback(f"        ❌ 图片处理失败: {image_name}")
                        skipped_count += 1
                        error_count += 1
                        stats['errors'] += quantity  # 全部失败

                except Exception as e:
                    df.at[index, '处理状态'] = f'异常:{str(e)[:20]}'
                    if log_callback and error_count < 3:  # 只显示前3个错误
                        log_callback(f"        ❌ 处理图片 {image_name if 'image_name' in locals() else '未知'} 时出错: {str(e)}")
                    skipped_count += 1
                    error_count += 1
                    stats['errors'] += 1
                    continue

            # 最终进度更新
            if progress_callback:
                progress_callback(total_rows, total_rows, 100)
                
            # 计算最终统计
            end_time = time.time()
            total_time = end_time - start_time
            stats['end_time'] = end_time
            stats['total_time'] = total_time
            
            if log_callback:
                log_callback(f"      📊 Sheet统计: 处理{processed_count}个，跳过{skipped_count}个，错误{error_count}个，用时{total_time:.1f}秒")

            return processed_count, found_count, stats

        except Exception as e:
            if log_callback:
                log_callback(f"      ❌ 处理sheet图片时出错: {str(e)}")
            return 0, 0, {'errors': 1}

    @staticmethod
    def _get_image_dpi(image_path):
        """获取图片的DPI值"""
        try:
            from PIL import Image
            with Image.open(image_path) as img:
                dpi = img.info.get('dpi', (72, 72))
                if isinstance(dpi, tuple):
                    return dpi[0]
                return dpi
        except Exception:
            return DEFAULT_DPI  # 默认DPI

    @staticmethod
    def _copy_and_resize_image(source_path, target_folder, image_name, width, height, quantity, width_cm=0, height_cm=0, log_callback=None, default_dpi=DEFAULT_DPI):
        """复制图片到目标文件夹并调整尺寸"""
        try:
            import time
            from PIL import Image

            start_time = time.time()

            # 检查源文件是否存在
            if not os.path.exists(source_path):
                if log_callback:
                    log_callback(f"        ❌ 源图片文件不存在: {os.path.basename(source_path)}")
                return False, 0

            # 检查目标文件夹是否存在
            if not os.path.exists(target_folder):
                if log_callback:
                    log_callback(f"        ❌ 目标文件夹不存在: {target_folder}")
                return False, 0

            # 预检查图片尺寸和文件大小
            try:
                file_size = os.path.getsize(source_path)
                file_size_mb = file_size / (1024 * 1024)

                # 对于大文件给出警告
                if file_size_mb > 100:  # 100MB以上
                    if log_callback:
                        log_callback(f"        ⚠️ 处理大文件: {os.path.basename(source_path)} ({file_size_mb:.1f}MB)")

                # 尝试快速获取图片基本信息
                with Image.open(source_path) as img_info:
                    original_width, original_height = img_info.size
                    total_pixels = original_width * original_height

                    # 对超大图片给出详细信息
                    if total_pixels > 100_000_000:  # 1亿像素以上
                        estimated_memory = (total_pixels * 3) / (1024 * 1024)  # RGB内存估算
                        if log_callback:
                            log_callback(f"        📊 超大图片: {original_width}x{original_height} ({total_pixels:,}像素, 估算内存:{estimated_memory:.1f}MB)")

            except Exception as size_check_error:
                if log_callback:
                    log_callback(f"        ⚠️ 无法预检查图片尺寸: {str(size_check_error)}")
                # 继续处理，不因预检查失败而中断

            # 获取文件扩展名
            _, ext = os.path.splitext(source_path)
            if not ext:
                ext = '.jpg'

            # 清理图片名称并添加尺寸信息
            clean_name = str(image_name).strip()

            # 生成包含尺寸信息的文件名：图案名称_宽_高
            if width_cm > 0 and height_cm > 0:
                # 将厘米尺寸转换为整数（去掉小数点）
                width_int = int(round(width_cm))
                height_int = int(round(height_cm))
                base_name = f"{clean_name}_{width_int}_{height_int}"
            else:
                base_name = clean_name

            # 获取源文件大小
            source_size = os.path.getsize(source_path)

            # 打开并调整图片尺寸 - 增强错误处理
            try:
                with Image.open(source_path) as img:
                    # 处理透明度：PNG等格式的透明部分转换为白色背景
                    if img.mode in ('RGBA', 'LA', 'P'):
                        # 创建白色背景
                        background = Image.new('RGB', img.size, (255, 255, 255))
                        if img.mode == 'P':
                            # 调色板模式先转换为RGBA
                            img = img.convert('RGBA')
                        # 将原图粘贴到白色背景上，透明部分变成白色
                        if img.mode in ('RGBA', 'LA'):
                            background.paste(img, mask=img.split()[-1])  # 使用Alpha通道作为遮罩
                        else:
                            background.paste(img)
                        img = background
                        if log_callback:
                            log_callback(f"        🎨 PNG透明度处理: 透明部分已转换为白色背景")
                    elif img.mode != 'RGB':
                        # 其他模式直接转换为RGB
                        img = img.convert('RGB')

                    # 获取原始图片尺寸
                    original_width, original_height = img.size
                    total_pixels = original_width * original_height

                    # 优化图片尺寸分配逻辑：大的数值设置图片实际边长大的边，小的数值设置另一边
                    target_width_cm = width_cm if width_cm > 0 else 0
                    target_height_cm = height_cm if height_cm > 0 else 0

                    # 如果提供了厘米尺寸，根据图片实际尺寸智能分配
                    if target_width_cm > 0 and target_height_cm > 0:
                        # 判断哪个是大边，哪个是小边
                        larger_cm = max(target_width_cm, target_height_cm)
                        smaller_cm = min(target_width_cm, target_height_cm)

                        # 根据图片实际尺寸分配：大的厘米值分配给图片实际的长边
                        if original_width >= original_height:
                            # 图片实际宽度更大，大的厘米值分配给宽度
                            final_width_cm = larger_cm
                            final_height_cm = smaller_cm
                        else:
                            # 图片实际高度更大，大的厘米值分配给高度
                            final_width_cm = smaller_cm
                            final_height_cm = larger_cm

                        # 重新计算像素尺寸
                        try:
                            from core.precision_converter import PrecisionConverter
                            width = PrecisionConverter.cm_to_px_precise(final_width_cm, default_dpi)
                            height = PrecisionConverter.cm_to_px_precise(final_height_cm, default_dpi)
                        except ImportError:
                            width = int(final_width_cm * default_dpi / 2.54)
                            height = int(final_height_cm * default_dpi / 2.54)

                        if log_callback:
                            log_callback(f"        🔄 智能尺寸分配: 原图{original_width}x{original_height} → 目标{final_width_cm}x{final_height_cm}cm ({int(width)}x{int(height)}px)")

                    # 对超大图片进行特殊处理
                    if total_pixels > 200_000_000:  # 2亿像素以上
                        if log_callback:
                            log_callback(f"        🔄 处理超大图片: {original_width}x{original_height} ({total_pixels:,}像素)")

                        # 对于超大图片，可以考虑先降采样再调整尺寸
                        if original_width > width * 2 and original_height > height * 2:
                            # 先降采样到目标尺寸的2倍，减少内存使用
                            intermediate_width = int(round(width * 2))
                            intermediate_height = int(round(height * 2))
                            if log_callback:
                                log_callback(f"        📉 先降采样到: {intermediate_width}x{intermediate_height}")
                            img = img.resize((intermediate_width, intermediate_height), Image.Resampling.LANCZOS)

                    # 强制拉伸到目标尺寸，不保持宽高比
                    # PIL需要整数像素值，传入的width和height已经是安全的整数值
                    target_width_px = int(width)
                    target_height_px = int(height)

                    resize_start = time.time()
                    resized_img = img.resize((target_width_px, target_height_px), Image.Resampling.LANCZOS)
                    resize_time = time.time() - resize_start

                    total_saved_size = 0
                    saved_files = []

                    # 根据数量复制图片
                    successful_saves = 0
                    failed_saves = 0

                    for i in range(quantity):
                        # 生成目标文件名
                        if quantity == 1:
                            target_name = f"{base_name}{ext}"
                        else:
                            target_name = f"{base_name}_{i+1:03d}{ext}"

                        target_path = os.path.join(target_folder, target_name)

                        # 检查文件是否已存在，如果存在则生成新的文件名
                        if os.path.exists(target_path):
                            counter = 1
                            name_without_ext = os.path.splitext(target_name)[0]
                            while os.path.exists(target_path):
                                new_target_name = f"{name_without_ext}_副本{counter}{ext}"
                                target_path = os.path.join(target_folder, new_target_name)
                                counter += 1
                            target_name = new_target_name
                            if log_callback:
                                log_callback(f"        ⚠️ 检测到同名文件，重命名为: {target_name}")

                        try:
                            # 保存调整后的图片，设置DPI
                            save_start = time.time()
                            resized_img.save(target_path, quality=95, optimize=True, dpi=(default_dpi, default_dpi))
                            save_time = time.time() - save_start

                            # 验证文件是否成功保存
                            if os.path.exists(target_path):
                                file_size = os.path.getsize(target_path)
                                total_saved_size += file_size
                                saved_files.append(target_name)
                                successful_saves += 1

                                # 使用统一的文件大小格式化
                                try:
                                    from utils.image_helper import format_file_size_mb
                                    file_size_mb = format_file_size_mb(file_size)
                                except ImportError:
                                    file_size_mb = f"{file_size / (1024*1024):.2f}MB"

                                if i == 0 and log_callback:  # 只在第一份时显示详细信息
                                    compression_ratio = (1 - file_size / source_size) * 100 if source_size > 0 else 0
                                    log_callback(f"        ✅ 已保存图片: {target_name}")
                                    log_callback(f"          📏 尺寸变化: {original_width}x{original_height} → {target_width_px}x{target_height_px} (从{width:.1f}x{height:.1f}px)")
                                    log_callback(f"          💾 文件大小: {file_size_mb} (压缩率: {compression_ratio:.1f}%)")
                                    log_callback(f"          ⚡ 处理耗时: 调整{resize_time*1000:.1f}ms + 保存{save_time*1000:.1f}ms")
                                elif log_callback and quantity <= 3:  # 数量少时显示每个文件
                                    log_callback(f"        ✅ 已保存图片: {target_name} ({file_size_mb})")
                            else:
                                failed_saves += 1
                                if log_callback:
                                    log_callback(f"        ❌ 保存失败: {target_name} (文件未创建)")
                        except Exception as save_error:
                            failed_saves += 1
                            if log_callback:
                                log_callback(f"        ❌ 保存失败: {target_name} (错误: {str(save_error)})")

            except Image.DecompressionBombError as bomb_error:
                if log_callback:
                    log_callback(f"        ❌ 图片尺寸过大，无法处理: {str(bomb_error)}")
                return False, 0
            except MemoryError as mem_error:
                if log_callback:
                    log_callback(f"        ❌ 内存不足，无法处理图片: {str(mem_error)}")
                return False, 0
            except Exception as img_error:
                if log_callback:
                    log_callback(f"        ❌ 图片处理失败: {str(img_error)}")
                return False, 0

            # 计算总处理时间
            total_time = time.time() - start_time

            # 显示批量处理摘要
            if log_callback:
                try:
                    from utils.image_helper import format_file_size_mb
                    total_size_mb = format_file_size_mb(total_saved_size)
                except ImportError:
                    total_size_mb = f"{total_saved_size / (1024*1024):.2f}MB"

                if quantity > 1:
                    if failed_saves > 0:
                        log_callback(f"        📦 批量处理完成: 成功{successful_saves}个，失败{failed_saves}个，总大小{total_size_mb}，用时{total_time*1000:.1f}ms")
                    else:
                        log_callback(f"        📦 批量处理完成: {successful_saves}个文件，总大小{total_size_mb}，用时{total_time*1000:.1f}ms")

            # 返回成功状态和实际保存的数量
            if successful_saves > 0:
                return True, total_time, successful_saves  # 有成功保存的图片
            else:
                return False, 0, 0  # 没有成功保存任何图片

        except Exception as e:
            if log_callback:
                log_callback(f"        ❌ 复制和调整图片尺寸时出错: {str(e)}")
            return False, 0, 0

# ---------------------------
# 工具类
# ---------------------------
class Utils:
    """工具类，提供各种实用功能方法"""
    
    @staticmethod
    def is_valid_tracking_number(tracking_number: Optional[str]) -> bool:
        """
        验证快递单号是否符合预期格式
        
        Args:
            tracking_number: 待验证的快递单号
            
        Returns:
            bool: 是否为有效的快递单号
        """
        if not tracking_number or not isinstance(tracking_number, str):
            return False

        # 去除空格和其他非法字符
        tracking_number = tracking_number.strip()

        # 检查是否为顺丰单号（以SF开头）
        if tracking_number.upper().startswith('SF') and len(tracking_number) >= 12:
            return True

        # 检查是否为纯数字单号（长度在10-20位之间）
        if tracking_number.isdigit() and 10 <= len(tracking_number) <= 20:
            return True

        return False

    @staticmethod
    def get_excel_path(txt_path: str) -> str:
        """
        根据txt文件路径生成对应的Excel文件路径
        
        Args:
            txt_path: TXT文件路径
            
        Returns:
            str: 对应的Excel文件路径
        """
        base_name = os.path.splitext(txt_path)[0]
        return f"{base_name}_总操作表格.xlsx"

    @staticmethod
    def fetch_texture_categories() -> Dict[str, Dict[str, Any]]:
        """
        从Supabase获取素材分类数据，包括基础素材和子素材映射关系
        如果无法获取，返回默认的素材分类数据
        
        Returns:
            Dict[str, Dict[str, Any]]: 素材分类数据字典
        """
        try:
            log.info("使用默认素材分类数据")
            # 返回默认的素材分类数据
            default_categories = {
                "棉质": {
                    "sub_textures": ["纯棉", "棉麻", "棉丝", "棉毛", "有机棉", "精梳棉"],
                    "description": "棉质面料"
                },
                "丝质": {
                    "sub_textures": ["真丝", "桑蚕丝", "柞蚕丝", "人造丝", "丝绸"],
                    "description": "丝质面料"
                },
                "毛质": {
                    "sub_textures": ["羊毛", "羊绒", "马海毛", "兔毛", "驼毛"],
                    "description": "毛质面料"
                },
                "化纤": {
                    "sub_textures": ["涤纶", "尼龙", "氨纶", "聚酯纤维", "弹性纤维"],
                    "description": "化学纤维面料"
                }
            }
            return default_categories
        except Exception as e:
            log.warning(f"获取素材分类数据时出错: {str(e)}，使用默认数据")
            return {
                "通用": {
                    "sub_textures": ["棉", "丝", "毛", "麻"],
                    "description": "通用面料"
                }
            }

    @staticmethod
    def fetch_remove_word_arr() -> List[str]:
        """
        从Supabase获取需要移除的词语数组
        如果无法获取，返回默认的移除词列表
        
        Returns:
            List[str]: 需要移除的词语列表
        """
        try:
            log.info("使用默认移除词列表")
            # 返回常用的需要移除的词语
            default_remove_words = [
                "定制", "客服", "备注", "梦", "特殊", "加急", "样品", "测试",
                "临时", "修改", "补单", "重做", "返工", "加工", "处理"
            ]
            return default_remove_words
        except Exception as e:
            log.warning(f"获取移除词数组时出错: {str(e)}，使用默认列表")
            return ["定制", "客服", "备注"]  # 最基本的移除词

    @staticmethod
    def fetch_material_keywords() -> List[str]:
        """
        从Supabase获取素材关键词列表
        如果无法获取，返回默认的材料关键字列表
        
        Returns:
            List[str]: 材料关键字列表
        """
        try:
            # 尝试从Supabase获取（需要传入supabase_helper参数，暂时使用默认值）
            log.info("使用默认材料关键字列表")
            # 返回常用的材料关键字
            default_keywords = [
                "印花2m", "DLDM2M", "圈绒棉布2m", "波斯绒",
                "棉", "麻", "丝", "毛", "涤纶", "尼龙", "氨纶", "粘胶", "莫代尔", "天丝",
                "亚麻", "真丝", "羊毛", "羊绒", "兔毛", "马海毛", "人造丝", "聚酯纤维",
                "弹性纤维", "醋酸纤维", "铜氨纤维", "竹纤维", "大豆纤维", "玉米纤维",
                "牛奶纤维", "珍珠纤维", "银纤维", "负离子纤维", "远红外纤维"
            ]
            return default_keywords
        except Exception as e:
            log.warning(f"获取材料关键字时出错: {str(e)}，使用默认列表")
            return ["印花2m", "DLDM2M", "圈绒棉布2m", "波斯绒"]  # 最基本的材料关键字

    @staticmethod
    def fetch_robot_config_width_arr(supabase_helper: Optional[Any] = None) -> Optional[List[int]]:
        """
        从kufang_robot_config表获取全局width_arr数据
        使用认证客户端以支持RLS策略
        参照robot_bot_0.0.12.py的逻辑实现
        
        Args:
            supabase_helper: Supabase帮助器实例
            
        Returns:
            Optional[List[int]]: 宽度数组，获取失败时返回None
        """
        try:
            # 如果没有传入supabase_helper，尝试获取全局实例
            if not supabase_helper:
                # 尝试从全局变量获取
                if 'main_window_instance' in globals() and main_window_instance:
                    supabase_helper = getattr(main_window_instance, 'supabase_helper', None)

            if not supabase_helper or not supabase_helper.is_connected():
                log.warning("Supabase连接不可用，使用默认width_arr配置")
                return None

            # 获取认证客户端
            auth_client = supabase_helper.get_authenticated_client()
            if not auth_client:
                log.warning("无法获取认证客户端，使用默认width_arr配置")
                return None

            # 使用认证客户端查询数据
            response = auth_client.table('kufang_robot_config').select('width_arr').execute()
            if not response.data:
                log.error("无法从kufang_robot_config获取width_arr配置。")
                return None

            width_arr_str = response.data[0].get('width_arr', '')
            if not width_arr_str:
                log.error("kufang_robot_config的width_arr字段为空。")
                return None

            width_arr = [int(w.strip()) for w in width_arr_str.split(',') if w.strip().isdigit()]
            width_arr = sorted(width_arr)
            if not width_arr:
                log.error("kufang_robot_config的width_arr字段格式不正确。")
                return None

            log.info(f"成功从kufang_robot_config获取width_arr: {width_arr}")
            return width_arr
        except Exception as e:
            log.error(f"从kufang_robot_config获取width_arr时发生错误: {e}")
            return None

    @staticmethod
    def fetch_texture_categories_with_width(supabase_helper: Optional[Any] = None) -> Dict[str, Dict[str, Any]]:
        """
        从kufang_texture_category表获取base_texture和width_arr数据
        使用认证客户端以支持RLS策略
        参照robot_bot_0.0.12.py的逻辑实现

        Args:
            supabase_helper: Supabase帮助器实例
            
        Returns:
            Dict[str, Dict[str, Any]]: 材质分类数据字典，格式为:
            {
                'base_texture1': {
                    'sub_textures': [...],
                    'width_arr': [160, 180, 200, 240, 300] 或 None
                },
                'base_texture2': {
                    'sub_textures': [...],
                    'width_arr': None
                },
                ...
            }
        """
        try:
            # 如果没有传入supabase_helper，尝试获取全局实例
            if not supabase_helper:
                if 'main_window_instance' in globals() and main_window_instance:
                    supabase_helper = getattr(main_window_instance, 'supabase_helper', None)

            if not supabase_helper or not supabase_helper.is_connected():
                log.warning("Supabase连接不可用，使用默认材质配置")
                return {}

            # 获取认证客户端
            auth_client = supabase_helper.get_authenticated_client()
            if not auth_client:
                log.warning("无法获取认证客户端，使用默认材质配置")
                return {}

            # 使用认证客户端查询数据
            response = auth_client.table('kufang_texture_category').select('base_texture, texture_arr, width_arr').execute()
            if not response.data:
                log.error("无法从kufang_texture_category获取数据。")
                return {}

            texture_dict = {}
            for item in response.data:
                base_texture = item.get('base_texture', '').strip()
                texture_arr = item.get('texture_arr', '')
                sub_textures = [tex.strip() for tex in texture_arr.split(',') if tex.strip()]
                width_arr_str = item.get('width_arr')
                if width_arr_str:
                    width_arr = [int(w.strip()) for w in width_arr_str.split(',') if w.strip().isdigit()]
                    width_arr = sorted(width_arr)
                else:
                    width_arr = None
                if base_texture:
                    texture_dict[base_texture] = {
                        'sub_textures': sub_textures,
                        'width_arr': width_arr
                    }

            log.info(f"成功从kufang_texture_category获取数据: {texture_dict}")
            return texture_dict
        except Exception as e:
            log.error(f"从kufang_texture_category获取数据时发生错误: {e}")
            return {}

    @staticmethod
    def apply_yellow_background_for_missing_images(excel_path: str) -> None:
        """
        为Excel文件中'未入库'的数据行设置黄色背景

        Args:
            excel_path: Excel文件路径
        """
        try:
            from openpyxl import load_workbook

            # 加载工作簿
            workbook = load_workbook(excel_path)

            # 定义黄色背景填充
            yellow_fill = PatternFill(start_color='FFFF99', end_color='FFFF99', fill_type='solid')

            # 遍历所有工作表
            for sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]

                # 跳过空工作表
                if worksheet.max_row <= 1:
                    continue

                # 特殊处理：如果是'未入库'或'未入库图片'sheet，设置所有数据行为黄色背景
                if sheet_name in ['未入库', '未入库图片']:
                    for row_idx in range(2, worksheet.max_row + 1):  # 从第二行开始（跳过标题行）
                        for col_idx in range(1, worksheet.max_column + 1):
                            worksheet.cell(row=row_idx, column=col_idx).fill = yellow_fill
                    continue

                # 查找'图片路径'列的索引
                image_path_col_index = None
                if worksheet.max_row > 0:
                    for col_idx, cell in enumerate(worksheet[1], 1):  # 第一行是标题行
                        if cell.value == '图片路径':
                            image_path_col_index = col_idx
                            break

                # 如果找到'图片路径'列，检查每一行
                if image_path_col_index:
                    for row_idx in range(2, worksheet.max_row + 1):  # 从第二行开始（跳过标题行）
                        cell_value = worksheet.cell(row=row_idx, column=image_path_col_index).value

                        # 如果图片路径为'未入库'，设置整行黄色背景
                        if cell_value == '未入库':
                            for col_idx in range(1, worksheet.max_column + 1):
                                worksheet.cell(row=row_idx, column=col_idx).fill = yellow_fill

            # 保存工作簿
            workbook.save(excel_path)
            log.info(f"已为Excel文件 {excel_path} 中的未入库数据设置黄色背景")

        except Exception as e:
            log.error(f"设置未入库数据黄色背景时出错: {str(e)}")

# ---------------------------
# 解析器模块（从robot_bot_0.0.12.py移植）
# ---------------------------
class LineParser:
    """文本行解析器，支持多种格式的文本解析和AI辅助解析"""
    
    def __init__(self, material_keywords: List[str], remove_words: List[str],
                 openai_client_manager: Optional[Any] = None, no_image_keywords: Optional[List[str]] = None):
        """
        初始化文本行解析器
        
        Args:
            material_keywords: 材质关键词列表
            remove_words: 需要移除的词语列表
            openai_client_manager: OpenAI客户端管理器实例
            no_image_keywords: 无图识别关键词列表
        """
        self._lock = threading.Lock()  # 用于保护 OpenAI 请求的线程安全
        
        # 编译各种正则表达式模式
        self.s_persian_pattern = re.compile(
            r'^S波斯绒-([A-Za-z0-9\u4e00-\u9fa5\-]+)[；;]?$'
        )
        self.diameter_pattern = re.compile(
            r'^([A-Za-z0-9\u4e00-\u9fa5]+)-([A-Za-z0-9\u4e00-\u9fa5\-]+)-直径(\d+)(?:cm)?\s+(\d+)$'
        )
        self.leading_symbols_pattern = re.compile(r'^[,;，。；#]+')
        # 匹配分隔符（半角 '-'、半角 '~' 和全角 '～'）
        self.separator_pattern = re.compile(r'[-～~]')

        # 材质名称相关
        self.material_case_mapping: Dict[str, str] = {}
        for keyword in material_keywords:
            self.material_case_mapping[keyword.lower()] = keyword

        self.material_separators = r'[-丨]'
        material_regex = '|'.join(
            f"{re.escape(keyword)}(?:{self.material_separators})"
            for keyword in material_keywords
        )
        self.material_patterns = re.compile(
            rf'^(?:{material_regex})',
            re.IGNORECASE
        )
        
        # remove_word处理
        if remove_words:
            self.remove_words = sorted(remove_words, key=len, reverse=True)  # 按长度降序排序，确保优先匹配较长的词
            escaped_remove_words = [re.escape(word) for word in self.remove_words]
            self.remove_word_pattern = re.compile(
                '|'.join(escaped_remove_words),
                re.IGNORECASE
            )
        else:
            self.remove_word_pattern = None

        self.material_keywords = material_keywords

        self.openai_mgr = openai_client_manager  # 可传入 OpenAIClientManager 实例
        self.openai_retries = 5  # 重试次数
        self.openai_timeout = 20  # 请求超时（秒）
        self.openai_cache: Dict[str, List[Dict[str, Any]]] = {}  # 缓存OpenAI解析结果

        # 新增扩展直径格式：支持在 "直径数字+(可选cm)" 与数量之间包含任意描述文本（例如 "原创设计"）
        self.circle_extended_pattern = re.compile(
            r'^([A-Za-z0-9\u4e00-\u9fa5]+)-([A-Za-z0-9\u4e00-\u9fa5\-]+)-直径(\d+)(?:cm)?(?:\s+\S+)*\s+(\d+)$'
        )

        # 新增：支持"直径-数字"格式的直径模式
        self.diameter_with_dash_pattern = re.compile(
            r'^([A-Za-z0-9\u4e00-\u9fa5]+)-([A-Za-z0-9\u4e00-\u9fa5\-]+)-直径-(\d+)(?:cm)?\s+(\d+)$'
        )

        # 通用格式，处理 "材质-图案-宽-高 数量"
        self.general_pattern = re.compile(
            r'^([^-]+)-(.+?)-(\d+)-(\d+)\s+(\d+)$'
        )
        # 圆形格式，处理 "材质-图案-直径圆 数量"
        self.circle_pattern = re.compile(
            r'^([^-]+)-(.+?)-(\d+)圆\s+(\d+)$'
        )
        # 椭圆格式，处理 "材质-图案-椭圆-宽-高 数量"
        self.oval_pattern = re.compile(
            r'^([^-]+)-(.+?)-椭圆-(\d+)-(\d+)\s+(\d+)$'
        )
        
        # 无图识别关键词
        self.no_image_keywords = no_image_keywords or []

    def normalize_material_name(self, material):
        """标准化材质名称"""
        if not material:
            return material
        material_lower = material.lower()
        if material_lower in self.material_case_mapping:
            return self.material_case_mapping[material_lower]
        parts = material.split('-')
        normalized_parts = []
        for part in parts:
            part_lower = part.lower()
            if part_lower in self.material_case_mapping:
                normalized_parts.append(self.material_case_mapping[part_lower])
            else:
                normalized_parts.append(part)
        return '-'.join(normalized_parts)

    def clean_pattern(self, pattern, material_name=None, raw_data=None):
        """清理图案名称
        
        Args:
            pattern: 图案名称
            material_name: 材质名称，用于清洗图案名称中包含的材质内容
            raw_data: 原始数据，用于检测形状类型
        """
        if not pattern:
            return pattern
        original_pattern = pattern
        pattern = pattern.strip()
        pattern = pattern.replace('·', '')
        pattern = re.sub(r'^[^\w\u4e00-\u9fa5]+', '', pattern).strip()

        material_match = self.material_patterns.search(pattern)
        if material_match:
            matched_part = material_match.group(0)
            pattern = pattern[len(matched_part):].strip()

        pattern = re.sub(r'^丨+', '', pattern).strip()
        pattern = re.sub(r'^(颜色分类?:[^-]+[-])', '', pattern)
        pattern = re.sub(r'^(颜色[:：])', '', pattern)
        pattern = re.sub(r'^颜色分类?[:：][^-]+[-]', '', pattern)
        pattern = re.sub(r'^颜色[:：][^-]+[-]', '', pattern)
        pattern = re.sub(r'^颜色分类?[:：][^;；]+[;；]', '', pattern)
        pattern = self.leading_symbols_pattern.sub('', pattern).strip()
        pattern = pattern.split(',')[0].strip()
        pattern = pattern.split('，')[0].strip()
        match = re.match(r'^([\u4e00-\u9fa5A-Za-z]+)[-\s]*([\dA-Za-z]+)?$', pattern)
        if match:
            main_pattern = match.group(1)
            suffix = match.group(2)
            if suffix:
                pattern = f"{main_pattern}{suffix}"
            else:
                pattern = main_pattern
        else:
            pattern = re.sub(r'[-\s]+', '', pattern)
        pattern = pattern.replace(' ', '')

        # 新增：清洗图案名称中包含的材质内容
        if material_name and pattern:
            original_before_material_clean = pattern
            # 清洗材质关键词中的数字和特殊字符，获取纯材质名称
            clean_material = self._extract_clean_material_name(material_name)
            if clean_material and clean_material in pattern:
                # 移除图案名称中包含的材质名称
                pattern = pattern.replace(clean_material, '').strip()
                # 清理可能残留的分隔符
                pattern = re.sub(r'^[-丨]+|[-丨]+$', '', pattern).strip()
                if pattern != original_before_material_clean:
                    log.debug(f"材质清洗: '{original_before_material_clean}' -> '{pattern}' (移除材质: '{clean_material}')")

        # 使用 remove_word_pattern 清洗图案名称
        if self.remove_word_pattern:
            original_after_basic = pattern
            prev_pattern = None
            while prev_pattern != pattern:
                prev_pattern = pattern
                pattern = self.remove_word_pattern.sub('', pattern)
                pattern = pattern.strip()
            pattern = re.sub(r'^[-丨]+|[-丨]+$', '', pattern).strip()
            if pattern != original_after_basic:
                log.debug(f"Remove words 清理: '{original_after_basic}' -> '{pattern}'")

        # 新增：形状类型检测和自动补充
        pattern = self._ensure_shape_type_in_pattern(pattern, raw_data)

        if not pattern:
            log.debug(f"清洗后图案名称为空，原始图案名称: '{original_pattern}'")
            return ''
        log.debug(f"清洗完成: '{original_pattern}' -> '{pattern}'")
        return pattern

    def _ensure_shape_type_in_pattern(self, pattern, raw_data=None):
        """确保图案名称包含形状类型
        
        当原始数据中包含形状关键词时，检查图案名称是否包含对应的形状类型，
        如果没有则自动添加。
        
        Args:
            pattern: 清理后的图案名称
            raw_data: 原始数据文本
            
        Returns:
            str: 包含形状类型的图案名称
        """
        if not pattern or not raw_data:
            return pattern
            
        # 定义形状关键词映射
        shape_keywords = {
            '圆形': ['圆形'],
            '半圆': ['半圆'],
            '异形': ['异形'],
            '方形': ['方形', '正方形']
        }
        
        # 检查原始数据中是否包含形状关键词
        detected_shape = None
        for shape_type, keywords in shape_keywords.items():
            for keyword in keywords:
                if keyword in raw_data:
                    detected_shape = shape_type
                    break
            if detected_shape:
                break
        
        # 如果检测到形状类型，检查图案名称是否已包含
        if detected_shape:
            # 检查图案名称是否已经包含任何形状类型
            has_shape = False
            for shape_type in shape_keywords.keys():
                if shape_type in pattern:
                    has_shape = True
                    break
            
            # 如果图案名称中没有形状类型，则添加检测到的形状类型
            if not has_shape:
                original_pattern = pattern
                pattern = f"{pattern}{detected_shape}"
                log.debug(f"形状类型补充: '{original_pattern}' -> '{pattern}' (添加形状: '{detected_shape}')")
        
        return pattern

    def _extract_clean_material_name(self, material_name):
        """提取纯净的材质名称，去除数字和特殊字符
        
        Args:
            material_name: 原始材质名称，如 '小圈硅藻丝2m'、'印花2m' 等
            
        Returns:
            str: 纯净的材质名称，如 '小圈硅藻丝'、'印花' 等
        """
        if not material_name:
            return ''
        
        # 移除常见的材质后缀（数字+单位）
        clean_material = re.sub(r'\d+[mM]?$', '', material_name).strip()
        # 移除其他数字
        clean_material = re.sub(r'\d+', '', clean_material).strip()
        # 移除常见的分隔符和特殊字符
        clean_material = re.sub(r'[-_丨]+', '', clean_material).strip()
        
        return clean_material

    def is_result_anomalous(self, entry):
        """
        检查规则匹配结果是否异常：
          如果数量<=0或图案字段全部为数字，则认为异常。
        """
        try:
            quantity = int(entry.get('数量', 1))
            if quantity <= 0:
                return True
        except Exception:
            return True
        pattern = entry.get('图案', '').strip()
        if pattern.isdigit():
            return True
        # 检查整个图案字符串中是否存在汉字
        if re.search("[\u4e00-\u9fa5]", pattern) is None:
            return True
        return False

    def extract_tracking_number(self, text, openai_data=None):
        """
        从文本中提取快递单号。
        支持两种解析方式：
        1. 正则匹配：
           - SF开头的顺丰单号，例如：SF3156390969274
           - 纯数字的单号，长度为10-20位，例如：78885397777199
        2. OpenAI解析：
           - 如果提供了openai_data且包含express_num字段，优先使用该字段作为快递单号

        参数:
            text: 原始文本内容
            openai_data: OpenAI返回的解析结果字典（可选）

        返回值:
            tuple: (是否识别到单号, 单号字符串, 数量)
        """
        # 首先尝试从OpenAI解析结果中获取快递单号
        if openai_data and isinstance(openai_data, dict):
            express_num = openai_data.get('express_num', '').strip()
            if express_num and Utils.is_valid_tracking_number(express_num):
                # 如果OpenAI结果中有数量字段，使用它
                count = openai_data.get('count', None)
                if count is not None:
                    try:
                        count = int(count)
                    except (ValueError, TypeError):
                        count = None
                return True, express_num, count

        # 如果没有足够的内容或OpenAI解析失败，使用正则匹配
        if not text or len(text) < 5:
            return False, "无", None

        # 分离文本中间的空格，得到可能的单词序列
        parts = re.split(r'\s+', text.strip())

        # 如果只有少于2个部分，可能没有足够信息
        if len(parts) < 2:
            return False, "无", None

        # 检查最后一个部分是否可能是快递单号
        last_part = parts[-1].strip()
        second_last_part = parts[-2].strip() if len(parts) >= 2 else ""

        # 使用通用验证方法检查是否为有效的快递单号
        is_valid = Utils.is_valid_tracking_number(last_part)

        quantity = None
        # 如果倒数第二个部分是1-3位数字，可能是数量
        if second_last_part and re.match(r'^\d{1,3}$', second_last_part):
            try:
                quantity = int(second_last_part)
            except ValueError:
                quantity = None

        # 如果是有效的快递单号，返回
        if is_valid:
            return True, last_part, quantity

        return False, "无", None

    def _build_ai_parsed_entry(self, item: Dict[str, Any], raw_line: str, product: str,
                              pattern: str, width: int, length: int, count: int) -> Dict[str, Any]:
        """
        根据 OpenAI 返回的原始数据和转换规则，生成标准的解析结果
        
        Args:
            item: OpenAI返回的原始数据项
            raw_line: 原始文本行
            product: 产品/材质名称
            pattern: 图案名称
            width: 宽度值
            length: 长度值
            count: 数量
            
        Returns:
            Dict[str, Any]: 标准化的解析结果字典
        """
        if count <= 0:
            count = 1
        product_normalized = self.normalize_material_name(product)
        pattern_cleaned = self.clean_pattern(pattern, product_normalized, raw_line)

        # 优化：确保宽度 > 高度，数值大的为宽，数值小的为高
        if width > 0 and length > 0 and width < length:
            width, length = length, width
            log.debug(f"自动纠正尺寸: 宽度({width}) < 高度({length})，已交换")

        # 提取可能的快递单号，现在传入OpenAI解析的item
        _, tracking_number, tracking_count = self.extract_tracking_number(raw_line, item)

        # 如果从文本中检测到数量，则覆盖AI解析的数量
        if tracking_count is not None:
            count = tracking_count

        # 修改：圆形图案全称不再包含直径信息
        if '直径' in raw_line:
            pattern_full_name = f"{pattern_cleaned}圆形"
        else:
            orientation = '竖版' if '竖' in raw_line else '横版'
            pattern_full_name = f"{pattern_cleaned}-{width}-{length}-{orientation}"

        entry = {
            '材质': product_normalized,
            '图案': pattern_cleaned,
            '宽cm': width,
            '高cm': length,  # 修改：长cm改为高cm
            '图案全称': pattern_full_name,
            '数量': count,
            '原始数据': raw_line,
            '处理方式': 'AI解析',
            '快递单号': tracking_number
        }
        return entry

    def parse_with_openai(self, raw_line: str, line_number: Optional[int] = None,
                         file_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        使用OpenAI解析未匹配正则或规则匹配异常的数据，同步等待结果
        
        新逻辑采用长期记忆方式与OpenAI接口交互，保证在并发环境下不混乱。
        当OpenAI解析数据错误或异常，或解析成功但宽、长均为空时，
        判定为解析失败，并返回包含详细错误信息的记录。

        优化内容：
        1. 增强错误恢复能力，更智能地处理不同类型的API错误
        2. 改进重试策略，针对不同错误类型采用不同的等待时间
        3. 增强JSON解析的健壮性，处理更多边缘情况
        4. 缓存机制优化，避免重复请求相同内容
        
        Args:
            raw_line: 原始文本行
            line_number: 行号（可选）
            file_name: 文件名（可选）
            
        Returns:
            List[Dict[str, Any]]: 解析结果列表，每个元素包含解析后的数据或错误信息
        """
        # 检查缓存，避免重复请求
        if raw_line in self.openai_cache:
            log.info(f"使用缓存的OpenAI解析结果: {raw_line[:30]}...")
            return self.openai_cache[raw_line]

        config = self.openai_mgr.get_client_config() if self.openai_mgr else {}
        if not config:
            error_entry = {
                '原始数据': raw_line,
                '行号': line_number,
                '错误原因': 'OpenAI配置不可用',
                '错误日志': 'OpenAI客户端未初始化或配置缺失',
                '数据处理流程': 'AI解析',
                '解析错误': 'OpenAI配置不可用'
            }
            self.openai_cache[raw_line] = [error_entry]
            return [error_entry]

        # 辅助函数，用于处理OpenAI接口返回的内容，保证代码复用
        def process_response_content(response):
            try:
                content = response.choices[0].message.content
                # 移除可能的代码块标记
                content = content.replace('```json', '').replace('```', '').strip()

                # 处理可能的XML/HTML标记包装
                for tag_pair in [('<r>', '</r>'), ('<json>', '</json>'), ('<r>', '</r>')]:
                    if tag_pair[0] in content and tag_pair[1] in content:
                        result_match = re.search(f'{tag_pair[0]}\\s*([\\[\\{{].*?[\\]\\}}])\\s*{tag_pair[1]}', content, re.DOTALL)
                        if result_match:
                            content = result_match.group(1).strip()
                            break

                # 修复JSON字符串中的转义问题
                def escape_json_string(match):
                    inner = match.group(1)
                    # 处理控制字符和特殊字符，使用JSON兼容的转义
                    def escape_char(m):
                        char = m.group(0)
                        # 使用JSON标准的转义序列
                        if char == '\n':
                            return '\\n'
                        elif char == '\r':
                            return '\\r'
                        elif char == '\t':
                            return '\\t'
                        elif char == '\b':
                            return '\\b'
                        elif char == '\f':
                            return '\\f'
                        elif char == '"':
                            return '\\"'
                        elif char == '\\':
                            return '\\\\'
                        elif char == '/':
                            return '\\/'
                        else:
                            # 对于其他控制字符，使用Unicode转义
                            return f'\\u{ord(char):04x}'

                    fixed = re.sub(r'[\x00-\x1F\x7F-\x9F"\\]', escape_char, inner)
                    return f"\"{fixed}\""

                # 应用JSON字符串修复
                content = re.sub(r'"((?:[^"\\]|\\.)*?)"', escape_json_string, content)

                # 处理可能的尾部逗号问题（非标准JSON）
                content = re.sub(r',\s*([}\]])', r'\1', content)

                log.debug(f"清理后的OpenAI JSON内容: {content}")
                self.openai_mgr.status_update.emit(f"OpenAI解析成功，返回数据: {content}")

                try:
                    result_json = json.loads(content)
                    if not isinstance(result_json, list):
                        result_json = [result_json]
                except json.JSONDecodeError as je:
                    # 尝试更激进的JSON修复
                    log.warning(f"标准JSON解析失败: {je}, 尝试修复...")
                    try:
                        # 移除所有注释
                        content = re.sub(r'//.*?$|/\*.*?\*/', '', content, flags=re.MULTILINE|re.DOTALL)
                        # 确保属性名有引号
                        content = re.sub(r'([{,])\s*([a-zA-Z0-9_]+)\s*:', r'\1"\2":', content)
                        result_json = json.loads(content)
                        if not isinstance(result_json, list):
                            result_json = [result_json]
                        log.info("JSON修复成功")
                    except Exception as fix_error:
                        log.error(f"JSON修复失败: {fix_error}")
                        raise ValueError(f"OpenAI返回的JSON格式无效且无法修复: {je}")

                parsed_results = []
                for item in result_json:
                    try:
                        # 优先从product字段获取材质，如果没有则从raw_data中提取
                        product = item.get('product', '').strip()
                        pattern = item.get('pattern', '').strip()

                        # 如果没有product字段，尝试从raw_data中提取材质
                        if not product:
                            raw_data = item.get('raw_data', '').strip()
                            if raw_data and '-' in raw_data:
                                # 从raw_data中提取第一部分作为材质
                                parts = raw_data.split('-')
                                if len(parts) > 0:
                                    product = parts[0].strip()
                                    log.debug(f"从raw_data中提取材质: {product}")

                        # 如果还是没有材质，使用默认值
                        if not product:
                            product = "未知材质"
                            log.warning(f"无法提取材质信息，使用默认值: {product}")

                        try:
                            # 更健壮的数值转换，支持height字段
                            width = item.get('width', 0)
                            width = int(float(width)) if width else 0

                            # 优先使用height字段，如果没有则使用length字段
                            length = item.get('height', item.get('length', 0))
                            length = int(float(length)) if length else 0

                            count = item.get('count', 1)
                            count = int(count) if count else 1
                            if count <= 0:
                                count = 1
                        except (ValueError, TypeError) as e:
                            log.warning(f"数值转换错误: {e}, 使用默认值")
                            width, length, count = 0, 0, 1

                        # 修改验证逻辑：只要有图案就可以，材质可以从其他地方提取
                        if not pattern:
                            raise ValueError("缺少必要字段: 图案")

                        if width == 0 and length == 0:
                            raise ValueError("OpenAI返回的JSON中高、宽均为空值")

                        # 优化：确保宽度 > 高度，数值大的为宽，数值小的为高
                        if width > 0 and length > 0 and width < length:
                            width, length = length, width
                            log.debug(f"自动纠正尺寸: 宽度({width}) < 高度({length})，已交换")

                        # 保持原有快递单号处理逻辑
                        express_num = item.get('express_num', '').strip()
                        if express_num:
                            # 标准化快递单号格式
                            # 1. 移除可能的前缀如"单号:"
                            express_num = re.sub(r'^(快递单号|单号|号码)\s*[:：]?\s*', '', express_num, flags=re.IGNORECASE)
                            # 2. 移除可能的空格和其他非法字符
                            express_num = re.sub(r'[^A-Za-z0-9]', '', express_num)
                            # 3. 如果以"SF"开头但不是大写，转换为大写
                            if express_num.lower().startswith('sf') and not express_num.startswith('SF'):
                                express_num = 'SF' + express_num[2:]

                            # 验证快递单号格式
                            if Utils.is_valid_tracking_number(express_num):
                                item['express_num'] = express_num
                                log.info(f"从OpenAI响应中提取到有效的快递单号: {express_num}")
                            else:
                                log.warning(f"OpenAI返回的快递单号格式无效: {express_num}")
                                item['express_num'] = '无'
                        else:
                            item['express_num'] = '无'

                        entry = self._build_ai_parsed_entry(item, raw_line, product, pattern, width, length, count)
                        parsed_results.append(entry)
                    except Exception as e_inner:
                        log.error(f"处理单个结果时出错: {e_inner}, 数据: {item}")
                        parsed_results.append({
                            '原始数据': raw_line,
                            '行号': line_number,
                            '错误原因': f"处理结果出错: {str(e_inner)}",
                            '错误日志': repr(e_inner),
                            '数据处理流程': 'AI解析',
                            '解析错误': f"处理结果出错: {str(e_inner)}"
                        })
                return parsed_results
            except Exception as process_error:
                log.error(f"处理OpenAI响应内容时出错: {process_error}")
                raise process_error

        max_attempts = 3
        backoff_times = [2, 5, 10]  # 基础退避时间（秒）

        # 前3次尝试
        for attempt in range(max_attempts):
            try:
                log_msg = f"OpenAI解析中 文件:{file_name} 行:{line_number} 尝试:{attempt+1}/{max_attempts}"
                self.openai_mgr.status_update.emit(log_msg)

                with self._lock:
                    response = self.openai_mgr.chat_completion(
                        raw_line,
                        force_system_reset=(attempt > 0)
                    )

                log.info(f"OpenAI接口返回: {response}")
                parsed_results = process_response_content(response)

                if parsed_results:
                    # 验证结果有效性 - 检查字典中是否包含解析错误字段
                    valid_results = [r for r in parsed_results if not r.get('解析错误')]
                    if valid_results:
                        self.openai_cache[raw_line] = valid_results  # 只缓存有效结果
                        self.openai_mgr.status_update.emit(f"解析成功: {file_name} 行:{line_number}")
                        return valid_results  # 只返回有效结果
                    else:
                        raise ValueError("解析结果无效，所有结果都包含错误")
                else:
                    raise ValueError("未能生成有效的解析结果")
            except Exception as e:
                err_msg = f"OpenAI解析失败 尝试:{attempt+1}/{max_attempts} 错误:{str(e)}"
                self.openai_mgr.status_update.emit(err_msg)
                log.error(f"{err_msg}, 原始数据: {raw_line}")

                # 智能退避策略
                backoff_time = backoff_times[min(attempt, len(backoff_times)-1)]

                # 针对不同类型的错误采用不同的退避策略
                if '429' in str(e) or 'rate limit' in str(e).lower():
                    # 限速错误，使用更长的退避时间
                    backoff_time = backoff_time * 2
                    self.openai_mgr.status_update.emit(f"遇到限速错误, 等待 {backoff_time} 秒后重试")
                elif 'timeout' in str(e).lower():
                    # 超时错误，可能是网络问题
                    backoff_time = backoff_time * 1.5
                    self.openai_mgr.status_update.emit(f"遇到超时错误, 等待 {backoff_time} 秒后重试")
                else:
                    # 其他错误
                    self.openai_mgr.status_update.emit(f"遇到错误, 等待 {backoff_time} 秒后重试")

                time.sleep(backoff_time)

        # 前3次重试后依然失败，进行最后一次尝试
        try:
            self.openai_mgr.status_update.emit("经过3次重试后仍失败, 最后再次尝试带 system role")
            time.sleep(15)  # 延长等待时间，缓解限速压力

            with self._lock:
                # 最后一次尝试使用更低的温度值，获得更确定性的结果
                response = self.openai_mgr.chat_completion(
                    f"请解析以下文本并提取材质、图案、宽度、高度和数量信息，以JSON格式返回: {raw_line}",  # 修改：长度改为高度
                    force_system_reset=True
                )

            log.info(f"OpenAI接口返回(最后尝试): {response}")
            parsed_results = process_response_content(response)

            if parsed_results:
                # 验证结果有效性 - 检查字典中是否包含解析错误字段
                valid_results = [r for r in parsed_results if not r.get('解析错误')]
                if valid_results:
                    self.openai_cache[raw_line] = valid_results  # 只缓存有效结果
                    self.openai_mgr.status_update.emit(f"解析成功(最后尝试): {file_name} 行:{line_number}")
                    return valid_results  # 只返回有效结果
                else:
                    raise ValueError("最后尝试解析结果无效，所有结果都包含错误")
            else:
                raise ValueError("最后再次尝试未生成有效解析结果")
        except Exception as final_e:
            final_err_msg = f"OpenAI解析最终失败: {str(final_e)}"
            self.openai_mgr.status_update.emit(final_err_msg)
            log.error(f"{final_err_msg}, 原始数据: {raw_line}")

            error_entry = {
                '原始数据': raw_line,
                '行号': line_number,
                '错误原因': f"OpenAI解析失败: {str(final_e)}",
                '错误日志': repr(final_e),
                '数据处理流程': "OpenAI解析",
                '解析错误': f"OpenAI解析失败: {str(final_e)}"
            }
            self.openai_cache[raw_line] = [error_entry]
            return [error_entry]

    def parse_line(self, line: str, line_number: Optional[int] = None,
                  file_name: Optional[str] = None,
                  openai_log_callback: Optional[callable] = None) -> List[Dict[str, Any]]:
        """
        解析单行文本，支持多种格式的自动识别和处理
        
        Args:
            line: 待解析的文本行
            line_number: 行号（可选）
            file_name: 文件名（可选）
            openai_log_callback: OpenAI日志回调函数（可选）
            
        Returns:
            List[Dict[str, Any]]: 解析结果列表，每个元素包含解析后的数据或错误信息
        """
        # 预处理：去除前后空白符
        original_line = line.strip()

        # 新增逻辑：清洗掉 '共' 之后的数据
        if '共' in original_line:
            original_line = original_line.split('共')[0].strip()

        # 新增: 检查"，"后的内容是否包含有效数据
        if '，' in original_line:
            parts = original_line.split('，')
            first_part = parts[0].strip()
            remaining_parts = [p.strip() for p in parts[1:] if p.strip()]
            valid_parts = [first_part]
            for part in remaining_parts:
                has_numbers = bool(re.search(r'\d', part))
                has_separators = len(self.separator_pattern.findall(part)) >= 2
                if has_numbers and has_separators:
                    valid_parts.append(part)
            original_line = '，'.join(valid_parts)

        # 当原始数据中存在全角分隔符 '；' 时，优先以 '；' 作为拆分条件
        if '；' in original_line:
            raw_sub_lines = original_line.split('；')
            sub_lines = []

            # 新增：处理数据补充逻辑
            for i, s in enumerate(raw_sub_lines):
                s_norm = s.strip()
                s_norm = self.leading_symbols_pattern.sub('', s_norm)
                s_norm = s_norm.rstrip(',;，。；#').strip()
                s_norm = (s_norm.replace('～', '-').replace('~', '-')
                        .replace('*', '-').replace('×', '-')
                        .replace('x', '-').replace('X', '-')
                        .replace('cm', '').replace('Cm', '').replace('CM', '').strip())

                # 检查是否缺少必要信息（例如材质和图案）
                parts = s_norm.split('-')
                if len(parts) >= 3 and not any(keyword in s_norm for keyword in self.material_keywords):
                    # 尝试从后续数据中补充信息
                    for next_s in raw_sub_lines[i+1:]:
                        next_norm = next_s.strip()
                        if any(keyword in next_norm for keyword in self.material_keywords):
                            # 提取材质和图案信息
                            material_pattern = re.search(r'([^-]+)-([^-]+)$', next_norm)
                            if material_pattern:
                                material_info = f"{material_pattern.group(1)}-{material_pattern.group(2)}"
                                s_norm = f"{s_norm}-{material_info}"
                                break
                if s_norm:
                    sub_lines.append(s_norm)
        else:
            # 原有逻辑：统一替换、再按多个符号拆分
            line = self.leading_symbols_pattern.sub('', original_line)
            line = line.rstrip(',;，。；#').strip()
            line = (line.replace('～', '-').replace('~', '-')
                        .replace('*', '-').replace('×', '-')
                        .replace('x', '-').replace('X', '-')
                        .replace('\t', ' ')  # 将制表符替换为空格
                        .replace('cm', '').replace('Cm', '').replace('CM', '').strip())
            sub_lines = re.split('[；;，,]', line)
            sub_lines = [s.strip() for s in sub_lines if s.strip()]

        # 去重，保持顺序
        sub_lines = list(dict.fromkeys(sub_lines))

        results = []
        for sub_line in sub_lines:
            # 检查子项是否至少包含两个分隔符（匹配半角 '-'、半角 '~'、全角 '～'）
            separator_count = len(self.separator_pattern.findall(sub_line))

            # 新增：检查原始子项中的所有分隔符（包括'-'、'~'、'*'）
            original_sub_line = sub_line
            total_separator_count = (
                original_sub_line.count('-') +
                original_sub_line.count('~') +
                original_sub_line.count('*') +
                original_sub_line.count('×') +
                original_sub_line.count('x', 0, len(original_sub_line)) +  # 只计算小写x
                original_sub_line.count('X', 0, len(original_sub_line))    # 只计算大写X
            )

            if separator_count < 2:
                results.append({
                    '原始数据': original_line,
                    '行号': line_number,
                    '错误原因': "数据不具备拆分条件，缺少必要的分隔符",
                    '错误日志': f"子项 '{sub_line}' 中检测到的分隔符数量不足（最少需要2个分隔符）",
                    '数据处理流程': '规则匹配',
                    '解析错误': "数据不具备拆分条件"
                })
                continue

            # 新增：当总分隔符数量大于等于4时，优先使用OpenAI解析
            if total_separator_count >= 4:
                if openai_log_callback:
                    openai_log_callback(f"检测到多分隔符case，优先使用AI解析:'{original_sub_line}'")
                openai_results = self.parse_with_openai(original_sub_line, line_number, file_name)

                if openai_results and isinstance(openai_results, list):
                    # 修复验证逻辑 - 检查字典中是否包含解析错误字段
                    valid_results = [r for r in openai_results if not r.get('解析错误')]
                    if valid_results:
                        results.extend(valid_results)
                        continue  # 成功解析，跳过后续规则匹配
                    # 如果OpenAI解析失败，继续尝试规则匹配
                    if openai_log_callback:
                        openai_log_callback(f"OpenAI优先解析未返回有效结果，尝试规则匹配: '{original_sub_line}'")

            # 1. 匹配S波斯绒格式
            match = self.s_persian_pattern.match(sub_line)
            if match:
                try:
                    pattern = self.clean_pattern(match.group(1), '波斯绒', sub_line)
                    entry = {
                        '材质': '波斯绒',
                        '图案': pattern,
                        '宽cm': 0,
                        '高cm': 0,  # 修改：长cm改为高cm
                        '图案全称': f"{pattern}-0-0-横版",
                        '数量': 1,
                        '原始数据': original_line,
                        '处理方式': '规则匹配'
                    }
                    # 统一调用辅助方法检查是否异常
                    results.extend(self.process_local_entry(sub_line, entry, original_line, openai_log_callback, line_number, file_name))
                    continue
                except Exception as e:
                    log.error(f"S波斯绒格式解析错误: {e}")
                    results.append({
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': f"S波斯绒格式解析错误: {str(e)}",
                        '错误日志': repr(e),
                        '数据处理流程': '规则匹配',
                        '解析错误': f"S波斯绒格式解析错误: {str(e)}"
                    })
                    continue

            # 匹配直径格式: e.g. S波斯绒2m-逐光圆形-直径120cm 1
            match = self.diameter_pattern.match(sub_line)
            if match:
                try:
                    material, pattern, diameter, quantity = match.groups()
                    pattern = self.clean_pattern(pattern, material, sub_line)
                    diameter, quantity = int(diameter), int(quantity)
                    entry = {
                        '材质': self.normalize_material_name(material),
                        '图案': f"{pattern}圆形",
                        '宽cm': diameter,
                        '高cm': diameter,
                        '图案全称': f"{pattern}圆形",
                        '数量': quantity,
                        '原始数据': original_line,
                        '处理方式': '规则匹配-直径'
                    }
                    results.extend(self.process_local_entry(sub_line, entry, original_line, openai_log_callback, line_number, file_name))
                    continue
                except Exception as e:
                    log.error(f"直径格式解析错误: {e}")
                    results.append({
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': f"直径格式解析错误: {str(e)}",
                        '错误日志': repr(e),
                        '数据处理流程': '规则匹配',
                        '解析错误': f"直径格式解析错误: {str(e)}"
                    })
                    continue

            # 匹配直径-数字格式: e.g. S仿羊绒2m-柏风絮语-直径-120cm 1
            match = self.diameter_with_dash_pattern.match(sub_line)
            if match:
                try:
                    material, pattern, diameter, quantity = match.groups()
                    pattern = self.clean_pattern(pattern, material, sub_line)
                    diameter, quantity = int(diameter), int(quantity)
                    entry = {
                        '材质': self.normalize_material_name(material),
                        '图案': f"{pattern}圆形",
                        '宽cm': diameter,
                        '高cm': diameter,
                        '图案全称': f"{pattern}圆形",
                        '数量': quantity,
                        '原始数据': original_line,
                        '处理方式': '规则匹配-直径带分隔符'
                    }
                    results.extend(self.process_local_entry(sub_line, entry, original_line, openai_log_callback, line_number, file_name))
                    continue
                except Exception as e:
                    log.error(f"直径带分隔符格式解析错误: {e}")
                    results.append({
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': f"直径带分隔符格式解析错误: {str(e)}",
                        '错误日志': repr(e),
                        '数据处理流程': '规则匹配',
                        '解析错误': f"直径带分隔符格式解析错误: {str(e)}"
                    })
                    continue

            # 匹配扩展直径格式: e.g. S波斯绒2m-逐光圆形-直径120cm 原创设计 1
            match = self.circle_extended_pattern.match(sub_line)
            if match:
                try:
                    material, pattern, diameter, quantity = match.groups()
                    pattern = self.clean_pattern(pattern, material, sub_line)
                    diameter, quantity = int(diameter), int(quantity)
                    entry = {
                        '材质': self.normalize_material_name(material),
                        '图案': f"{pattern}圆形",
                        '宽cm': diameter,
                        '高cm': diameter,
                        '图案全称': f"{pattern}圆形",
                        '数量': quantity,
                        '原始数据': original_line,
                        '处理方式': '规则匹配-扩展直径'
                    }
                    results.extend(self.process_local_entry(sub_line, entry, original_line, openai_log_callback, line_number, file_name))
                    continue
                except Exception as e:
                    log.error(f"扩展直径格式解析错误: {e}")
                    results.append({
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': f"扩展直径格式解析错误: {str(e)}",
                        '错误日志': repr(e),
                        '数据处理流程': '规则匹配',
                        '解析错误': f"扩展直径格式解析错误: {str(e)}"
                    })
                    continue

            # 匹配椭圆格式: e.g. 透气-蝴蝶花园-椭圆-80-160 1
            match = self.oval_pattern.match(sub_line)
            if match:
                try:
                    material, pattern, width, height, quantity = match.groups()
                    pattern = self.clean_pattern(pattern, material, sub_line)
                    width, height, quantity = int(width), int(height), int(quantity)
                    if width < height:  # Swap if width < height
                        width, height = height, width
                    entry = {
                        '材质': self.normalize_material_name(material),
                        '图案': f"{pattern}-椭圆",
                        '宽cm': width,
                        '高cm': height,
                        '图案全称': f"{pattern}-椭圆-{width}-{height}-横版",
                        '数量': quantity,
                        '原始数据': original_line,
                        '处理方式': '规则匹配-椭圆'
                    }
                    results.extend(self.process_local_entry(sub_line, entry, original_line, openai_log_callback, line_number, file_name))
                    continue
                except Exception as e:
                    log.error(f"椭圆格式解析错误: {e}")
                    results.append({
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': f"椭圆格式解析错误: {str(e)}",
                        '错误日志': repr(e),
                        '数据处理流程': '规则匹配',
                        '解析错误': f"椭圆格式解析错误: {str(e)}"
                    })
                    continue

            # 匹配圆形格式: e.g. 透气-圆圆小猪-100圆 7
            match = self.circle_pattern.match(sub_line)
            if match:
                try:
                    material, pattern, diameter, quantity = match.groups()
                    pattern = self.clean_pattern(pattern, material, sub_line)
                    diameter, quantity = int(diameter), int(quantity)
                    entry = {
                        '材质': self.normalize_material_name(material),
                        '图案': f"{pattern}-圆",
                        '宽cm': diameter,
                        '高cm': diameter,
                        '图案全称': f"{pattern}-{diameter}圆",
                        '数量': quantity,
                        '原始数据': original_line,
                        '处理方式': '规则匹配-圆'
                    }
                    results.extend(self.process_local_entry(sub_line, entry, original_line, openai_log_callback, line_number, file_name))
                    continue
                except Exception as e:
                    log.error(f"圆形格式解析错误: {e}")
                    results.append({
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': f"圆形格式解析错误: {str(e)}",
                        '错误日志': repr(e),
                        '数据处理流程': '规则匹配',
                        '解析错误': f"圆形格式解析错误: {str(e)}"
                    })
                    continue

            # 匹配通用格式: e.g. 透气-布拉格-160-230 9
            # This should be tried after more specific patterns
            match = self.general_pattern.match(sub_line)
            if match:
                try:
                    material, pattern, width, height, quantity = match.groups()
                    pattern = self.clean_pattern(pattern, material, sub_line)
                    width, height, quantity = int(width), int(height), int(quantity)
                    if width < height:  # Swap if width < height
                        width, height = height, width
                    entry = {
                        '材质': self.normalize_material_name(material),
                        '图案': pattern,
                        '宽cm': width,
                        '高cm': height,
                        '图案全称': f"{pattern}-{width}-{height}-横版",
                        '数量': quantity,
                        '原始数据': original_line,
                        '处理方式': '规则匹配-通用'
                    }
                    results.extend(self.process_local_entry(sub_line, entry, original_line, openai_log_callback, line_number, file_name))
                    continue
                except Exception as e:
                    log.error(f"通用格式解析错误: {e}")
                    results.append({
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': f"通用格式解析错误: {str(e)}",
                        '错误日志': repr(e),
                        '数据处理流程': '规则匹配',
                        '解析错误': f"通用格式解析错误: {str(e)}"
                    })
                    continue

            # 如果所有规则都不匹配，尝试OpenAI解析作为最后的兜底
            if openai_log_callback:
                openai_log_callback(f"所有规则都不匹配，尝试OpenAI解析: '{sub_line}'")

            openai_results = self.parse_with_openai(sub_line, line_number, file_name)
            if openai_results and isinstance(openai_results, list):
                # 修复验证逻辑 - 检查字典中是否包含解析错误字段
                valid_results = [r for r in openai_results if not r.get('解析错误')]
                if valid_results:
                    results.extend(valid_results)
                    continue

            # 如果OpenAI也解析失败，添加到异常数据
            results.append({
                '原始数据': original_line,
                '行号': line_number,
                '错误原因': "所有解析方式都失败",
                '错误日志': f"子项 '{sub_line}' 无法匹配任何已知格式，OpenAI解析也失败",
                '数据处理流程': '规则匹配+AI兜底',
                '解析错误': "所有解析方式都失败"
            })

        return results

    def process_local_entry(self, sub_line: str, entry: Dict[str, Any],
                           original_line: str, openai_log_callback: Optional[callable] = None,
                           line_number: Optional[int] = None,
                           file_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        辅助方法：检查本地规则解析结果 entry 是否异常。
        如果异常，则调用 OpenAI 进行补救解析；否则返回原始 entry。
        
        Args:
            sub_line: 子行文本
            entry: 本地解析的条目数据
            original_line: 原始行文本
            openai_log_callback: OpenAI日志回调函数（可选）
            line_number: 行号（可选）
            file_name: 文件名（可选）
            
        Returns:
            List[Dict[str, Any]]: 处理后的条目列表
        """
        if self.is_result_anomalous(entry):
            if openai_log_callback:
                openai_log_callback(f"规则解析结果异常，尝试OpenAI补救: '{sub_line}'")
            openai_results = self.parse_with_openai(sub_line, line_number, file_name)
            if openai_results and isinstance(openai_results, list):
                # 修复验证逻辑 - 检查字典中是否包含解析错误字段
                valid_results = [r for r in openai_results if not r.get('解析错误')]
                if valid_results:
                    return valid_results
                else:
                    # OpenAI也解析失败，返回原始错误
                    return [{
                        '原始数据': original_line,
                        '行号': line_number,
                        '错误原因': "规则解析异常且OpenAI补救失败",
                        '错误日志': f"规则解析结果: {entry}, OpenAI结果: {openai_results}",
                        '数据处理流程': '规则匹配+AI补救',
                        '解析错误': "规则解析异常且OpenAI补救失败"
                    }]
            else:
                # OpenAI解析失败，返回原始错误
                return [{
                    '原始数据': original_line,
                    '行号': line_number,
                    '错误原因': "规则解析异常且OpenAI补救失败",
                    '错误日志': f"规则解析结果: {entry}",
                    '数据处理流程': '规则匹配+AI补救',
                    '解析错误': "规则解析异常且OpenAI补救失败"
                }]
        else:
            # 规则解析结果正常，直接返回
            return [entry]

# ---------------------------
# TXT文件处理器（从robot_bot_0.0.12.py移植）
# ---------------------------
class TxtFileProcessor(QThread):
    """
    TXT文件处理器，负责解析txt文件并生成Excel表格
    
    该类继承自QThread，用于在后台线程中处理TXT文件的解析工作，
    避免阻塞主界面。支持进度报告、日志输出和取消操作。
    """
    progress = pyqtSignal(int)
    log = pyqtSignal(str)
    finished = pyqtSignal(bool, str)

    def __init__(self, folder_path: str, openai_mgr: Any, configs: Dict[str, Any],
                 parent: Optional[QObject] = None):
        """
        初始化TXT文件处理器
        
        Args:
            folder_path: 要处理的文件夹路径
            openai_mgr: OpenAI客户端管理器
            configs: 配置字典
            parent: 父对象（可选）
        """
        super().__init__(parent)
        self.folder_path: str = folder_path
        self.openai_mgr: Any = openai_mgr

        # 从configs加载配置
        self.material_keywords: List[str] = configs.get('material_keywords', [])
        self.remove_words: List[str] = configs.get('remove_words', [])
        self.texture_categories: Dict[str, Any] = configs.get('texture_categories', {})
        self.sub_to_base_texture: Dict[str, str] = configs.get('sub_to_base_texture', {})
        self.base_texture_mappings: Dict[str, str] = configs.get('base_texture_mappings', {})
        self.global_width_ranges: List[Any] = configs.get('global_width_ranges', [])
        self.global_sheet_mapping: Dict[str, str] = configs.get('global_sheet_mapping', {})
        self.global_width_arr: List[int] = configs.get('global_width_arr', DEFAULT_WIDTH_VALUES)
        self.is_parse_unrecognized: bool = True

    def run(self) -> None:
        """
        主处理流程
        
        执行TXT文件的处理工作，包括文件组织和解析。
        在后台线程中运行，通过信号与主线程通信。
        """
        try:
            # 组织文件
            self.organize_files()

            # 处理txt文件
            self._process_txt_files()

            self.finished.emit(True, "TXT文件处理完成")

        except Exception as e:
            log.error(f"TXT文件处理失败: {str(e)}")
            self.log.emit(f"TXT文件处理失败: {str(e)}")
            self.finished.emit(False, f"TXT文件处理失败: {str(e)}")


    def organize_files(self) -> None:
        """
        组织TXT文件（不创建子文件夹，直接在主目录处理）
        
        扫描主目录中的TXT文件并准备处理。
        """
        try:
            txt_files = [f for f in os.listdir(self.folder_path) if f.lower().endswith('.txt')]
            self.log.emit(f"发现 {len(txt_files)} 个TXT文件，将在主目录直接处理")

            # 不再创建子文件夹，TXT文件保持在主目录
            for txt_file in txt_files:
                self.log.emit(f"准备处理TXT文件: {txt_file}")

        except Exception as e:
            log.error(f"TXT文件检查失败: {str(e)}")
            self.log.emit(f"TXT文件检查失败: {str(e)}")

    def _process_txt_files(self) -> None:
        """
        处理所有txt文件
        
        使用线程池并发处理主目录中的所有TXT文件，
        支持进度报告和取消操作。
        """
        try:
            txt_files = []
            # 只处理主目录中的 TXT 文件，不遍历子文件夹
            for file in os.listdir(self.folder_path):
                if file.lower().endswith('.txt'):
                    txt_files.append(os.path.join(self.folder_path, file))

            total_files = len(txt_files)
            if total_files == 0:
                self.log.emit("没有找到TXT文件。")
                return

            self.log.emit(f"开始处理 {total_files} 个文件。")
            processed_files = 0

            # 使用线程池处理文件
            with ThreadPoolExecutor(max_workers=min(8, os.cpu_count() + 4)) as executor:
                future_to_file = {
                    executor.submit(self.process_file, txt_file, os.path.basename(txt_file)): txt_file
                    for txt_file in txt_files
                }

                for future in as_completed(future_to_file):
                    txt_file = future_to_file[future]
                    try:
                        result = future.result()
                        if result:
                            self.log.emit(f"成功处理文件：{txt_file}")
                        else:
                            self.log.emit(f"处理失败文件：{txt_file}")
                    except Exception as e:
                        log.error(f"文件 {txt_file} 处理过程中发生错误: {str(e)}")
                        self.log.emit(f"文件 {txt_file} 处理过程中发生错误: {str(e)}")

                    processed_files += 1
                    progress_percent = int((processed_files / total_files) * 100)
                    self.progress.emit(progress_percent)

            self.log.emit("所有文件处理完成。")

        except Exception as e:
            log.error(f"处理txt文件时出错: {str(e)}")
            self.log.emit(f"处理txt文件时出错: {str(e)}")

    def process_file(self, file_path: str, txt_file: str) -> bool:
        """
        处理单个TXT文件

        Args:
            file_path: TXT文件的完整路径
            txt_file: TXT文件名（用于日志显示）

        Returns:
            bool: 处理是否成功
        """
        try:
            # 根据文件名获取对应的宽幅参数
            file_width_values = self.get_width_values_for_file(txt_file)
            self.log.emit(f"文件 {txt_file} 使用宽幅参数: {', '.join(map(str, file_width_values))}cm")

            # 根据文件特定的宽幅参数生成配置
            file_width_ranges, file_sheet_mapping = self.generate_width_mappings(file_width_values)

            # 创建独立的OpenAI会话
            local_openai_mgr = self.openai_mgr.clone()
            local_parser = LineParser(self.material_keywords, self.remove_words, openai_client_manager=local_openai_mgr)

            # 初始化sheet数据 - 使用文件特定的配置
            all_sheet_names = set(file_sheet_mapping.values())
            if hasattr(self, 'base_texture_mappings'):
                for _, mappings in self.base_texture_mappings.items():
                    all_sheet_names.update(mappings['sheet_mapping'].values())
            all_sheet_names.add('数据异常')
            all_sheet_names.add('Other') # for determine_sheet
            sheet_data = {name: [] for name in all_sheet_names if name}

            # 读取并解析文件
            with open(file_path, 'r', encoding='utf-8') as file:
                lines = file.readlines()

            # 处理每一行
            for line_num, line in enumerate(lines, start=1):
                if not line.strip():
                    continue

                try:
                    parsed_entries = local_parser.parse_line(
                        line,
                        line_num,
                        os.path.basename(file_path),
                        lambda msg: self.log.emit(msg)
                    )

                    # 处理解析结果
                    for entry in parsed_entries:
                        # 修复验证逻辑 - 检查字典中是否包含解析错误字段
                        if entry.get('解析错误'):
                            # 错误数据放入数据异常sheet
                            sheet_data['数据异常'].append(entry)
                        else:
                            # 根据宽度分配到对应sheet - 使用文件特定的宽幅配置
                            width = entry.get('宽cm', 0)
                            height = entry.get('高cm', 0)
                            material = entry.get('材质', '')
                            quantity = entry.get('数量', 1)
                            target_sheet = self.determine_sheet(width, material, width_ranges=file_width_ranges,
                                                              sheet_mapping=file_sheet_mapping, height=height, quantity=quantity)
                            
                            if target_sheet and target_sheet.startswith("SPLIT:"):
                                parts = target_sheet.split(":")
                                if len(parts) == 4:
                                    split_target_width = parts[1]
                                    optimized_quantity = int(parts[2])
                                    remaining_quantity = int(parts[3])

                                    if optimized_quantity > 0:
                                        optimized_entry = entry.copy()
                                        optimized_entry['数量'] = optimized_quantity
                                        if split_target_width in sheet_data:
                                            sheet_data[split_target_width].append(optimized_entry)
                                        else:
                                            sheet_data[split_target_width] = [optimized_entry]
                                    
                                    if remaining_quantity > 0:
                                        remaining_entry = entry.copy()
                                        remaining_entry['数量'] = remaining_quantity
                                        original_sheet = self.determine_sheet(width, material, width_ranges=file_width_ranges,
                                                                            sheet_mapping=file_sheet_mapping, height=height, quantity=1)
                                        if original_sheet and not original_sheet.startswith("SPLIT:"):
                                            if original_sheet in sheet_data:
                                                sheet_data[original_sheet].append(remaining_entry)
                                            else:
                                                sheet_data[original_sheet] = [remaining_entry]
                                        else:
                                            sheet_data['数据异常'].append(remaining_entry)
                            elif target_sheet:
                                if target_sheet in sheet_data:
                                    sheet_data[target_sheet].append(entry)
                                else:
                                    # 如果返回了新的sheet名，动态创建
                                    sheet_data[target_sheet] = [entry]
                            else:
                                sheet_data['数据异常'].append(entry)

                except Exception as e:
                    log.error(f"解析行 {line_num} 时出错: {str(e)}")
                    error_record = {
                        '原始数据': line.strip(),
                        '行号': line_num,
                        '错误原因': f"解析异常: {str(e)}",
                        '错误日志': repr(e),
                        '数据处理流程': '解析异常'
                    }
                    sheet_data['数据异常'].append(error_record)

            # 生成Excel文件
            excel_path = Utils.get_excel_path(file_path)
            self._save_to_excel(sheet_data, excel_path)

            return True

        except Exception as e:
            log.error(f"处理文件 {file_path} 时出错: {str(e)}")
            return False

    def get_width_values_for_file(self, filename: str) -> List[int]:
        """
        根据文件名获取对应的宽幅参数

        Args:
            filename: txt文件名

        Returns:
            List[int]: 对应的宽幅参数列表
        """
        # 定义需要使用特殊宽幅参数的关键词
        special_keywords = ['可拆洗', '毯面', '4mm底', '10mm底']

        # 检查文件名是否包含任意一个特殊关键词
        for keyword in special_keywords:
            if keyword in filename:
                # 包含特殊关键词的文件使用不包含180的宽幅参数
                return KCX_WIDTH_VALUES

        # 默认宽幅参数
        return DEFAULT_WIDTH_VALUES

    def generate_width_mappings(self, width_arr: List[int]) -> Tuple[List[Tuple[float, float, str]], Dict[int, str]]:
        """
        根据width_arr生成width_ranges和sheet_mapping

        Args:
            width_arr: 宽度数组

        Returns:
            Tuple[List[Tuple[float, float, str]], Dict[int, str]]: (width_ranges, sheet_mapping)
        """
        width_ranges = []
        sheet_mapping = {}
        previous = 0
        for width in width_arr:
            width_ranges.append((previous, width, str(width)))
            sheet_mapping[width] = str(width)
            previous = width
        width_ranges.append((previous, float('inf'), 'Other'))
        log.info(f"为文件生成的width_ranges: {width_ranges} 和 sheet_mapping: {sheet_mapping}")
        return width_ranges, sheet_mapping

    def _get_base_texture_from_material(self, material: str) -> Optional[str]:
        """
        根据材质名称获取基础材质
        
        参照robot_bot_0.0.12.py的逻辑实现
        
        Args:
            material: 材质名称
            
        Returns:
            Optional[str]: 基础材质名称，如果未找到则返回None
        """
        if not material or not hasattr(self, 'sub_to_base_texture'):
            return None

        # 直接查找
        if material in self.sub_to_base_texture:
            return self.sub_to_base_texture[material]



        log.debug(f"材质 {material} 未找到对应的基础材质")
        return None

    def determine_sheet(self, width: float, material: str, width_ranges: Optional[List[Tuple[float, float, str]]] = None,
                       sheet_mapping: Optional[Dict[int, str]] = None, height: Optional[float] = None,
                       quantity: int = 1) -> Optional[str]:
        """
        根据宽度和高度确定sheet名称，实现三级优先逻辑
        
        倍数优化逻辑：宽或高通过倍数关系匹配宽幅≤200时，根据数量智能分配
        第一优先逻辑：宽 或 高 等于 宽幅，且宽幅 <= 200时，分到对应的 sheet 页
        第二优先逻辑：高 大于前一个宽幅，小于等于当前宽幅时，分到当前宽幅的sheet页

        Args:
            width: 图片宽度
            material: 材质（保留兼容性）
            width_ranges: 宽度范围配置
            sheet_mapping: sheet映射配置
            height: 图片高度
            quantity: 图片数量（用于倍数优化逻辑）
            
        Returns:
            Optional[str]: 目标sheet名称，如果无法确定则返回None
        """
        # 如果没有传入width_ranges和sheet_mapping，根据材质确定
        if width_ranges is None or sheet_mapping is None:
            # 根据材质确定使用哪个宽度配置
            base_texture = self._get_base_texture_from_material(material)
            if base_texture and hasattr(self, 'base_texture_mappings') and base_texture in self.base_texture_mappings:
                mappings = self.base_texture_mappings[base_texture]
                width_ranges = mappings['width_ranges']
                sheet_mapping = mappings['sheet_mapping']
                log.debug(f"材质 {material} -> 基础材质 {base_texture}，使用专用宽度配置: {[r[1] for r in width_ranges if r[1] != float('inf')]}")
            else:
                # 使用全局配置
                if hasattr(self, 'global_width_ranges') and hasattr(self, 'global_sheet_mapping'):
                    width_ranges = self.global_width_ranges
                    sheet_mapping = self.global_sheet_mapping
                    log.debug(f"材质 {material} 使用全局宽度配置: {[r[1] for r in width_ranges if r[1] != float('inf')]}")
                else:
                    # 回退到默认配置
                    width_values = getattr(self, 'global_width_arr', DEFAULT_WIDTH_VALUES)
                    sheet_mapping = {width_val: str(width_val) for width_val in width_values}
                    width_ranges = []
                    previous = 0
                    for width_val in width_values:
                        width_ranges.append((previous, width_val, str(width_val)))
                        previous = width_val
                    width_ranges.append((previous, float('inf'), 'Other'))
                    log.debug(f"材质 {material} 使用默认宽度配置: {width_values}")

        # 如果仍然没有sheet_mapping，构建默认映射
        if sheet_mapping is None:
            width_values = getattr(self, 'global_width_arr', DEFAULT_WIDTH_VALUES)
            sheet_mapping = {width_val: str(width_val) for width_val in width_values}

        # 从width_ranges中提取宽幅值列表
        width_values = []
        for _, upper, _ in width_ranges:
            if upper != float('inf'):
                width_values.append(upper)
        width_values.sort()

        # 如果没有传入高度，使用宽度作为高度（向后兼容）
        if height is None:
            height = width
            log.debug(f"未传入高度参数，使用宽度{width}作为高度")

        log.debug(f"Sheet分配逻辑: 宽度={width}, 高度={height}, 数量={quantity}, 宽幅值={width_values}")

        # 倍数优化逻辑：宽或高通过倍数关系匹配宽幅≤200时，根据数量智能分配
        multiplier_result = self._check_multiplier_logic(width, height, width_values, quantity)
        if multiplier_result:
            log.info(f"倍数优化逻辑匹配: {multiplier_result['description']}，分配到sheet {multiplier_result['target_sheet']}")
            return multiplier_result['target_sheet']

        # 第一优先逻辑：宽 或 高 等于 宽幅，且宽幅 <= 200时，分到对应的 sheet 页
        for width_value in width_values:
            if width_value <= 200:  # 宽幅 <= 200的条件
                if width == width_value or height == width_value:
                    log.info(f"第一优先逻辑匹配: 宽{width}或高{height}等于宽幅{width_value}，分配到sheet {width_value}")
                    return str(width_value)

        # 第二优先逻辑：高 大于前一个宽幅，小于等于当前宽幅时，分到当前宽幅的sheet页
        previous_width = 0
        for width_value in width_values:
            if previous_width < height <= width_value:
                log.info(f"第二优先逻辑匹配: 高{height}在({previous_width}, {width_value}]范围内，分配到sheet {width_value}")
                return str(width_value)
            previous_width = width_value

        # 如果都不匹配，返回最大的宽幅或Other
        if width_values:
            if height > width_values[-1]:
                log.info(f"高度{height}超过最大宽幅{width_values[-1]}，分配到Other sheet")
                return 'Other'
            else:
                log.info(f"使用默认逻辑，分配到最小宽幅sheet {width_values[0]}")
                return str(width_values[0])  # 默认返回最小的宽幅

        log.warning(f"无法确定sheet分配，宽度={width}, 高度={height}")
        return None

    def _check_multiplier_logic(self, width, height, width_values, quantity):
        """
        检查倍数优化逻辑：宽或高通过倍数关系匹配宽幅≤200时，根据数量智能分配

        例如：
        - 宽或高是50时，4的倍数分配到200（50×4=200）
        - 宽或高是40时，5的倍数分到200（40×5=200）
        - 宽或高是60时，3的倍数分到180（60×3=180）

        智能分配规则：
        - 9个130×50的图片 → 8个分到200宽幅，1个分到原sheet
        - 3个100×100的图片 → 2个分到200宽幅，1个分到原sheet

        Args:
            width: 图片宽度
            height: 图片高度
            width_values: 宽幅值列表
            quantity: 图片数量

        Returns:
            dict: 匹配结果，包含target_sheet和description，如果不匹配返回None
        """
        # 只考虑宽幅≤200的情况
        target_widths = [w for w in width_values if w <= 200]
        target_widths.sort(reverse=True)  # 从大到小排序，优先选择更大的宽幅

        # 检查宽度和高度
        dimensions = [(width, "宽度"), (height, "高度")]

        best_result = None
        max_target_width = 0

        for dimension, dimension_type in dimensions:
            if dimension <= 0:
                continue

            for target_width in target_widths:
                if target_width % dimension == 0:  # 目标宽幅是当前尺寸的倍数
                    multiplier = target_width // dimension
                    if multiplier > 1:  # 确保是真正的倍数关系
                        # 计算可以优化的数量
                        optimized_quantity = (quantity // multiplier) * multiplier
                        remaining_quantity = quantity % multiplier

                        if optimized_quantity > 0:  # 至少有一组可以优化
                            # 选择能达到的最大宽幅
                            if target_width > max_target_width:
                                max_target_width = target_width

                                if remaining_quantity == 0:
                                    # 全部数量都可以分配到目标宽幅
                                    description = f"{dimension_type}{dimension}的{multiplier}倍等于宽幅{target_width}，{quantity}个图片全部分配到sheet {target_width}"
                                    target_sheet = str(target_width)
                                else:
                                    # 部分数量分配到目标宽幅，剩余的需要特殊处理
                                    groups = quantity // multiplier
                                    description = f"{dimension_type}{dimension}的{multiplier}倍等于宽幅{target_width}，{optimized_quantity}个图片（{groups}组×{multiplier}个）分配到sheet {target_width}，剩余{remaining_quantity}个需要单独处理"
                                    # 对于有剩余的情况，我们返回一个特殊的标记，让调用方处理分割逻辑
                                    target_sheet = f"SPLIT:{target_width}:{optimized_quantity}:{remaining_quantity}"

                                best_result = {
                                    'target_sheet': target_sheet,
                                    'description': description,
                                    'optimized_quantity': optimized_quantity,
                                    'remaining_quantity': remaining_quantity,
                                    'multiplier': multiplier,
                                    'target_width': target_width
                                }

        return best_result

    def _save_to_excel(self, sheet_data, excel_path):
        """保存数据到Excel文件"""
        try:
            import pandas as pd

            with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
                for sheet_name, data in sheet_data.items():
                    if data:  # 只保存有数据的sheet
                        df = pd.DataFrame(data)
                        df.to_excel(writer, sheet_name=sheet_name, index=False)

            # 为未入库数据设置黄色背景
            Utils.apply_yellow_background_for_missing_images(excel_path)

            self.log.emit(f"Excel文件已保存: {excel_path}")

        except Exception as e:
            log.error(f"保存Excel文件失败: {str(e)}")
            self.log.emit(f"保存Excel文件失败: {str(e)}")
            raise

# ---------------------------
# OpenAI客户端管理器（新增长期记忆机制）
# ---------------------------
class OpenAIClientManager(QObject):
    """
    OpenAI客户端管理器（支持长期记忆机制）
    
    负责管理OpenAI API客户端的初始化、配置和连接。
    支持长期记忆上下文、QPM限制、并行请求处理等高级功能。
    继承自QObject以支持Qt信号机制，用于异步通知状态更新。
    """
    initialized = pyqtSignal(bool, str)
    status_update = pyqtSignal(str)  # 用于更新日志、进度与网络请求状态

    def __init__(self, supabase_helper: Optional[Any] = None):
        """
        初始化OpenAI客户端管理器
        
        Args:
            supabase_helper: Supabase助手实例，用于获取配置信息（可选）
        """
        super().__init__()
        self.client: Optional[Any] = None
        self.config: Dict[str, Any] = {}
        self.supabase_helper: Optional[Any] = supabase_helper  # 使用主项目的SupabaseHelper
        
        # 长期记忆上下文
        self.chat_history: List[Dict[str, Any]] = []
        
        # 最大上下文token数
        self._max_context_tokens: int = 3500
        
        # QPM限制，初始化为0（不限制）
        self._qpm: int = 0
        
        # QPM时间窗口起点
        self._qpm_current_window_start: float = time.time()
        
        # 当前时间窗口内的调用次数
        self._qpm_call_count: int = 0
        
        # 用于控制QPM计数的锁
        self._qpm_lock: threading.Lock = threading.Lock()
        
        # 异步初始化
        threading.Thread(target=self._async_init, daemon=True).start()

        # 并行请求相关
        self._request_queue: queue.Queue = queue.Queue()
        self._worker_pool: List[Any] = []
        self._max_workers: int = MAX_WORKERS  # 最大并行请求数
        self._workers_running: bool = False
        self._results: Dict[str, Any] = {}  # 存储请求的结果
        self._result_locks: Dict[str, threading.Lock] = {}  # 用于同步结果访问的锁
        self._request_id_counter: int = 0
        self._request_id_lock: threading.Lock = threading.Lock()

    def _async_init(self) -> None:
        """
        异步初始化OpenAI客户端
        
        从Supabase获取配置信息并初始化OpenAI客户端。
        在后台线程中运行，通过信号通知初始化状态。
        """
        try:
            self.status_update.emit("正在获取OpenAI配置...")
            start_time = time.time()
            # 从 Supabase 获取配置，使用SupabaseHelper
            if self.supabase_helper and self.supabase_helper.is_connected():
                auth_client = self.supabase_helper.get_authenticated_client()
                if auth_client:
                    openai_response = auth_client.table('openai_robot').select('*').eq('robot', ROBOT_BOT_TAG).execute()
                    if not openai_response.data:
                        raise ValueError(f"未找到 {ROBOT_BOT_TAG} 配置")
                else:
                    raise ValueError("无法获取认证客户端")
            else:
                raise ValueError("Supabase连接不可用")

            self.config = openai_response.data[0]
            required_params = ['api_key', 'base_url', 'model', 'sys_role', 'sys_content']
            if not all(self.config.get(p) for p in required_params):
                raise ValueError("OpenAI配置参数不完整")

            # 获取新字段 qpm，0 表示不限制， >0 表示每分钟最大调用次数
            self._qpm = int(self.config.get('qpm', 0))

            self.status_update.emit("正在创建OpenAI客户端...")
            # 采用 OpenAI 兼容API，配置从 Supabase中获取
            self.client = OpenAI(
                api_key=self.config['api_key'],
                base_url=self.config['base_url']
            )

            # 初始化长期记忆，设置系统角色一次
            self._reset_chat_history()
            # 设置最大上下文token数（如配置中有设置则使用配置，否则默认3500）
            self._max_context_tokens = int(self.config.get('max_context_tokens', 3500))

            self.status_update.emit("正在测试OpenAI连接...")
            self._test_connection_async()

            log.info(f"OpenAI初始化完成，耗时 {time.time()-start_time:.2f}s")
            self.initialized.emit(True, "OpenAI客户端初始化成功")

            # 在初始化完成后启动工作线程池
            self._start_worker_pool()
        except Exception as e:
            error_msg = f"OpenAI初始化失败: {str(e)}"
            log.error(error_msg)
            self.initialized.emit(False, error_msg)

    def _start_worker_pool(self):
        """启动工作线程池来处理请求队列"""
        if self._workers_running:
            return

        self._workers_running = True
        for _ in range(min(self._max_workers, max(1, self._qpm) if self._qpm > 0 else self._max_workers)):
            worker = threading.Thread(target=self._process_request_queue, daemon=True)
            worker.start()
            self._worker_pool.append(worker)

        log.info(f"启动了 {len(self._worker_pool)} 个请求处理线程")

    def _process_request_queue(self):
        """工作线程函数，处理请求队列中的任务"""
        while self._workers_running:
            try:
                # 从队列获取任务，最多等待1秒
                try:
                    request_id, user_message, force_system_reset = self._request_queue.get(timeout=1)
                except queue.Empty:
                    continue

                # 应用速率限制
                self._rate_limit()

                # 处理请求
                try:
                    result = self._execute_chat_completion(user_message, force_system_reset)
                    with self._result_locks.get(request_id, threading.Lock()):
                        self._results[request_id] = {"success": True, "result": result}
                except Exception as e:
                    with self._result_locks.get(request_id, threading.Lock()):
                        self._results[request_id] = {"success": False, "error": str(e)}

                # 标记任务完成
                self._request_queue.task_done()

            except Exception as e:
                log.error(f"请求处理线程发生错误: {str(e)}")
                time.sleep(1)  # 避免在循环中过快地失败

    def _rate_limit(self):
        """
        简单的 QPM 限速机制：每分钟最多允许 self._qpm 次调用。
        如果 self._qpm 为 0，则不会限速。
        """
        if self._qpm <= 0:
            return
        with self._qpm_lock:
            current_time = time.time()
            # 如果当前窗口已过60秒，则重置计数器及起始时间
            if (current_time - self._qpm_current_window_start) >= 60:
                self._qpm_current_window_start = current_time
                self._qpm_call_count = 0
            # 如果调用数已达到上限，则等待直到新的时间窗口开始
            if self._qpm_call_count >= self._qpm:
                sleep_time = 60 - (current_time - self._qpm_current_window_start)
                if sleep_time > 0:
                    log.info(f"达到QPM限制，等待 {sleep_time:.2f} 秒")
                    time.sleep(sleep_time)
                self._qpm_current_window_start = time.time()
                self._qpm_call_count = 0
            self._qpm_call_count += 1

    def _test_connection_async(self):
        """异步测试连接（不影响主流程）"""
        def _test():
            try:
                start = time.time()
                # 安全获取测试消息内容
                test_message = self.config.get('sys_content', 'Hello')[:50] + "..."
                # 使用长期记忆接口测试
                _ = self.chat_completion(test_message)
                log.debug(f"OpenAI连接测试成功，耗时 {time.time()-start:.2f}s")
            except Exception as e:
                log.warning(f"OpenAI连接测试失败（不影响主要功能）: {str(e)}")

        # 使用线程而不是QTimer，确保不阻塞UI线程
        threading.Thread(target=_test, daemon=True).start()

    def get_client_config(self):
        """返回当前的客户端配置信息，供LineParser使用"""
        return self.config

    def _reset_chat_history(self):
        """重置聊天历史和系统角色设定"""
        system_message = self.config.get('sys_content', '')

        # 初始化聊天历史，设置系统角色
        self.chat_history = [
            {"role": "system", "content": system_message}
        ]

    def _estimate_token_count(self, messages):
        """
        简单估算消息中使用的token数，每4个字符大致算作1个token
        """
        total_chars = sum(len(msg['content']) for msg in messages)
        return total_chars // 4

    def _execute_chat_completion(self, user_message, force_system_reset=False):
        """实际执行聊天请求的内部方法"""
        if not self.client:
            raise ValueError("OpenAI客户端未初始化")

        if force_system_reset:
            self._reset_chat_history()

        # 添加用户消息
        self.chat_history.append({"role": "user", "content": user_message})

        # 确保历史不超过token限制
        token_count = self._estimate_token_count(self.chat_history)
        while token_count > self._max_context_tokens and len(self.chat_history) > 2:
            # 移除最早的非系统消息
            for i in range(1, len(self.chat_history)):
                if self.chat_history[i]["role"] != "system":
                    self.chat_history.pop(i)
                    break
            # 重新计算token
            token_count = self._estimate_token_count(self.chat_history)

        response = self.client.chat.completions.create(
            model=self.config['model'],
            messages=self.chat_history,
            temperature=float(self.config.get('temperature', 0.4)),
        )

        # 保存应答到历史
        self.chat_history.append({"role": "assistant", "content": response.choices[0].message.content})

        return response

    def chat_completion(self, user_message, force_system_reset=False):
        """
        提交聊天请求，会根据QPM设置决定是串行还是并行处理

        当QPM为0或大于50时，使用直接调用模式
        当QPM有限制且小于等于50时，请求会提交到请求队列中并行处理
        参数:
            user_message: 用户消息
            force_system_reset: 是否强制重置系统消息和聊天历史
        返回:
            OpenAI API的响应对象

        注意：即使使用并行处理模式，仍然遵循QPM限制，不会超过每分钟允许的请求数
        """
        # 不需要限制或请求较少时，直接处理
        if self._qpm <= 0 or self._qpm > 50:
            self._rate_limit()
            return self._execute_chat_completion(user_message, force_system_reset)

        # 生成唯一请求ID
        with self._request_id_lock:
            request_id = self._request_id_counter
            self._request_id_counter += 1

        # 初始化结果锁
        self._result_locks[request_id] = threading.Lock()

        # 将请求加入队列
        self._request_queue.put((request_id, user_message, force_system_reset))

        # 等待结果
        while True:
            with self._result_locks[request_id]:
                if request_id in self._results:
                    result = self._results[request_id]
                    # 清理结果
                    del self._results[request_id]
                    del self._result_locks[request_id]

                    if result["success"]:
                        return result["result"]
                    else:
                        raise ValueError(result["error"])

            time.sleep(0.1)  # 短暂休眠防止CPU占用过高

    def clone(self) -> 'OpenAIClientManager':
        """
        克隆当前OpenAI客户端管理器，为单独的文件处理创建独立的会话上下文
        
        创建一个新的管理器实例，共享客户端和配置，但拥有独立的聊天历史。
        所有克隆实例共享QPM限制和并行处理资源，确保全局速率控制。
        
        Returns:
            OpenAIClientManager: 新的客户端管理器实例
        """
        new_mgr = OpenAIClientManager.__new__(OpenAIClientManager)
        # 初始化QObject基类
        QObject.__init__(new_mgr)

        new_mgr.client = self.client
        new_mgr.config = self.config
        new_mgr.chat_history = [{"role": "system", "content": self.config.get('sys_content', '')}]
        new_mgr._max_context_tokens = self._max_context_tokens

        # 共享 QPM 限速相关配置和状态（确保所有克隆实例都遵循同一限速策略）
        new_mgr._qpm = self._qpm
        new_mgr._qpm_lock = self._qpm_lock
        new_mgr._qpm_call_count = self._qpm_call_count
        new_mgr._qpm_current_window_start = self._qpm_current_window_start

        # 初始化并行处理相关属性
        new_mgr._request_queue = self._request_queue  # 共享请求队列
        new_mgr._worker_pool = self._worker_pool  # 共享线程池
        new_mgr._max_workers = self._max_workers
        new_mgr._workers_running = self._workers_running
        new_mgr._results = self._results  # 共享结果存储
        new_mgr._result_locks = self._result_locks  # 共享结果锁
        new_mgr._request_id_counter = self._request_id_counter
        new_mgr._request_id_lock = self._request_id_lock

        # 连接信号
        new_mgr.status_update = self.status_update
        new_mgr.initialized = self.initialized

        return new_mgr

    def has_valid_client(self) -> bool:
        """
        检查OpenAI客户端是否已初始化且有效
        
        Returns:
            bool: 如果客户端和配置都已正确初始化则返回True，否则返回False
        """
        return self.client is not None and self.config is not None

# ---------------------------
# 图片索引相关工作线程
# ---------------------------

class ProcessedTableImageRetrieveWorker(QThread):
    """
    从已处理表格中检索图片的工作线程
    
    该类负责根据已处理的表格数据检索对应的图片文件，
    使用图片索引器进行高效的图片查找和匹配。
    支持进度报告、状态更新和详细的检索结果统计。
    """
    progress_signal = pyqtSignal(int)
    status_signal = pyqtSignal(str)
    log_signal = pyqtSignal(str)
    error_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(bool, str, list)  # 成功标志, 消息, 检索结果

    def __init__(self, processed_tables: Dict[str, Any], image_indexer: Any,
                 base_folder_path: str, default_dpi: int = DEFAULT_DPI):
        """
        初始化图片检索工作线程
        
        Args:
            processed_tables: 已处理的表格数据字典
            image_indexer: 图片索引器实例
            base_folder_path: 基础文件夹路径
            default_dpi: 默认DPI值
        """
        super().__init__()
        self.processed_tables: Dict[str, Any] = processed_tables  # 已处理的表格数据
        self.image_indexer: Any = image_indexer
        self.base_folder_path: str = base_folder_path
        self.default_dpi: int = default_dpi  # 默认DPI
        self.results: List[Any] = []
        self.missing_images: List[Dict[str, Any]] = []  # 存储丢失图片信息

    def run(self) -> None:
        """
        线程运行方法
        
        执行图片检索的主要逻辑，包括验证输入、处理表格数据、
        检索图片并生成详细的统计报告。
        """
        try:
            if not HAS_IMAGE_INDEXER or not self.image_indexer:
                self.finished_signal.emit(False, "图片索引器不可用", [])
                return

            if not self.processed_tables:
                self.finished_signal.emit(False, "没有已处理的表格数据", [])
                return

            self.log_signal.emit("开始从已处理的表格中检索图片...")

            # 清空丢失图片列表
            self.missing_images = []

            total_files = len(self.processed_tables)
            processed_files = 0
            total_found = 0
            results = []

            # 处理每个表格文件
            for file_path, file_data in self.processed_tables.items():
                try:
                    self.log_signal.emit(f"正在处理: {os.path.basename(file_path)}")

                    # 重要修复：为每个文件清空未入库图片列表，确保不跨文件累积
                    self.missing_images = []
                    self.log_signal.emit(f"已为文件 {os.path.basename(file_path)} 重置未入库数据收集")

                    # 检索该文件的图片
                    file_found, file_result = self._process_single_file(file_path, file_data)
                    total_found += file_found

                    if file_result:
                        results.append(file_result)

                    processed_files += 1
                    progress = int(processed_files / total_files * 100)
                    self.progress_signal.emit(progress)

                    self.status_signal.emit(f"已处理 {processed_files}/{total_files} 个文件")

                except Exception as e:
                    self.log_signal.emit(f"处理文件 {file_path} 时出错: {str(e)}")
                    continue

            result_message = f"检索完成！处理了 {processed_files} 个文件，共找到 {total_found} 个图片"
            self.log_signal.emit(result_message)

            # 自动开始图片尺寸修改处理
            self.log_signal.emit("\n=== 开始自动图片尺寸修改处理 ===")
            resize_success = self._process_image_resize()

            if resize_success:
                final_message = f"{result_message}\n图片尺寸修改处理完成！"
                self.log_signal.emit("=== 图片尺寸修改处理完成 ===")
            else:
                final_message = f"{result_message}\n图片尺寸修改处理失败，请检查日志"
                self.log_signal.emit("=== 图片尺寸修改处理失败 ===")

            # 强制设置进度为100%，确保UI正确显示
            self.progress_signal.emit(100)
            self.status_signal.emit("处理完成")

            self.finished_signal.emit(True, final_message, results)

        except Exception as e:
            error_msg = f"图片检索失败: {str(e)}"
            self.log_signal.emit(error_msg)
            # 即使失败也设置进度为100%，表示任务已结束
            self.progress_signal.emit(100)
            self.status_signal.emit("处理失败")
            self.finished_signal.emit(False, error_msg, [])

    def _process_single_file(self, file_path, file_data):
        """处理单个文件的图片检索，直接从总操作表格开始"""
        try:
            # 直接使用总操作表格文件进行图片检索
            source_file_path = file_data.get('source_file', file_path)
            if not os.path.exists(source_file_path):
                self.log_signal.emit(f"未找到总操作表格: {source_file_path}")
                return 0, None

            # 读取Excel文件的所有sheet
            try:
                xls = pd.ExcelFile(source_file_path)
                sheet_names = xls.sheet_names
                self.log_signal.emit(f"检测到 {len(sheet_names)} 个sheet: {', '.join(sheet_names)}")
            except Exception as e:
                self.log_signal.emit(f"读取总操作表格失败: {str(e)}")
                return 0, None

            if not sheet_names:
                self.log_signal.emit(f"总操作表格中没有sheet: {source_file_path}")
                return 0, None

            # 存储所有sheet的处理结果
            processed_sheets = {}
            total_found = 0
            total_processed = 0

            # 处理每个sheet
            for sheet_name in sheet_names:
                try:
                    self.log_signal.emit(f"正在处理sheet: {sheet_name}")

                    # 读取sheet数据
                    df = pd.read_excel(source_file_path, sheet_name=sheet_name)

                    if df.empty:
                        self.log_signal.emit(f"Sheet {sheet_name} 为空，跳过")
                        processed_sheets[sheet_name] = df  # 保持空的sheet
                        continue

                    # 检查是否为数据异常sheet，如果是则直接复制
                    if sheet_name == '数据异常':
                        self.log_signal.emit(f"Sheet {sheet_name} 为数据异常sheet，直接复制")
                        processed_sheets[sheet_name] = df
                        continue

                    # 检查必要的列
                    if '图案' not in df.columns:
                        self.log_signal.emit(f"Sheet {sheet_name} 中未找到'图案'列，跳过")
                        processed_sheets[sheet_name] = df  # 保持原始数据
                        continue

                    # 添加图片路径和查询方式列（如果不存在）
                    if '图片路径' not in df.columns:
                        df['图片路径'] = ''
                    if '查询方式' not in df.columns:
                        df['查询方式'] = ''

                    sheet_found = 0
                    sheet_processed = 0

                    # 处理每一行
                    for index, row in df.iterrows():
                        sheet_processed += 1

                        if pd.isna(row['图案']) or not str(row['图案']).strip():
                            df.at[index, '图片路径'] = '图案为空'
                            df.at[index, '查询方式'] = '跳过'
                            # 收集丢失图片信息
                            self._add_missing_image_info(
                                pattern_name='',
                                reason='图案为空',
                                sheet_name=sheet_name,
                                quantity=row.get('数量', 1),
                                image_path='',
                                search_method='跳过',
                                filename=os.path.basename(file_path)
                            )
                            continue

                        pattern_name = str(row['图案']).strip()
                        image_path = None
                        search_method = ''

                        # 计算表格中的宽高比
                        table_aspect_ratio = None
                        try:
                            # 尝试从多个可能的字段获取宽度和高度
                            width_cm = None
                            height_cm = None

                            # 获取宽度
                            possible_width_fields = ['宽cm', '宽度', 'width', '宽', 'w']
                            for field in possible_width_fields:
                                if field in row and pd.notna(row[field]):
                                    try:
                                        width_cm = float(row[field])
                                        break
                                    except (ValueError, TypeError):
                                        continue

                            # 获取高度
                            possible_height_fields = ['高cm', '高度', 'height', '高', 'h']
                            for field in possible_height_fields:
                                if field in row and pd.notna(row[field]):
                                    try:
                                        height_cm = float(row[field])
                                        break
                                    except (ValueError, TypeError):
                                        continue

                            # 计算宽高比
                            if width_cm and height_cm and width_cm > 0 and height_cm > 0:
                                table_aspect_ratio = width_cm / height_cm
                        except Exception as e:
                            # 如果计算宽高比失败，继续处理但不使用宽高比信息
                            pass

                        # 方形图片的特殊查找逻辑
                        if '方形' in pattern_name:
                            # 优先使用图案全称（如果存在）
                            if '图案全称' in df.columns and pd.notna(row['图案全称']):
                                full_pattern_name = str(row['图案全称']).strip()
                                if full_pattern_name:
                                    try:
                                        # 第一步：精确查询完整的图案全称（包含方形）
                                        image_path = self.image_indexer.find_image(full_pattern_name, exact_match=True, table_aspect_ratio=table_aspect_ratio)
                                        if image_path:
                                            search_method = "图案全称精确匹配(含方形)"
                                        else:
                                            # 第二步：去掉方形，精确查询修改后的图案全称
                                            modified_full_pattern = full_pattern_name.replace('方形', '')
                                            # 清理可能的连续分隔符
                                            modified_full_pattern = re.sub(r'-+', '-', modified_full_pattern).strip('-')
                                            if modified_full_pattern:
                                                image_path = self.image_indexer.find_image(modified_full_pattern, exact_match=True, table_aspect_ratio=table_aspect_ratio)
                                                if image_path:
                                                    search_method = "图案全称精确匹配(去方形)"
                                    except Exception as e:
                                        self.log_signal.emit(f"查找方形图片时发生错误 {full_pattern_name}: {str(e)}")

                            # 如果图案全称没找到，使用图案名称进行方形特殊查找
                            if not image_path:
                                try:
                                    # 第三步：精确查询图案名称（包含方形）
                                    image_path = self.image_indexer.find_image(pattern_name, exact_match=True, table_aspect_ratio=table_aspect_ratio)
                                    if image_path:
                                        search_method = "图案名称精确匹配(含方形)"
                                    else:
                                        # 第四步：去掉方形，精确查询修改后的图案名称
                                        modified_pattern = pattern_name.replace('方形', '').strip()
                                        if modified_pattern:
                                            image_path = self.image_indexer.find_image(modified_pattern, exact_match=True, table_aspect_ratio=table_aspect_ratio)
                                            if image_path:
                                                search_method = "图案名称精确匹配(去方形)"
                                except Exception as e:
                                    self.log_signal.emit(f"查找方形图片时发生错误 {pattern_name}: {str(e)}")
                        else:
                            # 非方形图片的原有逻辑（圆形、半圆、异形等）
                            # 优先使用图案全称（如果存在）
                            if '图案全称' in df.columns and pd.notna(row['图案全称']):
                                full_pattern_name = str(row['图案全称']).strip()
                                if full_pattern_name:
                                    try:
                                        image_path = self.image_indexer.find_image(full_pattern_name, exact_match=True, table_aspect_ratio=table_aspect_ratio)
                                        if image_path:
                                            search_method = "图案全称精确匹配"
                                    except Exception as e:
                                        self.log_signal.emit(f"查找图片时发生错误 {full_pattern_name}: {str(e)}")

                            # 如果图案全称没找到，使用图案名称进行精确匹配
                            if not image_path:
                                try:
                                    image_path = self.image_indexer.find_image(pattern_name, exact_match=True, table_aspect_ratio=table_aspect_ratio)
                                    if image_path:
                                        search_method = "图案名称精确匹配"
                                except Exception as e:
                                    self.log_signal.emit(f"查找图片时发生错误 {pattern_name}: {str(e)}")


                        # 更新结果
                        if image_path:
                            # 将路径中的 '/' 替换为 '\'
                            normalized_path = image_path.replace('/', '\\')
                            df.at[index, '图片路径'] = normalized_path
                            df.at[index, '查询方式'] = search_method
                            sheet_found += 1
                            total_found += 1
                        else:
                            df.at[index, '图片路径'] = '未入库'
                            df.at[index, '查询方式'] = '未找到'
                            # 收集丢失图片信息
                            self._add_missing_image_info(
                                pattern_name=pattern_name,
                                reason='未入库',
                                sheet_name=sheet_name,
                                quantity=row.get('数量', 1),
                                image_path='',
                                search_method='未找到',
                                filename=os.path.basename(file_path)
                            )

                    # 保存处理后的sheet
                    processed_sheets[sheet_name] = df
                    total_processed += sheet_processed

                    self.log_signal.emit(f"Sheet {sheet_name} 处理完成: {sheet_processed} 行，找到 {sheet_found} 个图片")

                except Exception as e:
                    self.log_signal.emit(f"处理sheet {sheet_name} 时出错: {str(e)}")
                    # 即使出错也要保持原始数据
                    try:
                        df = pd.read_excel(source_file_path, sheet_name=sheet_name)
                        processed_sheets[sheet_name] = df
                    except Exception:
                        pass

            # 保存结果，保持所有sheet的结构
            output_path = file_data.get('output_file')
            if not output_path:
                # 如果没有预设的输出路径，从源文件生成
                output_path = self._get_output_path(source_file_path)
            self._save_excel_with_sheets(output_path, processed_sheets)

            self.log_signal.emit(f"文件 {os.path.basename(file_path)} 检索完成: 处理 {total_processed} 行，找到 {total_found} 个图片")

            return total_found, {
                'file_path': file_path,
                'output_path': output_path,
                'total': total_processed,
                'found': total_found
            }

        except Exception as e:
            self.log_signal.emit(f"处理文件 {file_path} 时出错: {str(e)}")
            return 0, None

    def _add_missing_image_info(self, pattern_name, reason, sheet_name, quantity, image_path, search_method, filename):
        """添加丢失图片信息到列表中"""
        try:
            # 确保数量是有效数字
            try:
                quantity = int(float(quantity)) if pd.notna(quantity) else 1
                if quantity <= 0:
                    quantity = 1
            except (ValueError, TypeError):
                quantity = 1

            missing_info = {
                '图案名称': pattern_name,
                '丢失原因': reason,
                '所在Sheet': sheet_name,
                '数量': quantity,
                '图片路径': image_path,
                '查询方式': search_method,
                '文件名': filename
            }
            self.missing_images.append(missing_info)
        except Exception as e:
            self.log_signal.emit(f"记录丢失图片信息时出错: {str(e)}")





    def _get_output_path(self, source_file_path):
        """从总操作表格文件路径生成已检索文件路径"""
        file_dir = os.path.dirname(source_file_path)
        file_name, _ = os.path.splitext(os.path.basename(source_file_path))
        # 移除'_总操作表格'后缀，添加'_已检索'后缀
        if file_name.endswith('_总操作表格'):
            base_name = file_name[:-len('_总操作表格')]
        else:
            base_name = file_name
        output_file_name = f"{base_name}_已检索.xlsx"
        return os.path.join(file_dir, output_file_name)

    def _process_image_resize(self):
        """处理图片尺寸修改"""
        try:
            # 查找所有"xx_已检索.xlsx"文件
            retrieved_files = []
            for filename in os.listdir(self.base_folder_path):
                if filename.endswith('_已检索.xlsx'):
                    file_path = os.path.join(self.base_folder_path, filename)
                    retrieved_files.append(file_path)

            if not retrieved_files:
                self.log_signal.emit("未找到任何'_已检索.xlsx'文件，跳过图片尺寸修改")
                return True  # 不算失败，只是没有文件需要处理

            self.log_signal.emit(f"找到 {len(retrieved_files)} 个已检索表格文件，开始图片尺寸修改")

            total_processed = 0
            total_found = 0
            success_count = 0

            # 处理每个已检索表格文件
            for file_path in retrieved_files:
                try:
                    filename = os.path.basename(file_path)
                    self.log_signal.emit(f"正在处理图片尺寸修改: {filename}")

                    # 创建进度回调函数
                    def progress_callback(*args):
                        percentage = args[2] if len(args) > 2 else 0
                        if hasattr(self, 'progress_signal'):
                            self.progress_signal.emit(percentage)
                    
                    # 使用ImageResizeProcessor处理文件
                    success, processed, found, stats = ImageResizeProcessor.process_retrieved_file(
                        file_path,
                        self.base_folder_path,
                        log_callback=self.log_signal.emit,
                        progress_callback=progress_callback,
                        default_dpi=self.default_dpi
                    )

                    if success:
                        success_count += 1
                        total_processed += processed
                        total_found += found
                        
                        # 显示处理统计信息
                        if stats and self.log_signal:
                            self.log_signal.emit(f"  📊 文件处理统计: 用时{stats.get('total_time', 0):.1f}秒，速度{stats.get('average_speed', 0):.1f}行/秒")

                except Exception as e:
                    self.log_signal.emit(f"处理文件 {filename} 的图片尺寸修改时出错: {str(e)}")
                    continue

            # 显示最终统计摘要
            self.log_signal.emit(f"🎯 图片尺寸修改完成!")
            self.log_signal.emit(f"  📁 文件处理: 成功 {success_count}/{len(retrieved_files)} 个")
            self.log_signal.emit(f"  🖼️ 图片统计: 处理 {total_processed} 个，发现 {total_found} 个有效图片")

            if total_processed > 0:
                success_rate = (total_processed / total_found) * 100 if total_found > 0 else 0
                self.log_signal.emit(f"  📊 处理成功率: {success_rate:.1f}%")

            # 添加图片丢失诊断功能
            self._diagnose_image_loss(retrieved_files)

            return success_count > 0  # 至少有一个文件处理成功就算成功

        except Exception as e:
            self.log_signal.emit(f"图片尺寸修改处理失败: {str(e)}")
            return False

    def _diagnose_image_loss(self, retrieved_files):
        """诊断图片丢失情况"""
        try:
            self.log_signal.emit("\n🔍 开始图片丢失诊断...")

            for file_path in retrieved_files:
                filename = os.path.basename(file_path)
                self.log_signal.emit(f"🔍 诊断文件: {filename}")

                # 统计表格中的图片数量和处理状态
                indexed_count = 0
                valid_path_count = 0
                invalid_path_count = 0
                processed_count = 0
                status_stats = {}

                try:
                    # 读取Excel文件的所有sheet
                    xls = pd.ExcelFile(file_path)
                    for sheet_name in xls.sheet_names:
                        if sheet_name == '数据异常':
                            continue

                        df = pd.read_excel(file_path, sheet_name=sheet_name)
                        if df.empty or '图片路径' not in df.columns or '数量' not in df.columns:
                            continue

                        for _, row in df.iterrows():
                            image_path = row.get('图片路径', '')
                            quantity = row.get('数量', 1)
                            pattern_name = row.get('图案', '')
                            processing_status = row.get('处理状态', '')

                            # 跳过无效记录
                            if pd.isna(image_path) or str(image_path).strip() in ['', '未入库', '图案为空']:
                                continue

                            # 确保数量是有效数字
                            try:
                                quantity = int(float(quantity)) if pd.notna(quantity) else 1
                                if quantity <= 0:
                                    quantity = 1
                            except (ValueError, TypeError):
                                quantity = 1

                            indexed_count += quantity

                            # 检查路径有效性
                            if os.path.exists(str(image_path).strip()):
                                valid_path_count += quantity

                                # 检查处理状态
                                status = str(processing_status).strip()
                                if status == '已处理':
                                    processed_count += quantity
                                elif status.startswith('部分成功'):
                                    # 解析部分成功的数量
                                    try:
                                        import re
                                        match = re.search(r'(\d+)/(\d+)', status)
                                        if match:
                                            actual_processed = int(match.group(1))
                                            processed_count += actual_processed
                                            # 记录部分失败的图片
                                            failed_count = quantity - actual_processed
                                            if failed_count > 0:
                                                self._add_missing_image_info(
                                                    pattern_name=str(pattern_name).strip(),
                                                    reason='部分处理失败',
                                                    sheet_name=sheet_name,
                                                    quantity=failed_count,
                                                    image_path=str(image_path).strip(),
                                                    search_method=row.get('查询方式', ''),
                                                    filename=filename
                                                )
                                        else:
                                            processed_count += quantity
                                    except (ValueError, TypeError):
                                        processed_count += quantity
                                elif status in ['处理失败', '文件不存在', '跳过'] or status.startswith('异常:'):
                                    # 记录处理失败的图片
                                    reason = '处理失败' if status == '处理失败' else status
                                    self._add_missing_image_info(
                                        pattern_name=str(pattern_name).strip(),
                                        reason=reason,
                                        sheet_name=sheet_name,
                                        quantity=quantity,
                                        image_path=str(image_path).strip(),
                                        search_method=row.get('查询方式', ''),
                                        filename=filename
                                    )
                                elif status == '':
                                    # 没有处理状态，可能是有效路径但未处理的图片
                                    self._add_missing_image_info(
                                        pattern_name=str(pattern_name).strip(),
                                        reason='未处理',
                                        sheet_name=sheet_name,
                                        quantity=quantity,
                                        image_path=str(image_path).strip(),
                                        search_method=row.get('查询方式', ''),
                                        filename=filename
                                    )

                                # 统计处理状态
                                if status not in status_stats:
                                    status_stats[status] = 0
                                status_stats[status] += quantity

                            else:
                                invalid_path_count += quantity
                                # 收集路径无效的图片信息
                                self._add_missing_image_info(
                                    pattern_name=str(pattern_name).strip(),
                                    reason='路径无效',
                                    sheet_name=sheet_name,
                                    quantity=quantity,
                                    image_path=str(image_path).strip(),
                                    search_method=row.get('查询方式', ''),
                                    filename=filename
                                )

                except Exception as e:
                    self.log_signal.emit(f"  ❌ 读取文件时出错: {str(e)}")
                    continue

                # 显示诊断结果
                self.log_signal.emit(f"📊 索引图片: {indexed_count} 个")
                self.log_signal.emit(f"✅ 有效路径: {valid_path_count} 个")
                self.log_signal.emit(f"❌ 无效路径: {invalid_path_count} 个")
                self.log_signal.emit(f"✅ 已处理图片: {processed_count} 个")

                # 显示处理状态统计
                if status_stats:
                    self.log_signal.emit(f"📋 处理状态统计:")
                    for status, count in status_stats.items():
                        if status == '已处理':
                            self.log_signal.emit(f"  ✅ {status}: {count} 个")
                        elif status.startswith('部分成功'):
                            self.log_signal.emit(f"  ⚠️ {status}: {count} 个")
                        elif status in ['处理失败', '文件不存在', '跳过'] or status.startswith('异常:'):
                            self.log_signal.emit(f"  ❌ {status}: {count} 个")
                        elif status == '':
                            self.log_signal.emit(f"  ⏸️ 未处理: {count} 个")
                        else:
                            self.log_signal.emit(f"  ❓ {status}: {count} 个")

                # 判断是否有丢失
                missing_count = valid_path_count - processed_count
                if missing_count <= 0:
                    self.log_signal.emit("✅ 无丢失图片")
                else:
                    self.log_signal.emit(f"⚠️ 丢失图片: {missing_count} 个")

            # 显示丢失图片详细统计
            if self.missing_images:
                self.log_signal.emit(f"\n📋 未入库图片详细统计:")
                self.log_signal.emit(f"  总计: {len(self.missing_images)} 条记录")

                # 按原因分组统计
                reason_stats = {}
                for missing in self.missing_images:
                    reason = missing.get('丢失原因', missing.get('未入库原因', '未知原因'))
                    quantity = missing['数量']
                    if reason not in reason_stats:
                        reason_stats[reason] = 0
                    reason_stats[reason] += quantity

                for reason, count in reason_stats.items():
                    self.log_signal.emit(f"  - {reason}: {count} 个")

                self.log_signal.emit("  详细信息已记录在'未入库'sheet中")

            self.log_signal.emit("🔍 图片未入库诊断完成")

        except Exception as e:
            self.log_signal.emit(f"❌ 图片未入库诊断失败: {str(e)}")

    def _save_excel_with_sheets(self, output_path, processed_sheets):
        """保存包含多个sheet的Excel文件"""
        try:
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 对sheet进行排序，将"数据异常"放到最后，突出显示成功数据
                sorted_sheets = self._sort_sheets_for_retrieval(processed_sheets)

                # 按照排序后的顺序保存所有sheet
                for sheet_name, df in sorted_sheets:
                    if df is not None and not df.empty:
                        # 保存非空sheet
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
                        self.log_signal.emit(f"已保存sheet: {sheet_name} ({len(df)} 行)")
                    else:
                        # 保存空sheet，创建一个空的DataFrame
                        empty_df = pd.DataFrame()
                        empty_df.to_excel(writer, sheet_name=sheet_name, index=False)
                        self.log_signal.emit(f"已保存空sheet: {sheet_name}")

                # 添加丢失图片sheet
                self._add_missing_images_sheet(writer)

            # 为未入库数据设置黄色背景
            Utils.apply_yellow_background_for_missing_images(output_path)

            self.log_signal.emit(f"成功保存到: {output_path}")
            return True
        except Exception as e:
            self.log_signal.emit(f"保存Excel文件时出错: {str(e)}")
            return False

    def _sort_sheets_for_retrieval(self, processed_sheets):
        """
        对图片检索结果的sheet进行排序，将"数据异常"放到最后，突出显示成功数据
        排序规则：
        1. 数字sheet（如"163", "205"等）排在前面，按数字大小排序
        2. 其他sheet按原始顺序排在中间
        3. "数据异常"相关sheet排在最后
        """
        # 分类sheet
        numeric_sheets = []
        other_sheets = []
        error_sheets = []

        for sheet_name, df in processed_sheets.items():
            if "数据异常" in sheet_name or "异常" in sheet_name:
                error_sheets.append((sheet_name, df))
            elif sheet_name.isdigit():
                # 数字sheet，按数字大小排序
                numeric_sheets.append((int(sheet_name), sheet_name, df))
            else:
                # 其他sheet
                other_sheets.append((sheet_name, df))

        # 排序
        numeric_sheets.sort(key=lambda x: x[0])  # 按数字大小排序

        # 组合结果，数字sheet在前，其他sheet在中间，错误sheet在最后
        result = []

        # 添加数字sheet
        for _, sheet_name, df in numeric_sheets:
            result.append((sheet_name, df))

        # 添加其他sheet
        for sheet_name, df in other_sheets:
            result.append((sheet_name, df))

        # 添加错误sheet（放在最后）
        for sheet_name, df in error_sheets:
            result.append((sheet_name, df))

        return result

    def _add_missing_images_sheet(self, writer):
        """添加未入库图片sheet到Excel文件中"""
        try:
            if self.missing_images:
                # 创建未入库图片DataFrame
                missing_df = pd.DataFrame(self.missing_images)

                # 按文件名和Sheet名称排序
                missing_df = missing_df.sort_values(['文件名', '所在Sheet', '图案名称'])

                # 保存到Excel
                missing_df.to_excel(writer, sheet_name='未入库', index=False)

                # 统计信息
                total_missing = len(self.missing_images)
                total_quantity = missing_df['数量'].sum()

                self.log_signal.emit(f"已保存未入库sheet: {total_missing} 条记录，共 {total_quantity} 个图片")

                # 按原因分组统计
                # 检查列名，使用正确的字段名
                reason_column = '丢失原因' if '丢失原因' in missing_df.columns else '未入库原因'
                if reason_column in missing_df.columns:
                    reason_stats = missing_df.groupby(reason_column)['数量'].sum()
                    for reason, count in reason_stats.items():
                        self.log_signal.emit(f"  - {reason}: {count} 个")

            else:
                # 创建空的未入库图片sheet
                empty_missing_df = pd.DataFrame(columns=['图案名称', '未入库原因', '所在Sheet', '数量', '图片路径', '查询方式', '文件名'])
                empty_missing_df.to_excel(writer, sheet_name='未入库图片', index=False)
                self.log_signal.emit("已保存空的未入库sheet")

        except Exception as e:
            self.log_signal.emit(f"添加未入库sheet时出错: {str(e)}")

# ---------------------------
# 尺寸调整处理器
# ---------------------------
class DimensionAdjustmentProcessor:
    """
    处理表格中宽cm和高cm的尺寸调整
    
    该类负责根据预定义的边界值和增量规则对图片尺寸进行智能调整，
    支持多种调整策略以优化图片尺寸分配和处理效率。
    """

    def __init__(self, boundary1: float = 100, boundary2: float = 240,
                 increment1: float = 0, increment2: float = 1, increment3: float = 1.5):
        """
        初始化尺寸调整处理器

        Args:
            boundary1: 第一个边界值，默认100
            boundary2: 第二个边界值，默认240
            increment1: 第一个增量值，默认0（不增加）
            increment2: 第二个增量值，默认1
            increment3: 第三个增量值，默认1.5
        """
        self.boundary1: float = boundary1
        self.boundary2: float = boundary2
        self.increment1: float = increment1
        self.increment2: float = increment2
        self.increment3: float = increment3

    def adjust_dimensions(self, width_cm: Optional[float], height_cm: Optional[float]) -> Tuple[float, float]:
        """
        根据规则调整宽度和高度

        调整规则：
        - h >= boundary2 → h和w都+increment3
        - w <= boundary1 → h和w都+increment1（不增加）
        - h <= boundary1, w > boundary1 → w+increment2
        - boundary1 < h < boundary2, w >= boundary2 → h和w都+increment2
        - boundary1 < h < boundary2, w < boundary2 → h和w都+increment2

        Args:
            width_cm: 原始宽度(cm)，可以为None
            height_cm: 原始高度(cm)，可以为None

        Returns:
            Tuple[float, float]: (调整后的宽度, 调整后的高度)
        """
        try:
            # 确保输入是数值类型
            w = float(width_cm) if width_cm is not None else 0
            h = float(height_cm) if height_cm is not None else 0

            # 应用调整规则
            if h >= self.boundary2:
                # h >= 240 → h和w都+1.5
                new_w = w + self.increment3
                new_h = h + self.increment3
            elif w <= self.boundary1:
                # w <= 100 → h和w都+0（不增加）
                new_w = w + self.increment1
                new_h = h + self.increment1
            elif h <= self.boundary1 and w > self.boundary1:
                # h <= 100, w > 100 → w+1
                new_w = w + self.increment2
                new_h = h  # h不变
            elif self.boundary1 < h < self.boundary2 and w >= self.boundary2:
                # 100 < h < 240, w >= 240 → h和w都+1
                new_w = w + self.increment2
                new_h = h + self.increment2
            elif self.boundary1 < h < self.boundary2 and w < self.boundary2:
                # 100 < h < 240, w < 240 → h和w都+1
                new_w = w + self.increment2
                new_h = h + self.increment2
            else:
                # 默认情况，不调整
                new_w = w
                new_h = h

            return new_w, new_h

        except (ValueError, TypeError):
            # 如果转换失败，返回原始值
            return width_cm, height_cm

    def process_excel_file(self, excel_path, log_callback=None):
        """
        处理Excel文件中的尺寸调整

        Args:
            excel_path: Excel文件路径
            log_callback: 日志回调函数

        Returns:
            bool: 处理是否成功
        """
        try:
            if log_callback:
                log_callback(f"🔧 开始处理尺寸调整: {os.path.basename(excel_path)}")

            # 读取Excel文件的所有sheet
            xls = pd.ExcelFile(excel_path)
            sheet_names = xls.sheet_names

            if log_callback:
                log_callback(f"  📋 检测到 {len(sheet_names)} 个sheet")

            # 存储所有处理后的sheet数据
            processed_sheets = {}
            total_adjusted = 0

            # 处理每个sheet
            for sheet_name in sheet_names:
                try:
                    df = pd.read_excel(excel_path, sheet_name=sheet_name)

                    if df.empty:
                        processed_sheets[sheet_name] = df
                        continue

                    # 检查是否有宽cm和高cm列
                    width_col = None
                    height_col = None

                    for col in df.columns:
                        if '宽cm' in str(col) or col == '宽':
                            width_col = col
                        elif '高cm' in str(col) or col == '高':
                            height_col = col

                    if width_col is None or height_col is None:
                        if log_callback:
                            log_callback(f"    ⚠️ Sheet {sheet_name} 缺少宽cm或高cm列，跳过")
                        processed_sheets[sheet_name] = df
                        continue

                    # 调整尺寸
                    sheet_adjusted = 0
                    for index, row in df.iterrows():
                        original_width = row[width_col]
                        original_height = row[height_col]

                        # 调整尺寸
                        new_width, new_height = self.adjust_dimensions(original_width, original_height)

                        # 更新数据
                        if new_width != original_width or new_height != original_height:
                            df.at[index, width_col] = new_width
                            df.at[index, height_col] = new_height
                            sheet_adjusted += 1

                    processed_sheets[sheet_name] = df
                    total_adjusted += sheet_adjusted

                    if log_callback and sheet_adjusted > 0:
                        log_callback(f"    ✅ Sheet {sheet_name}: 调整了 {sheet_adjusted} 行数据")

                except Exception as e:
                    if log_callback:
                        log_callback(f"    ❌ 处理Sheet {sheet_name} 时出错: {str(e)}")
                    # 保留原始数据
                    try:
                        processed_sheets[sheet_name] = pd.read_excel(excel_path, sheet_name=sheet_name)
                    except Exception:
                        pass

            # 保存更新后的Excel文件
            with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
                for sheet_name, sheet_df in processed_sheets.items():
                    sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)

            # 为未入库数据设置黄色背景
            Utils.apply_yellow_background_for_missing_images(excel_path)

            if log_callback:
                log_callback(f"  ✅ 尺寸调整完成: 总计调整了 {total_adjusted} 行数据")
                log_callback(f"  💾 已保存更新后的文件: {os.path.basename(excel_path)}")

            return True

        except Exception as e:
            if log_callback:
                log_callback(f"  ❌ 尺寸调整处理失败: {str(e)}")
            return False

# ---------------------------
# 尺寸解析器
# ---------------------------
class SizeParser:
    """
    处理各种尺寸格式的解析器
    
    该类提供静态方法来解析复杂的尺寸字符串，支持多种格式、
    倍数、组合尺寸和中文数量描述等高级功能。
    """

    @staticmethod
    def parse_size(size_text: Optional[str]) -> List[Tuple[float, float, int]]:
        """
        解析各种尺寸格式，支持：
        1. '80*100cm' - 普通尺寸
        2. '50*65cmX2' - 带倍数的尺寸
        3. '50x160+50x80cm' - 多个尺寸组合
        4. '120✘45' - 使用特殊字符作为分隔符
        5. '中间椭圆部分85*136' - 包含描述性文本的尺寸
        6. '60*30cm#1733132032259' - 带时间戳或ID的尺寸
        7. '58*117 刘（客服邀请下单时添加的备注）' - 带备注的尺寸
        8. '梦28*152（客服邀请下单时添加的备注）' - 带前缀和备注的尺寸
        9. '23*64定制一个+40*72两个' - 复杂组合尺寸
        10. '50*120+50*80' - 无单位的组合尺寸

        Args:
            size_text: 尺寸文本字符串，可以为None
            
        Returns:
            List[Tuple[float, float, int]]: 解析结果列表，每个元素是 (宽度, 高度, 数量) 的元组
        """
        if not size_text or not isinstance(size_text, str):
            return [(0, 0, 1)]  # 默认值

        # 清理文本
        size_text = str(size_text).strip()

        # 预处理：移除时间戳、ID等后缀（#开头的数字）
        size_text = re.sub(r'#\d+', '', size_text)

        # 预处理：移除括号内的备注信息
        size_text = re.sub(r'[（(][^）)]*[）)]', '', size_text)

        # 预处理：移除常见的前缀词汇
        size_text = re.sub(r'^(梦|定制|客服|备注)\s*', '', size_text)

        # 1. 检查是否有多个尺寸（用+分隔）
        if '+' in size_text:
            sizes = []
            for part in size_text.split('+'):
                parsed = SizeParser._parse_single_size(part.strip())
                if parsed and parsed != (0, 0, 1):  # 只添加有效解析结果
                    sizes.append(parsed)
            return sizes if sizes else [(0, 0, 1)]

        # 2. 单一尺寸
        parsed = SizeParser._parse_single_size(size_text)
        return [parsed] if parsed else [(0, 0, 1)]

    @staticmethod
    def _parse_single_size(size_text: str) -> Tuple[float, float, int]:
        """
        解析单个尺寸表达式
        
        Args:
            size_text: 单个尺寸文本
            
        Returns:
            Tuple[float, float, int]: (宽度, 高度, 数量) 元组
        """
        # 保存原始文本用于日志
        original_text = size_text

        if not size_text or not isinstance(size_text, str):
            return (0, 0, 1)

        # 转换为小写以便统一处理
        size_text = size_text.lower().strip()

        # 提取数量信息（支持多种格式）
        quantity = 1

        # 1. 匹配 cmX2 或 cm*2 格式的倍数（在单位后面）
        unit_multiplier_match = re.search(r'cm\s*[*xX](\d+)\s*$', size_text)
        if unit_multiplier_match:
            try:
                quantity = int(unit_multiplier_match.group(1))
                size_text = size_text.replace(unit_multiplier_match.group(0), 'cm').strip()
            except ValueError:
                pass

        # 2. 匹配中文数量描述（如：两个、一个）
        chinese_quantity_map = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10}
        for chinese_num, num_val in chinese_quantity_map.items():
            if f'{chinese_num}个' in original_text:
                quantity = num_val
                break

        # 3. 匹配数字+个的格式
        digit_quantity_match = re.search(r'(\d+)个', original_text)
        if digit_quantity_match:
            try:
                quantity = int(digit_quantity_match.group(1))
            except ValueError:
                pass

        # 预处理：移除各种后缀备注和描述
        # 移除中文描述性文字（如：刘、定制一个、两个等）
        size_text = re.sub(r'[一二三四五六七八九十]个?', '', size_text)
        size_text = re.sub(r'定制.*?$', '', size_text)
        size_text = re.sub(r'[\u4e00-\u9fff]+(?![\d\.])', '', size_text)  # 移除不紧邻数字的中文字符

        # 去除单位标识
        size_text = re.sub(r'cm|CM|Cm|cM', '', size_text)

        # 移除常见的描述性文本，但保留数字和分隔符
        descriptive_patterns = [
            r'中间椭圆部分', r'椭圆部分', r'椭圆', r'部分', r'直径', r'圆形', r'圆',
            r'长方形', r'正方形', r'方形', r'客服', r'邀请', r'下单', r'添加', r'备注',
            r'定制', r'梦', r'刘'
        ]
        for pattern in descriptive_patterns:
            size_text = re.sub(pattern, '', size_text, flags=re.IGNORECASE)

        # 清理前后空格和特殊字符
        size_text = re.sub(r'[\s\-_,，。]+', ' ', size_text).strip()

        # 主要解析逻辑：尝试匹配宽x高格式 (支持各种分隔符: *, x, X, ×, ✘)
        # 增强的正则表达式，支持小数点和各种分隔符
        dimensions_match = re.search(r'(\d+(?:\.\d+)?)\s*[xX*×✘]\s*(\d+(?:\.\d+)?)', size_text)
        if dimensions_match:
            try:
                dim1 = float(dimensions_match.group(1))
                dim2 = float(dimensions_match.group(2))

                # 转换为整数（如果是整数值）
                dim1 = int(dim1) if dim1.is_integer() else dim1
                dim2 = int(dim2) if dim2.is_integer() else dim2

                # 优化逻辑：大的数值设置图片实际边长大的边，小的数值设置另一边
                # 这里我们需要根据图片的实际尺寸来决定宽高分配
                # 暂时保持原有的数值大小分配逻辑，后续在图片处理时进行调整
                if dim1 >= dim2:
                    width, height = dim1, dim2
                else:
                    width, height = dim2, dim1

                # 确保返回有效的尺寸值
                if width > 0 and height > 0:
                    return (width, height, quantity)
            except (ValueError, AttributeError):
                pass

        # 尝试匹配只有单一数字的情况（可能是直径或正方形）
        single_number_match = re.search(r'(\d+(?:\.\d+)?)', size_text)
        if single_number_match:
            try:
                number = float(single_number_match.group(1))
                number = int(number) if number.is_integer() else number

                if number > 0:
                    # 只有一个边长表示两边都是这一个值（正方形）
                    # 优化：所有只有一个数字的情况都视为正方形
                    return (number, number, quantity)
            except ValueError:
                pass

        # 尝试从文本中提取两个数字，即使没有明确的分隔符
        all_numbers = re.findall(r'\d+(?:\.\d+)?', size_text)
        if len(all_numbers) >= 2:
            try:
                dim1 = float(all_numbers[0])
                dim2 = float(all_numbers[1])
                dim1 = int(dim1) if dim1.is_integer() else dim1
                dim2 = int(dim2) if dim2.is_integer() else dim2

                # 自动纠正：数值大的放到宽的位置，数值小的放到高的位置
                if dim1 >= dim2:
                    width, height = dim1, dim2
                else:
                    width, height = dim2, dim1

                if width > 0 and height > 0:
                    return (width, height, quantity)
            except (ValueError, IndexError):
                pass

        # 特殊情况：只有一个数字且没有其他信息（正方形）
        if len(all_numbers) == 1:
            try:
                number = float(all_numbers[0])
                number = int(number) if number.is_integer() else number
                if number > 0:
                    # 只有一个边长表示两边都是这一个值（正方形）
                    return (number, number, quantity)
            except ValueError:
                pass

        # 无法解析，返回默认值
        log.debug(f"无法解析尺寸: {original_text}")
        return (0, 0, quantity)

# ---------------------------
# 工作线程
# ---------------------------
class Worker(QThread):
    """
    主工作线程类
    
    负责协调整个图片检索和处理流程，包括TXT文件处理、
    图片索引建立、表格数据处理和图片检索等核心功能。
    继承自QThread以支持后台处理和进度报告。
    
    主要功能：
    - 多表格文件的自动检测和处理
    - 结构化数据（3列：图案、尺寸、数量）和非结构化数据（2列：规格名称、数量）的智能识别
    - OpenAI辅助解析复杂文本格式
    - 材质宽幅配置的动态应用
    - 倍数优化逻辑的智能分配
    - 数据去重合并和排序
    """
    progress = pyqtSignal(int)
    log = pyqtSignal(str)
    finished = pyqtSignal(bool, str)

    def __init__(self, folder_path: str, openai_mgr: Any, is_structured_data: bool = False,
                 column_mapping: Optional[Dict[str, Any]] = None,
                 image_finder_config: Optional[Any] = None,
                 parent: Optional[QObject] = None):
        """
        初始化主工作线程
        
        Args:
            folder_path: 要处理的文件夹路径
            openai_mgr: OpenAI客户端管理器实例
            is_structured_data: 是否为结构化数据模式
            column_mapping: 列映射配置字典，包含材质宽幅配置等
            image_finder_config: 图片查找器配置管理器实例
            parent: 父QObject对象（可选）
        """
        super().__init__(parent)
        self.folder_path: str = folder_path
        self.openai_mgr: Any = openai_mgr
        self.is_structured_data: bool = is_structured_data
        self.column_mapping: Dict[str, Any] = column_mapping or {}
        self.image_finder_config: Optional[Any] = image_finder_config

        # 初始化默认值，确保属性存在
        self.material_keywords: List[str] = []
        self.remove_words: List[str] = []
        self.texture_categories: Dict[str, Any] = {}
        self.sub_to_base_texture: Dict[str, str] = {}
        self.base_texture_mappings: Dict[str, Dict[str, Any]] = {}
        self.global_width_ranges: List[Tuple[float, float, str]] = []
        self.global_sheet_mapping: Dict[int, str] = {}

        # 从UI获取材质宽幅配置
        if isinstance(self.column_mapping, dict):
            # 获取材质宽幅配置
            self.texture_categories = self.column_mapping.get('texture_categories', {})
            self.sub_to_base_texture = self.column_mapping.get('sub_to_base_texture', {})
            self.base_texture_mappings = self.column_mapping.get('base_texture_mappings', {})
            self.global_width_ranges = self.column_mapping.get('global_width_ranges', [])
            self.global_sheet_mapping = self.column_mapping.get('global_sheet_mapping', {})

            # 获取全局宽度参数（用于向后兼容和默认情况）
            self.global_width_arr = self.column_mapping.get('global_width_arr', DEFAULT_WIDTH_VALUES)
            self.width_values = self.global_width_arr  # 设置为全局宽度数组
            self.width_value = self.column_mapping.get('width', DEFAULT_WIDTH)  # 保持向后兼容性
        else:
            # 使用默认配置
            self.global_width_arr = DEFAULT_WIDTH_VALUES
            self.width_values = DEFAULT_WIDTH_VALUES
            self.width_value = DEFAULT_WIDTH

        # 获取无图识别关键字
        self.no_image_keywords: List[str] = self.column_mapping.get('no_image_keywords', [])

        # 简化的宽度设置，不再需要复杂的宽度映射
        self.is_parse_unrecognized: bool = False

        # 存储已处理的文件数据，供图片检索使用
        self.processed_files_data: Dict[str, Dict[str, Any]] = {}

        # 检查OpenAI状态
        if self.openai_mgr and not self.openai_mgr.has_valid_client():
            self.log.emit("OpenAI客户端未初始化，请先配置API密钥")
            return
        elif not self.openai_mgr:
            # 测试模式或没有OpenAI管理器时的处理
            log.warning("OpenAI管理器未提供，某些功能可能受限")

        try:
            # 获取关键字列表
            self.material_keywords = Utils.fetch_material_keywords()
            if not self.material_keywords:
                log.error("无法获取素材名称列表。")
                self.finished.emit(False, "无法获取素材名称列表。")
                return

            # 获取 remove_words
            self.remove_words = Utils.fetch_remove_word_arr()
            if self.remove_words is None:
                log.error("无法获取移除词数组。")
                self.finished.emit(False, "无法获取移除词数组。")
                return

            # 从配置中获取是否解析"未识别"文件夹中的文件
            self.is_parse_unrecognized = Utils.fetch_is_parse_unrecognized()
            log.info(f"is_parse_unrecognized 配置: {self.is_parse_unrecognized}")

            # 初始化解析器
            self.parser: LineParser = LineParser(self.material_keywords, self.remove_words, openai_client_manager=self.openai_mgr, no_image_keywords=self.no_image_keywords)

            # 获取素材分类
            self.texture_categories = Utils.fetch_texture_categories()
            if not self.texture_categories:
                log.error("无法获取素材分类数据。")
                self.finished.emit(False, "无法获取素材分类数据。")
                return

            # 构建子素材到基础素材的映射
            self.sub_to_base_texture = {}
            for base, details in self.texture_categories.items():
                for sub_texture in details['sub_textures']:
                    self.sub_to_base_texture[sub_texture] = base

        except Exception as e:
            log.error(f"Worker初始化过程中发生错误: {str(e)}")
            self.finished.emit(False, f"Worker初始化过程中发生错误: {str(e)}")

    def _get_base_texture_from_material(self, material: str) -> Optional[str]:
        """
        根据材质名称获取基础材质
        
        参照robot_bot_0.0.12.py的逻辑实现
        
        Args:
            material: 材质名称
            
        Returns:
            Optional[str]: 基础材质名称，如果未找到则返回None
        """
        if not material or not hasattr(self, 'sub_to_base_texture'):
            return None

        # 直接查找
        if material in self.sub_to_base_texture:
            return self.sub_to_base_texture[material]



        log.debug(f"材质 {material} 未找到对应的基础材质")
        return None


    def check_no_image_pattern(self, size_text: Optional[str]) -> bool:
        """
        检查尺寸文本是否包含无图识别关键字
        
        Args:
            size_text: 尺寸文本
            
        Returns:
            bool: 如果包含无图关键字则返回True，否则返回False
        """
        if not size_text or not self.no_image_keywords:
            return False

        size_text = str(size_text).strip()
        for keyword in self.no_image_keywords:
            if keyword.strip() and keyword.strip() in size_text:
                return True
        return False

    def run(self):
        try:
            # 查找所有表格文件
            self.log.emit(f"🔍 开始扫描文件夹: {self.folder_path}")
            spreadsheet_files = []
            all_files = []
            excel_files = []

            for root, _, files in os.walk(self.folder_path):
                # 处理顶层目录和子目录中的文件
                relative_path = os.path.relpath(root, self.folder_path)
                if relative_path == ".":
                    self.log.emit(f"📁 扫描主目录: {root}")
                else:
                    self.log.emit(f"📁 扫描子目录: {relative_path}")

                self.log.emit(f"📄 发现 {len(files)} 个文件")

                for file in files:
                    all_files.append(file)
                    file_lower = file.lower()

                    # 检查是否为表格文件
                    if file_lower.endswith(('.xlsx', '.xls', '.csv')):
                        excel_files.append(file)
                        full_path = os.path.join(root, file)
                        self.log.emit(f"📊 发现表格文件: {file} (位置: {relative_path if relative_path != '.' else '主目录'})")

                        # 检查是否应该排除
                        if "_已检索" in file_lower:
                            self.log.emit(f"❌ 排除已检索文件: {file}")
                        else:
                            spreadsheet_files.append(full_path)
                            self.log.emit(f"✅ 包含表格文件: {file}")

            # 详细的调试信息
            self.log.emit(f"📊 文件扫描结果:")
            self.log.emit(f"  总文件数: {len(all_files)}")
            self.log.emit(f"  表格文件数: {len(excel_files)}")
            self.log.emit(f"  可处理文件数: {len(spreadsheet_files)}")

            if len(all_files) > 0:
                self.log.emit(f"📋 所有文件列表:")
                for file in all_files[:10]:  # 只显示前10个文件
                    self.log.emit(f"  • {file}")
                if len(all_files) > 10:
                    self.log.emit(f"  ... 还有 {len(all_files) - 10} 个文件")

            if len(excel_files) > 0:
                self.log.emit(f"📋 表格文件详情:")
                for file in excel_files:
                    file_lower = file.lower()
                    status = "✅ 可处理"
                    if "_已检索" in file_lower:
                        status = "❌ 已检索文件"
                    self.log.emit(f"  • {file} - {status}")

            total_files = len(spreadsheet_files)
            if total_files == 0:
                # 检查是否有TXT文件，提供更智能的提示
                txt_files_found = []
                for root, _, files in os.walk(self.folder_path):
                    for file in files:
                        if file.lower().endswith('.txt'):
                            txt_files_found.append(file)

                self.log.emit("=" * 60)
                self.log.emit("❌ 没有找到可处理的表格文件")

                if txt_files_found:
                    self.log.emit("🔍 但是发现了TXT文件，可以先进行解析:")
                    for txt_file in txt_files_found[:5]:  # 只显示前5个
                        self.log.emit(f"  • {txt_file}")
                    if len(txt_files_found) > 5:
                        self.log.emit(f"  ... 还有 {len(txt_files_found) - 5} 个TXT文件")

                    self.log.emit("")
                    self.log.emit("💡 建议操作步骤:")
                    self.log.emit("  1️⃣ 点击 'TXT文件解析' 按钮")
                    self.log.emit("  2️⃣ 等待解析完成，生成总操作表格")
                    self.log.emit("  3️⃣ 解析完成后会自动询问是否继续图片处理")
                    self.log.emit("")
                    self.log.emit("🚀 使用 'TXT文件解析' 功能即可完成整个流程")
                else:
                    self.log.emit("📋 支持的文件格式: .xlsx, .xls, .csv")
                    self.log.emit("📋 支持的文件类型:")
                    self.log.emit("  • 原始Excel表格文件")
                    self.log.emit("  • TXT解析生成的总操作表格文件")
                    self.log.emit("📋 排除的文件类型:")
                    self.log.emit("  • *_已检索.xlsx (已检索的结果文件)")

                self.log.emit("=" * 60)

                if txt_files_found:
                    self.finished.emit(False, f"发现 {len(txt_files_found)} 个TXT文件，请先点击 'TXT文件解析' 按钮进行解析。")
                else:
                    self.finished.emit(False, "没有找到可处理的表格文件。请确保文件夹中包含Excel表格文件或TXT文件。")
                return

            log.info(f"开始处理 {total_files} 个表格文件。")
            self.log.emit(f"🚀 开始多表格处理模式，共发现 {total_files} 个表格文件")
            self.log.emit(f"📋 表格文件列表:")
            for i, file_path in enumerate(spreadsheet_files, 1):
                self.log.emit(f"  {i}. {os.path.basename(file_path)}")

            processed_files = 0
            successful_files = 0
            failed_files = 0

            # 总行数计数器（用于更精确的进度追踪）
            total_rows = 0
            processed_rows = 0

            # 首先快速统计所有文件的总行数
            self.log.emit("📊 正在统计所有表格文件的数据量...")
            for file_path in spreadsheet_files:
                try:
                    df = Utils.read_spreadsheet(file_path)
                    if df is not None:
                        file_rows = len(df)
                        total_rows += file_rows
                        self.log.emit(f"  📄 {os.path.basename(file_path)}: {file_rows} 行数据")
                except Exception as e:
                    log.warning(f"统计行数时出错: {file_path}, {str(e)}")
                    self.log.emit(f"  ⚠️ {os.path.basename(file_path)}: 无法读取文件")

            self.log.emit(f"📈 统计完成，总计 {total_rows} 行数据需要处理")
            self.log.emit("=" * 60)

            # 发送初始总进度信号
            self.progress.emit(0)

            # 改为串行处理，确保按顺序处理每个表格
            for i, file_path in enumerate(spreadsheet_files, 1):
                try:
                    filename = os.path.basename(file_path)
                    self.log.emit(f"🔄 正在处理第 {i}/{total_files} 个表格: {filename}")

                    # 处理单个表格文件
                    result, rows_processed = self.process_spreadsheet(file_path, total_files, total_rows)

                    if result:
                        successful_files += 1
                        log.info(f"成功处理文件：{file_path}")
                        self.log.emit(f"✅ 第 {i} 个表格处理成功: {filename}")
                    else:
                        failed_files += 1
                        log.warning(f"处理失败文件：{file_path}")
                        self.log.emit(f"❌ 第 {i} 个表格处理失败: {filename}")

                    processed_files += 1
                    processed_rows += rows_processed

                    # 计算总进度（基于文件数量）
                    file_progress = int((processed_files / total_files) * 100)
                    self.progress.emit(file_progress)

                    # 更新状态信息
                    self.log.emit(f"📊 当前进度: {processed_files}/{total_files} 个文件已处理 (成功:{successful_files}, 失败:{failed_files})")
                    self.log.emit("-" * 40)

                except Exception as e:
                    failed_files += 1
                    log.error(f"处理文件 {file_path} 时出错: {str(e)}")
                    self.log.emit(f"❌ 处理文件 {os.path.basename(file_path)} 时出错: {str(e)}")
                    processed_files += 1

            # 处理完成
            self.log.emit("=" * 60)
            self.log.emit(f"🎉 多表格处理完成！")
            self.log.emit(f"📊 处理统计:")
            self.log.emit(f"  • 总文件数: {total_files}")
            self.log.emit(f"  • 成功处理: {successful_files}")
            self.log.emit(f"  • 处理失败: {failed_files}")
            self.log.emit(f"  • 总数据行: {processed_rows}")

            if successful_files > 0:
                success_rate = (successful_files / total_files) * 100
                self.log.emit(f"  • 成功率: {success_rate:.1f}%")
                self.finished.emit(True, f"成功处理 {successful_files}/{total_files} 个表格文件")
            else:
                self.log.emit(f"  • 所有表格处理失败")
                self.finished.emit(False, "所有表格文件处理失败")

        except Exception as e:
            log.error(f"多表格处理过程中出错: {str(e)}")
            self.log.emit(f"❌ 多表格处理过程中出错: {str(e)}")
            self.finished.emit(False, f"处理失败: {str(e)}")

    def process_spreadsheet(self, file_path: str, total_files: int, total_rows: int) -> None:
        """
        处理单个表格文件，根据列数自动检测数据类型
        
        3列视为结构化数据，2列视为非结构化数据
        
        Args:
            file_path: 表格文件路径
            total_files: 总文件数量（暂未使用）
            total_rows: 总行数（暂未使用）
        """
        try:
            # 获取文件基础名称（不含路径和扩展名）
            base_name = os.path.basename(file_path)

            # 创建输出文件名
            output_file = Utils.get_output_excel_path(file_path)
            if not output_file:
                self.log.emit(f"无法生成输出文件路径，跳过处理: {base_name}")
                return False, 0

            self.log.emit(f"开始处理表格文件: {base_name}")

            # 读取表格数据
            df = Utils.read_spreadsheet(file_path)
            if df is None or df.empty:
                self.log.emit(f"表格文件 {base_name} 为空或无法读取，跳过处理")
                return False, 0

            # 获取文件的实际行数
            actual_row_count = len(df)

            # 初始化错误sheet名称为"数据异常"
            error_sheet = "数据异常"

            # 动态收集所有需要的宽度配置
            all_width_values = set()

            # 添加全局宽度配置
            if hasattr(self, 'global_width_arr') and self.global_width_arr:
                all_width_values.update(self.global_width_arr)
            else:
                all_width_values.update(DEFAULT_WIDTH_VALUES)

            # 添加所有材质的专用宽度配置
            if hasattr(self, 'base_texture_mappings'):
                for _, mappings in self.base_texture_mappings.items():
                    width_ranges = mappings.get('width_ranges', [])
                    for _, upper, _ in width_ranges:
                        if upper != float('inf'):
                            all_width_values.add(upper)

            # 转换为排序的列表
            width_values = sorted(list(all_width_values))

            # 创建多个sheet名称：每个宽度值一个sheet，加上超宽幅和数据异常
            sheet_names = [str(w) for w in width_values] + ["超宽幅", error_sheet]

            # 初始化sheet_data；每个sheet存储对应的数据
            sheet_data = {sheet: [] for sheet in sheet_names}

            self.log.emit(f"动态收集的宽度值: {width_values}，将创建sheet: {sheet_names[:-1]}")  # 不显示数据异常sheet
            if hasattr(self, 'base_texture_mappings') and self.base_texture_mappings:
                self.log.emit(f"已加载 {len(self.base_texture_mappings)} 个材质的专用宽度配置")

            # 为当前文件创建独立的OpenAI会话
            local_openai_mgr = self.openai_mgr.clone()
            local_parser = LineParser(self.material_keywords, self.remove_words, openai_client_manager=local_openai_mgr, no_image_keywords=self.no_image_keywords)

            self.log.emit(f"表格有 {actual_row_count} 行数据需要处理")

            # 自动检测数据类型
            # 检测A-M列中有效数据列的数量
            valid_cols = 0
            valid_col_indices = []

            # 只检查前13列（A-M）
            max_col_check = min(13, len(df.columns))
            for col_idx in range(max_col_check):
                # 检查列是否包含足够的非空数据
                non_empty_count = df.iloc[:, col_idx].notna().sum()
                if non_empty_count > 0.1 * len(df):  # 如果超过10%的数据非空
                    valid_cols += 1
                    valid_col_indices.append(col_idx)

            self.log.emit(f"检测到 {valid_cols} 列有效数据")

            # 根据有效列数确定处理方式
            if valid_cols == 3:
                # 3列有效数据，视为结构化数据，顺序为：图案、尺寸、数量
                self.log.emit("检测到3列有效数据，按结构化数据处理（图案、尺寸、数量）")

                # 更新列映射
                self.column_mapping = {
                    'pattern_column': chr(65 + valid_col_indices[0]),  # 图案列
                    'size_column': chr(65 + valid_col_indices[1]),     # 尺寸列
                    'quantity_column': chr(65 + valid_col_indices[2])  # 数量列
                }

                # 使用结构化数据处理
                self.process_structured_data_simplified(df, sheet_data, width_values, error_sheet)

            elif valid_cols == 2:
                # 2列有效数据，视为非结构化数据，顺序为：规格名称、数量
                self.log.emit("检测到2列有效数据，按非结构化数据处理（规格名称、数量）")

                # 更新列映射
                self.column_mapping = {
                    'unstructured': True,
                    'spec_column': chr(65 + valid_col_indices[0]),     # 规格名称列
                    'quantity_column': chr(65 + valid_col_indices[1])  # 数量列
                }

                # 使用非结构化数据处理（OpenAI解析）
                self.process_unstructured_data_simplified(df, sheet_data, width_values, error_sheet, local_parser, base_name)

            else:
                # 其他情况，尝试OpenAI解析
                self.log.emit(f"检测到 {valid_cols} 列有效数据，尝试使用OpenAI解析")
                self.process_unstructured_data_simplified(df, sheet_data, width_values, error_sheet, local_parser, base_name)

            # 创建Excel工作簿并保存数据
            try:
                self.save_to_excel(output_file, sheet_data)
                self.log.emit(f"表格文件 {base_name} 处理完成，已保存到 {output_file}")

                # 记录已处理的文件数据，供图片检索使用
                self.processed_files_data[file_path] = {
                    'output_file': output_file,
                    'sheet_data': sheet_data,
                    'row_count': actual_row_count,
                    'column_mapping': self.column_mapping.copy() if hasattr(self, 'column_mapping') else {}
                }

                return True, actual_row_count
            except Exception as e:
                self.log.emit(f"保存Excel文件时出错: {str(e)}")
                return False, 0

        except Exception as e:
            log.error(f"处理表格文件 {file_path} 时出错: {str(e)}")
            self.log.emit(f"处理表格文件 {file_path} 时出错: {str(e)}")
            # 捕获到异常并记录日志，但依然返回False表示处理失败
            return False, 0

    def process_structured_data_simplified(self, df: pd.DataFrame, sheet_data: Dict[str, List[Dict[str, Any]]],
                                         width_values: List[float], error_sheet: List[Dict[str, Any]]) -> None:
        """
        处理已结构化的表格数据，支持多宽度参数和智能sheet分类
        
        直接处理图案、尺寸、数量三列数据
        
        Args:
            df: pandas DataFrame对象，包含结构化数据
            sheet_data: 按sheet名称分组的数据字典
            width_values: 宽度值列表
            error_sheet: 错误数据收集列表
        """
        try:
            # 获取用户指定的默认宽度值（使用第一个宽度值作为默认值）
            default_width = width_values[0] if width_values else 163

            # 将Excel列标识(A,B,C...)转换为pandas列索引(0,1,2...)
            def excel_col_to_index(col_letter):
                col_letter = col_letter.upper()
                result = 0
                for c in col_letter:
                    result = result * 26 + (ord(c) - ord('A') + 1)
                return result - 1  # 转为0-based索引

            # 获取列映射
            pattern_col = self.column_mapping.get('pattern_column', 'A')
            size_col = self.column_mapping.get('size_column', 'B')
            quantity_col = self.column_mapping.get('quantity_column', 'C')

            # 转换列标识为索引
            try:
                pattern_idx = excel_col_to_index(pattern_col)
                size_idx = excel_col_to_index(size_col)
                quantity_idx = excel_col_to_index(quantity_col)

                # 确保索引在有效范围内
                max_col = max(pattern_idx, size_idx, quantity_idx)
                if max_col >= len(df.columns):
                    self.log.emit(f"列映射超出表格范围，表格仅有 {len(df.columns)} 列")
                    # 尝试使用列名映射
                    self.process_unstructured_data_simplified(df, sheet_data, width_values, error_sheet, self.parser, "")
                    return
            except Exception as e:
                self.log.emit(f"列映射转换失败: {str(e)}，将使用OpenAI解析")
                self.process_unstructured_data_simplified(df, sheet_data, width_values, error_sheet, self.parser, "")
                return

            # 处理每一行数据
            for _, row in df.iterrows():
                try:
                    # 直接获取原始数据
                    pattern = str(row.iloc[pattern_idx]) if pattern_idx < len(row) else ""
                    size_text = str(row.iloc[size_idx]) if size_idx < len(row) else ""
                    quantity_text = str(row.iloc[quantity_idx]) if quantity_idx < len(row) else "1"

                    # 清理数据
                    pattern = pattern.strip() if not pd.isna(pattern) else ""
                    size_text = size_text.strip() if not pd.isna(size_text) else ""
                    quantity_text = quantity_text.strip() if not pd.isna(quantity_text) else "1"

                    # 构建原始数据行，用于日志记录
                    original_data = f"图案: {pattern}, 尺寸: {size_text}, 数量: {quantity_text}"

                    # 获取基础数量
                    base_quantity = 1
                    try:
                        base_quantity = int(float(quantity_text)) if quantity_text else 1
                        if base_quantity <= 0:
                            base_quantity = 1
                    except (ValueError, TypeError):
                        base_quantity = 1

                    # 直接使用正则表达式解析尺寸
                    width, height = self._parse_size_directly(size_text, default_width)

                    # 检查是否为无图模式
                    is_no_image = self.check_no_image_pattern(size_text)
                    if is_no_image:
                        pattern = "无图"

                    # 如果图案为空，添加到错误sheet
                    if not pattern:
                        error_record = {
                            '原始数据': original_data,
                            '错误原因': "结构化数据缺少必要信息",
                            '错误日志': "图案为空",
                            '数据处理流程': "结构化数据处理",
                            '解析错误': "结构化数据缺少必要信息"
                        }
                        sheet_data[error_sheet].append(error_record)
                        continue

                    # 如果无法解析尺寸，添加到错误sheet
                    if width == 0 and height == 0:
                        error_record = {
                            '原始数据': original_data,
                            '错误原因': "无法解析尺寸信息",
                            '错误日志': f"尺寸文本: {size_text}",
                            '数据处理流程': "结构化数据处理",
                            '解析错误': "无法从尺寸文本提取有效数值"
                        }
                        sheet_data[error_sheet].append(error_record)
                        continue

                    # 应用边长增加逻辑
                    width, height = self._apply_edge_increment(width, height)

                    # 创建记录
                    entry = {
                        '图案': pattern,
                        '宽cm': width,
                        '高cm': height,
                        '数量': base_quantity,
                        '处理方式': '结构化数据处理',
                        '原始数据': original_data
                    }

                    # 根据宽度和高度智能分配到对应的sheet，使用robot_bot_0.0.12的逻辑
                    material = ""
                    match = self.parser.material_patterns.match(pattern)
                    if match:
                        material = match.group(0).rstrip('-丨')

                    target_sheet = self.determine_sheet(
                        width=width,
                        material=material,
                        height=height,
                        quantity=base_quantity
                    )

                    # 处理SPLIT逻辑（倍数优化分割）
                    if target_sheet and target_sheet.startswith("SPLIT:"):
                        # 解析SPLIT标记：SPLIT:target_width:optimized_quantity:remaining_quantity
                        parts = target_sheet.split(":")
                        if len(parts) == 4:
                            split_target_width = parts[1]
                            optimized_quantity = int(parts[2])
                            remaining_quantity = int(parts[3])

                            # 创建优化数量的条目
                            if optimized_quantity > 0:
                                optimized_entry = entry.copy()
                                optimized_entry['数量'] = optimized_quantity
                                if split_target_width in sheet_data:
                                    sheet_data[split_target_width].append(optimized_entry)
                                    self.log.emit(f"倍数优化: {optimized_quantity}个图片分配到sheet {split_target_width}")

                            # 创建剩余数量的条目，使用原始逻辑分配
                            if remaining_quantity > 0:
                                remaining_entry = entry.copy()
                                remaining_entry['数量'] = remaining_quantity
                                # 重新计算sheet，但不使用倍数优化（传入quantity=1避免再次触发倍数优化）
                                original_sheet = self.determine_sheet(width, material, height=height, quantity=1)
                                if original_sheet and not original_sheet.startswith("SPLIT:"):
                                    if original_sheet in sheet_data:
                                        sheet_data[original_sheet].append(remaining_entry)
                                        self.log.emit(f"剩余数量: {remaining_quantity}个图片分配到sheet {original_sheet}")
                                else:
                                    # 如果无法分配，使用第一个宽度sheet
                                    first_width_sheet = str(width_values[0])
                                    sheet_data[first_width_sheet].append(remaining_entry)
                                    self.log.emit(f"剩余数量: {remaining_quantity}个图片分配到默认sheet {first_width_sheet}")
                        continue  # 跳过后续的普通添加逻辑

                    # 添加到对应的sheet
                    if target_sheet and target_sheet in sheet_data:
                        sheet_data[target_sheet].append(entry)
                    else:
                        # 如果目标sheet不存在，添加到第一个宽度sheet
                        first_width_sheet = str(width_values[0])
                        sheet_data[first_width_sheet].append(entry)

                except Exception as e:
                    error_record = {
                        '原始数据': str(row),
                        '错误原因': f"处理结构化数据出错: {str(e)}",
                        '错误日志': traceback.format_exc(),
                        '数据处理流程': "结构化数据处理",
                        '解析错误': f"处理结构化数据出错: {str(e)}"
                    }
                    sheet_data[error_sheet].append(error_record)
            
            # 数据去重合并：对每个宽度sheet分别进行去重合并
            for width_val in width_values:
                width_sheet = str(width_val)
                if width_sheet in sheet_data:
                    self._merge_duplicate_records(sheet_data, width_sheet)

            # 对超宽幅sheet也进行去重合并
            if "超宽幅" in sheet_data:
                self._merge_duplicate_records(sheet_data, "超宽幅")

        except Exception as e:
            self.log.emit(f"结构化数据处理失败: {str(e)}，将使用OpenAI解析")
            # 如果结构化数据处理失败，回退到OpenAI解析
            self.process_unstructured_data_simplified(df, sheet_data, width_values, error_sheet, self.parser, "")

    def _parse_size_directly(self, size_text: str, default_width: float = 200) -> Tuple[float, float]:
        """
        直接使用正则表达式解析尺寸文本，提取宽度和高度

        Args:
            size_text: 尺寸文本
            default_width: 默认宽度（暂未使用，保留用于向后兼容）

        Returns:
            Tuple[float, float]: (宽度, 高度) 元组
        """
        if not size_text or not isinstance(size_text, str):
            return (0, 0)

        # 清理文本
        size_text = str(size_text).strip()

        # 移除描述性文本
        descriptive_patterns = [
            '中间椭圆部分',
            '椭圆',
            '部分',
            '直径',
            '圆形',
            '圆'
        ]
        for pattern in descriptive_patterns:
            size_text = size_text.replace(pattern, '')

        # 清理前后空格
        size_text = size_text.strip()

        # 尝试匹配宽x高格式 (支持各种分隔符: *, x, X, ×, ✘)
        dimensions_match = re.search(r'(\d+(?:\.\d+)?)\s*[xX*×✘]\s*(\d+(?:\.\d+)?)', size_text)
        if dimensions_match:
            try:
                # 支持小数点
                dim1 = float(dimensions_match.group(1))
                dim2 = float(dimensions_match.group(2))
                # 转换为整数（如果是整数值）
                dim1 = int(dim1) if dim1.is_integer() else dim1
                dim2 = int(dim2) if dim2.is_integer() else dim2

                # 优化逻辑：大的数值设置图片实际边长大的边，小的数值设置另一边
                # 这里我们需要根据图片的实际尺寸来决定宽高分配
                # 暂时保持原有的数值大小分配逻辑，后续在图片处理时进行调整
                if dim1 >= dim2:
                    width, height = dim1, dim2
                else:
                    width, height = dim2, dim1

                return (width, height)
            except (ValueError, AttributeError):
                pass

        # 尝试从文本中提取两个数字，即使没有明确的分隔符
        all_numbers = re.findall(r'\d+(?:\.\d+)?', size_text)
        if len(all_numbers) >= 2:
            try:
                dim1 = float(all_numbers[0])
                dim2 = float(all_numbers[1])
                dim1 = int(dim1) if dim1.is_integer() else dim1
                dim2 = int(dim2) if dim2.is_integer() else dim2

                # 优化逻辑：大的数值设置图片实际边长大的边，小的数值设置另一边
                # 这里我们需要根据图片的实际尺寸来决定宽高分配
                # 暂时保持原有的数值大小分配逻辑，后续在图片处理时进行调整
                if dim1 >= dim2:
                    width, height = dim1, dim2
                else:
                    width, height = dim2, dim1

                return (width, height)
            except (ValueError, IndexError):
                pass

        # 尝试匹配只有单一数字的情况（正方形）
        single_number_match = re.search(r'(\d+(?:\.\d+)?)', size_text)
        if single_number_match:
            try:
                number = float(single_number_match.group(1))
                number = int(number) if number.is_integer() else number

                # 只有一个边长表示两边都是这一个值（正方形）
                # 优化：所有只有一个数字的情况都视为正方形
                return (number, number)
            except ValueError:
                pass

        # 无法解析，返回默认值
        log.debug(f"无法解析尺寸: {size_text}")
        return (0, 0)

    def process_unstructured_data_simplified(self, df: pd.DataFrame, sheet_data: Dict[str, List[Dict[str, Any]]],
                                           width_values: List[float], error_sheet: List[Dict[str, Any]],
                                           local_parser: LineParser, file_name: str) -> None:
        """
        使用OpenAI处理非结构化表格数据，支持多宽度参数和智能sheet分类
        
        仅处理图案、尺寸、数量三个关键字段
        
        Args:
            df: pandas DataFrame对象，包含非结构化数据
            sheet_data: 按sheet名称分组的数据字典
            width_values: 宽度值列表
            error_sheet: 错误数据收集列表
            local_parser: LineParser实例，用于解析数据
            file_name: 文件名称
        """
        # 按批次处理数据，使用100行一组
        batch_size = 100
        total_rows = len(df)
        row_results = []

        # 获取用户指定的默认宽度值（使用第一个宽度值作为默认值）
        default_width = width_values[0] if width_values else 163

        # 检查是否有指定列配置
        has_column_config = isinstance(self.column_mapping, dict) and self.column_mapping.get('unstructured', False)

        # 辅助函数，转换Excel列标识到索引
        def excel_col_to_index(col_letter):
            if not col_letter:
                return None
            col_letter = col_letter.upper()
            result = 0
            for c in col_letter:
                result = result * 26 + (ord(c) - ord('A') + 1)
            return result - 1  # 转为0-based索引

        # 获取列配置
        spec_idx = None
        quantity_idx = None

        if has_column_config:
            try:
                spec_col = self.column_mapping.get('spec_column', '')
                quantity_col = self.column_mapping.get('quantity_column', '')

                if spec_col:
                    spec_idx = excel_col_to_index(spec_col)
                if quantity_col:
                    quantity_idx = excel_col_to_index(quantity_col)

                self.log.emit(f"使用指定列: 规格名称列({spec_col}), 数量列({quantity_col})")
            except Exception as e:
                self.log.emit(f"列索引转换失败: {str(e)}")
                spec_idx = None
                quantity_idx = None

        # 准备所有批次任务
        all_batches = []
        for start_idx in range(0, total_rows, batch_size):
            end_idx = min(start_idx + batch_size, total_rows)
            batch_rows = []

            # 收集当前批次的行
            for idx in range(start_idx, end_idx):
                row = df.iloc[idx]
                # 处理指定列
                if has_column_config and spec_idx is not None:
                    # 只合并指定的列
                    row_data = []

                    # 添加规格列
                    if spec_idx < len(row):
                        spec_value = row.iloc[spec_idx]
                        if pd.notna(spec_value):
                            row_data.append(f"规格: {spec_value}")

                    # 添加数量列
                    if quantity_idx is not None and quantity_idx < len(row):
                        quantity_value = row.iloc[quantity_idx]
                        if pd.notna(quantity_value):
                            row_data.append(f"数量: {quantity_value}")

                    # 合并列数据
                    row_str = ' '.join(row_data)
                else:
                    # 将整行转换为字符串
                    row_str = ' '.join([f"{col}: {val}" for col, val in row.items() if pd.notna(val)])

                batch_rows.append((idx + 1, row_str))  # (行号, 行文本)

            # 构建批次请求内容
            batch_text = "\n---行分隔符---\n".join([f"行 {row_num}: {row_text}" for row_num, row_text in batch_rows])
            all_batches.append((batch_rows, batch_text, start_idx, end_idx))

        # 计算最佳并行请求数
        qpm = getattr(local_parser.openai_mgr, '_qpm', 0)
        max_parallel = min(10, max(1, qpm // 6) if qpm > 0 else 5)  # 预留一些QPM空间，避免触发限制

        self.log.emit(f"将使用最多 {max_parallel} 个并行批处理任务处理 {total_rows} 行数据")

        # 使用并行处理批次
        active_batches = 0
        batch_index = 0
        batch_results = []

        # 创建一个简单的信号量来限制并发数
        semaphore = threading.Semaphore(max_parallel)
        completion_event = threading.Event()

        def process_batch(batch_data, batch_idx):
            batch_rows, batch_text, start_idx, end_idx = batch_data
            try:
                self.log.emit(f"批量处理第 {start_idx+1} 到 {end_idx} 行数据 (批次 {batch_idx+1}/{len(all_batches)})")

                # 发送批量请求到OpenAI
                results = local_parser.parse_with_openai(
                    batch_text,
                    f"{start_idx+1}-{end_idx}",
                    file_name
                )

                # 创建行到结果的映射
                row_result_mapping = {}

                # 首先检查结果是否有效 - 修复验证逻辑
                if results and not all(entry.get('解析错误') for entry in results):
                    # 对每个结果项进行处理并与行关联
                    for entry in results:
                        # 修复验证逻辑 - 检查字典中是否包含解析错误字段
                        if not entry.get('解析错误'):
                            # 尝试获取行号
                            line_num = entry.get('行号', 0)

                            # 将结果与相应的行关联
                            if line_num > 0 and line_num <= len(batch_rows):
                                row_idx = line_num - 1  # 调整为0索引
                                if row_idx < len(batch_rows):
                                    row_num, row_text = batch_rows[row_idx]
                                    if row_num not in row_result_mapping:
                                        row_result_mapping[row_num] = []
                                    row_result_mapping[row_num].append(entry)

                # 创建最终批次结果
                for _, (row_num, row_text) in enumerate(batch_rows):
                    if row_num in row_result_mapping and row_result_mapping[row_num]:
                        # 有关联的解析结果
                        row_entries = row_result_mapping[row_num]
                        row_results.append((row_num, row_entries, row_text))
                    else:
                        # 没有找到解析结果，创建错误记录
                        error_record = {
                            '原始数据': row_text,
                            '错误原因': "批量解析中未找到该行的有效数据",
                            '错误日志': "OpenAI返回的数据无法与行匹配",
                            '数据处理流程': "OpenAI批量解析",
                            '解析错误': "批量解析中未找到行匹配数据"
                        }
                        sheet_data[error_sheet].append(error_record)

                batch_results.append((batch_idx, batch_rows, results))

            except Exception as e:
                self.log.emit(f"批次 {batch_idx+1} 处理失败: {str(e)}")
                # 为批次中的每一行创建错误记录
                error_records = []
                for row_num, row_text in batch_rows:
                    error_record = {
                        '原始数据': row_text,
                        '错误原因': f"批量处理异常: {str(e)}",
                        '错误日志': traceback.format_exc(),
                        '数据处理流程': "OpenAI批量解析",
                        '解析错误': f"批量处理失败: {str(e)}"
                    }
                    error_records.append((row_num, error_record))

                batch_results.append((batch_idx, batch_rows, None, error_records))
            finally:
                semaphore.release()

                # 检查是否所有批次已完成
                nonlocal active_batches
                active_batches -= 1
                if active_batches == 0 and batch_index >= len(all_batches):
                    completion_event.set()

        # 启动批处理任务
        while batch_index < len(all_batches) or active_batches > 0:
            # 如果还有批次要处理并且没有达到并行上限
            if batch_index < len(all_batches) and semaphore.acquire(blocking=False):
                active_batches += 1
                threading.Thread(
                    target=process_batch,
                    args=(all_batches[batch_index], batch_index),
                    daemon=True
                ).start()
                batch_index += 1
            elif active_batches > 0:
                # 等待有批次完成
                time.sleep(0.1)
                # 更新进度
                # progress_value = min(100, int((batch_index - active_batches) / len(all_batches) * 100))
                # self.progress.emit(progress_value)
            else:
                # 所有批次已完成
                break

        # 等待所有批次完成
        if not completion_event.is_set():
            completion_event.wait()

        # 按批次索引排序结果
        batch_results.sort(key=lambda x: x[0])

        # 处理所有批次结果
        for _, batch_rows, results, *error_records in batch_results:
            if results is None and error_records:
                # 批次处理失败，添加错误记录
                for _, error_record in error_records[0]:
                    sheet_data[error_sheet].append(error_record)
                continue

            # 如果results不为空但全部是错误记录，也直接添加到错误sheet - 修复验证逻辑
            if results and all(entry.get('解析错误') for entry in results):
                for entry in results:
                    sheet_data[error_sheet].append(entry)
                continue

            # 常规批量结果处理
            if results:
                # 使用预处理的row_results，这些结果已经在process_batch中处理好了
                # 不需要额外的处理逻辑
                pass

        # 设置最终进度为100%
        self.progress.emit(100)

        # 处理解析的结果
        self.log.emit(f"批量解析完成，共处理 {total_rows} 行，成功解析 {len(row_results)} 条")

        # 按行号排序结果
        row_results.sort(key=lambda x: x[0])

        # 处理每行数据
        for _, parsed_entries, original_row in row_results:
            # 修复验证逻辑 - 检查字典中是否包含解析错误字段
            valid_entries = [entry for entry in parsed_entries if not entry.get('解析错误')]

            # 进一步检查若有效结果中高、宽均为空（即未解析出关键数值），则判定为异常
            filtered_valid_entries = []
            for entry in valid_entries:
                width = entry.get('宽cm')
                height = entry.get('高cm')
                pattern = entry.get('图案')

                # 放宽条件：只要有图案且至少宽度或高度有一个有效值，就认为是有效数据
                if not pattern or (not width and not height):
                    error_record = {
                        '原始数据': entry.get('原始数据', original_row),
                        '错误原因': "OpenAI解析返回错误: 图案为空或高宽均为空值",
                        '错误日志': "OpenAI返回结果缺少必要字段",
                        '数据处理流程': entry.get('处理方式', 'OpenAI解析'),
                        '解析错误': "OpenAI解析返回错误: 缺少必要数据"
                    }
                    sheet_data[error_sheet].append(error_record)
                else:
                    filtered_valid_entries.append(entry)

            if filtered_valid_entries:
                for entry in filtered_valid_entries:
                    width = entry.get('宽cm', 0)
                    # 如果宽度为0，使用默认宽度
                    if width == 0:
                        width = default_width
                        entry['宽cm'] = default_width

                    height = entry.get('高cm', 0)
                    # 如果高度为0，使用默认宽度作为高度
                    if height == 0:
                        height = default_width
                        entry['高cm'] = default_width

                    # 应用边长增加逻辑
                    width, height = self._apply_edge_increment(width, height)
                    entry['宽cm'] = width
                    entry['高cm'] = height

                    quantity = entry.get('数量', 1)
                    if quantity <= 0:
                        quantity = 1
                        entry['数量'] = 1



                    # 根据宽度和高度智能分配到对应的sheet，使用robot_bot_0.0.12的逻辑
                    material = entry.get('材质', '')
                    target_sheet = self.determine_sheet(
                        width=width,
                        material=material,
                        height=height,
                        quantity=quantity
                    )

                    # 处理SPLIT逻辑（倍数优化分割）
                    if target_sheet and target_sheet.startswith("SPLIT:"):
                        # 解析SPLIT标记：SPLIT:target_width:optimized_quantity:remaining_quantity
                        parts = target_sheet.split(":")
                        if len(parts) == 4:
                            split_target_width = parts[1]
                            optimized_quantity = int(parts[2])
                            remaining_quantity = int(parts[3])

                            # 创建优化数量的条目
                            if optimized_quantity > 0:
                                optimized_entry = entry.copy()
                                optimized_entry['数量'] = optimized_quantity
                                if split_target_width in sheet_data:
                                    sheet_data[split_target_width].append(optimized_entry)
                                    self.log.emit(f"倍数优化: {optimized_quantity}个图片分配到sheet {split_target_width}")

                            # 创建剩余数量的条目，使用原始逻辑分配
                            if remaining_quantity > 0:
                                remaining_entry = entry.copy()
                                remaining_entry['数量'] = remaining_quantity
                                # 重新计算sheet，但不使用倍数优化（传入quantity=1避免再次触发倍数优化）
                                original_sheet = self.determine_sheet(width, material, height=height, quantity=1)
                                if original_sheet and not original_sheet.startswith("SPLIT:"):
                                    if original_sheet in sheet_data:
                                        sheet_data[original_sheet].append(remaining_entry)
                                        self.log.emit(f"剩余数量: {remaining_quantity}个图片分配到sheet {original_sheet}")
                                else:
                                    # 如果无法分配，使用第一个宽度sheet
                                    first_width_sheet = str(width_values[0])
                                    sheet_data[first_width_sheet].append(remaining_entry)
                                    self.log.emit(f"剩余数量: {remaining_quantity}个图片分配到默认sheet {first_width_sheet}")
                        continue  # 跳过后续的普通添加逻辑

                    # 添加到对应的sheet
                    if target_sheet and target_sheet in sheet_data:
                        sheet_data[target_sheet].append(entry)
                    else:
                        # 如果目标sheet不存在，添加到第一个宽度sheet
                        first_width_sheet = str(width_values[0])
                        sheet_data[first_width_sheet].append(entry)
            else:
                # 如果没有有效条目，将原始行添加到错误sheet
                error_record = {
                    '原始数据': original_row,
                    '错误原因': "OpenAI未能提取有效数据",
                    '错误日志': "OpenAI解析结果中不包含有效数据",
                    '数据处理流程': "OpenAI解析",
                    '解析错误': "OpenAI未能提取有效数据"
                }
                sheet_data[error_sheet].append(error_record)
        
        # 数据去重合并：对每个宽度sheet分别进行去重合并
        for width_val in width_values:
            width_sheet = str(width_val)
            if width_sheet in sheet_data:
                self._merge_duplicate_records(sheet_data, width_sheet)

        # 对超宽幅sheet也进行去重合并
        if "超宽幅" in sheet_data:
            self._merge_duplicate_records(sheet_data, "超宽幅")

    def _add_sorting_keys(self, df: pd.DataFrame, width_value: float) -> pd.DataFrame:
        """
        根据不同规则添加排序键

        规则：
        1. 两边都小于等于宽幅，则以长的边为准，倒序排列
        2. 长的边大于宽幅，则以短的边为准，倒序排列
        3. 找出宽、高与宽幅相等，或更相近的边，倒序排列
        
        Args:
            df: pandas DataFrame
            width_value: 宽度值
            
        Returns:
            pd.DataFrame: 添加了排序键的DataFrame
        """
        # 定义一个函数来计算排序键
        def calculate_sort_key(row):
            width = row['宽cm']
            height = row['高cm']

            # 两边都小于等于宽幅，则以长的边为准
            if width <= width_value and height <= width_value:
                return max(width, height)

            # 长的边大于宽幅，则以短的边为准
            max_dim = max(width, height)
            min_dim = min(width, height)
            if max_dim > width_value:
                return min_dim

            # 找出宽、高与宽幅相等，或更相近的边
            width_diff = abs(width - width_value)
            height_diff = abs(height - width_value)
            if width_diff <= height_diff:
                return width
            else:
                return height

        # 添加排序键列
        df['sort_key'] = df.apply(calculate_sort_key, axis=1)

        return df

    def _sort_sheets_for_display(self, sheet_data: Dict[str, List[Dict[str, Any]]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        对sheet进行排序，将"数据异常"放到最后，突出显示成功数据
        
        排序规则：
        1. 数字sheet（如"163", "205"等）排在前面，按数字大小排序
        2. 功能性sheet（如"超宽幅数据"）排在中间
        3. "数据异常"排在最后
        
        Args:
            sheet_data: 按sheet名称分组的数据字典
            
        Returns:
            Dict[str, List[Dict[str, Any]]]: 排序后的sheet数据字典
        """
        # 分类sheet
        numeric_sheets = []
        functional_sheets = []
        error_sheets = []

        for sheet_name, data in sheet_data.items():
            if sheet_name == "数据异常":
                error_sheets.append((sheet_name, data))
            elif sheet_name.isdigit():
                # 数字sheet，按数字大小排序
                numeric_sheets.append((int(sheet_name), sheet_name, data))
            else:
                # 功能性sheet
                functional_sheets.append((sheet_name, data))

        # 排序
        numeric_sheets.sort(key=lambda x: x[0])  # 按数字大小排序
        functional_sheets.sort(key=lambda x: x[0])  # 按名称排序

        # 组合结果，数字sheet在前，功能性sheet在中间，错误sheet在最后
        result = []

        # 添加数字sheet
        for _, sheet_name, data in numeric_sheets:
            result.append((sheet_name, data))

        # 添加功能性sheet
        for sheet_name, data in functional_sheets:
            result.append((sheet_name, data))

        # 添加错误sheet（放在最后）
        for sheet_name, data in error_sheets:
            result.append((sheet_name, data))

        return result

    def save_to_excel(self, output_file: str, sheet_data: Dict[str, List[Dict[str, Any]]]) -> None:
        """
        将处理后的数据保存到Excel文件
        
        Args:
            output_file: 输出文件路径
            sheet_data: 按sheet名称分组的数据字典
        """
        try:
            # 确保至少有一个sheet有数据
            has_data = False
            for data in sheet_data.values():
                if data:
                    has_data = True
                    break

            if not has_data:
                # 如果没有数据，添加一条默认记录到"数据异常"
                if "数据异常" in sheet_data:
                    sheet_data["数据异常"].append({
                        '原始数据': '无有效数据',
                        '错误原因': '处理过程中未能提取有效数据',
                        '错误日志': '可能是输入格式不兼容或数据不完整',
                        '数据处理流程': '数据预处理',
                        '解析错误': '未能提取有效数据'
                    })
                else:
                    sheet_data["数据异常"] = [{
                        '原始数据': '无有效数据',
                        '错误原因': '处理过程中未能提取有效数据',
                        '错误日志': '可能是输入格式不兼容或数据不完整',
                        '数据处理流程': '数据预处理',
                        '解析错误': '未能提取有效数据'
                    }]

            # 数据已经在前面的逻辑中正确分配到各个sheet，这里不需要重新分配
            # 直接使用传入的sheet_data

            writer = pd.ExcelWriter(output_file, engine='openpyxl')

            # 对sheet进行排序，将"数据异常"放到最后，突出显示成功数据
            sorted_sheets = self._sort_sheets_for_display(sheet_data)

            for sheet_name, data in sorted_sheets:
                if data:  # 只保存有数据的sheet
                    df = pd.DataFrame(data)

                    # 移除行号列（如果存在）
                    if '行号' in df.columns:
                        df = df.drop('行号', axis=1)

                    # 确保关键列在前面
                    columns_order = [
                        '图案', '宽cm', '高cm', '数量',
                        '处理方式', '原始数据', '错误原因', '错误日志', '解析错误'
                    ]
                    # 过滤掉不在当前DataFrame中的列
                    available_columns = [col for col in columns_order if col in df.columns]
                    # 添加任何不在预定顺序中的列
                    remaining_columns = [col for col in df.columns if col not in available_columns]
                    final_columns = available_columns + remaining_columns

                    # 重新排序列
                    if final_columns:  # 确保有列可以排序
                        df = df[final_columns]

                    # 如果不是错误sheet，根据不同规则进行排序
                    if sheet_name != '数据异常' and '宽cm' in df.columns and '高cm' in df.columns:
                        # 根据sheet名称确定对应的宽度值
                        try:
                            if sheet_name == "超宽幅":
                                # 对于超宽幅sheet，使用最大的宽度值
                                current_width_value = max(self.width_values) if hasattr(self, 'width_values') else 205
                            else:
                                # 对于数字sheet，使用sheet名称作为宽度值
                                current_width_value = int(sheet_name)
                        except (ValueError, AttributeError):
                            # 如果无法确定宽度值，使用默认值
                            current_width_value = 163

                        # 添加排序键列
                        df = self._add_sorting_keys(df, current_width_value)

                        # 按排序键降序排序
                        df = df.sort_values(by='sort_key', ascending=False)

                        # 删除排序键列
                        df = df.drop('sort_key', axis=1)

                    df.to_excel(writer, sheet_name=sheet_name, index=False)

            writer.close()

            # 为未入库数据设置黄色背景
            Utils.apply_yellow_background_for_missing_images(output_file)

            return True
        except Exception as e:
            log.error(f"保存Excel文件时出错: {str(e)}")
            raise  # 重新抛出异常，由上层处理

    def _apply_edge_increment(self, width: float, height: float) -> Tuple[float, float]:
        """
        根据边长大小应用增加逻辑
        
        边长<boundary1时增加increment1
        边长==boundary1时增加默认值2.5
        boundary1<边长<boundary2时增加increment2
        边长==boundary2时增加默认值3
        边长>boundary2时增加increment3
        
        Args:
            width: 原始宽度
            height: 原始高度
            
        Returns:
            Tuple[float, float]: (调整后的宽度, 调整后的高度)
        """
        try:
            # 优先从配置管理器获取边界值和增加值
            if hasattr(self, 'image_finder_config') and self.image_finder_config:
                boundary_params = self.image_finder_config.get_boundary_params()
                boundary1 = boundary_params['boundary_value_1']
                boundary2 = boundary_params['boundary_value_2']
                increment1 = boundary_params['increment_value_1']
                increment2 = boundary_params['increment_value_2']
                increment3 = boundary_params['increment_value_3']
                equal_boundary1 = boundary_params['equal_boundary_increment_1']
                equal_boundary2 = boundary_params['equal_boundary_increment_2']
            else:
                # 回退到使用默认值（Worker类中无法访问UI控件）
                boundary1 = 100
                boundary2 = 160
                increment1 = 0
                increment2 = 1.0
                increment3 = 1.5
                equal_boundary1 = 0
                equal_boundary2 = 1.5

            # 对宽度应用优化的边界值逻辑
            if width == boundary1:
                width += equal_boundary1  # 边长等于第一边界值时使用用户设置的值
            elif width == boundary2:
                width += equal_boundary2  # 边长等于第二边界值时使用用户设置的值
            elif width < boundary1:
                width += increment1
            elif width < boundary2:
                width += increment2
            else:
                width += increment3

            # 对高度应用优化的边界值逻辑
            if height == boundary1:
                height += equal_boundary1  # 边长等于第一边界值时使用用户设置的值
            elif height == boundary2:
                height += equal_boundary2  # 边长等于第二边界值时使用用户设置的值
            elif height < boundary1:
                height += increment1
            elif height < boundary2:
                height += increment2
            else:
                height += increment3

            return width, height

        except (ValueError, AttributeError):
            # 如果获取参数失败，使用默认值和优化的边界逻辑
            equal_boundary1_default = 2.5
            equal_boundary2_default = 3.0
            
            if width == 100:
                width += equal_boundary1_default
            elif width == 160:
                width += equal_boundary2_default
            elif width < 100:
                width += 2
            elif width < 160:
                width += 3
            else:
                width += 5

            if height == 100:
                height += equal_boundary1_default
            elif height == 160:
                height += equal_boundary2_default
            elif height < 100:
                height += 2
            elif height < 160:
                height += 3
            else:
                height += 5

            return width, height

    def _merge_duplicate_records(self, sheet_data: Dict[str, List[Dict[str, Any]]], width_sheet: str) -> None:
        """
        合并相同图案名字和宽高尺寸的记录，数量累加
        
        Args:
            sheet_data: 所有sheet数据
            width_sheet: 当前处理的宽度sheet名称
        """
        try:
            if width_sheet not in sheet_data or not sheet_data[width_sheet]:
                return
            
            # 记录原始记录数
            original_count = len(sheet_data[width_sheet])
            
            # 使用字典来存储合并后的数据，key为(图案, 宽cm, 高cm)
            merged_data = {}
            
            # 用于生成唯一ID的计数器
            no_image_counter = 0
            
            for record in sheet_data[width_sheet]:
                # 提取关键字段
                pattern = record.get('图案', '')
                width = record.get('宽cm', 0)
                height = record.get('高cm', 0)
                quantity = record.get('数量', 1)
                
                # 特殊处理：如果图案名字是'无图'，每条记录都是独有的，不进行合并
                if pattern == '无图':
                    # 为无图记录创建唯一键，确保不会合并
                    key = ('无图', width, height, no_image_counter)
                    no_image_counter += 1
                    merged_data[key] = record.copy()
                else:
                    # 普通记录使用原有的合并逻辑
                    key = (pattern, width, height)
                    
                    if key in merged_data:
                        # 如果已存在相同的记录，累加数量
                        merged_data[key]['数量'] += quantity

                        # 合并原始数据信息
                        original_data = merged_data[key].get('原始数据', '')
                        new_original_data = record.get('原始数据', '')
                        if new_original_data and new_original_data not in original_data:
                            merged_data[key]['原始数据'] = f"{original_data}; {new_original_data}"
                    else:
                        # 如果是新记录，直接添加
                        merged_data[key] = record.copy()
            
            # 将合并后的数据重新赋值给sheet_data
            sheet_data[width_sheet] = list(merged_data.values())
            
            # 记录合并信息
            merged_count = len(merged_data)
            if original_count > merged_count:
                self.log.emit(f"数据去重合并完成：原始记录 {original_count} 条，合并后 {merged_count} 条")
            
        except Exception as e:
            self.log.emit(f"数据去重合并时出错: {str(e)}")
            log.error(f"数据去重合并时出错: {str(e)}")


# ---------------------------
# UI层
# ---------------------------
class TxtToExcelApp(QMainWindow):
    """
    图片查找器主应用程序界面
    
    基于PyQt6的GUI应用，提供表格处理、图片检索、尺寸调整等功能
    """
    
    def __init__(self) -> None:
        """初始化主应用程序界面"""
        super().__init__()
        # 设置主窗口实例引用
        set_main_window_instance(self)
        # 初始化SupabaseHelper引用
        self.supabase_helper = None
        self.openai_mgr = None  # 延迟初始化
        # 初始化高级设置授权管理器（延迟初始化，等待supabase_helper设置）
        self.settings_auth = None
        self.init_ui()
        self.load_column_config()

        # 初始化宽度参数（从supabase获取）
        self.material_keywords = Utils.fetch_material_keywords()
        self.remove_words = Utils.fetch_remove_word_arr()
        self.init_width_values()

        # 更新按钮状态（在加载配置后）
        self.enable_buttons_based_on_state()

        # 更新窗口标题以显示登录用户
        self.update_window_title()

    def send_hotkey(self, key_char):
        """
        发送热键组合 Ctrl+Shift+指定键

        Args:
            key_char: 要发送的键字符（如'P'、'D'等）
        """
        try:
            # Windows虚拟键码
            VK_CONTROL = 0x11
            VK_SHIFT = 0x10

            # 获取字符对应的虚拟键码
            if key_char.isalpha():
                vk_key = ord(key_char.upper())
            elif key_char.isdigit():
                vk_key = ord(key_char)
            else:
                log.warning(f"不支持的热键字符: {key_char}")
                return False

            # 定义Windows API函数
            user32 = ctypes.windll.user32

            # 按下Ctrl
            user32.keybd_event(VK_CONTROL, 0, 0, 0)
            # 按下Shift
            user32.keybd_event(VK_SHIFT, 0, 0, 0)
            # 按下指定键
            user32.keybd_event(vk_key, 0, 0, 0)

            # 释放指定键
            user32.keybd_event(vk_key, 0, 2, 0)  # 2表示KEYEVENTF_KEYUP
            # 释放Shift
            user32.keybd_event(VK_SHIFT, 0, 2, 0)
            # 释放Ctrl
            user32.keybd_event(VK_CONTROL, 0, 2, 0)

            log.info(f"已发送热键组合: Ctrl+Shift+{key_char}")
            return True

        except Exception as e:
            log.error(f"发送热键时出错: {e}")
            return False

    def trigger_completion_hotkey(self):
        """
        触发任务完成热键
        """
        try:
            # 检查是否启用热键
            if not hasattr(self, 'hotkey_enabled_checkbox') or not self.hotkey_enabled_checkbox.isChecked():
                return

            # 获取热键字符
            if not hasattr(self, 'hotkey_combo'):
                return

            hotkey_key = self.hotkey_combo.currentText()
            if not hotkey_key:
                return

            # 发送热键
            self.update_log(f"🎯 触发完成热键: Ctrl+Shift+{hotkey_key}")

            success = self.send_hotkey(hotkey_key)

            if success:
                self.update_log("✅ 热键发送成功 - 任务完成通知已发送")
            else:
                self.update_log("❌ 热键发送失败")

        except Exception as e:
            log.error(f"触发完成热键时出错: {e}")
            self.update_log(f"❌ 触发完成热键时出错: {e}")

    def init_width_values(self) -> None:
        """
        初始化宽度参数，从supabase获取材质宽幅映射
        """
        try:
            # 从supabase获取材质分类和宽幅配置
            self.texture_categories = Utils.fetch_texture_categories_with_width(self.supabase_helper)

            # 构建子材质到基础材质的映射
            self.sub_to_base_texture = {}
            for base, details in self.texture_categories.items():
                for sub_texture in details['sub_textures']:
                    self.sub_to_base_texture[sub_texture] = base

            # 从supabase获取全局宽幅参数
            self.global_width_arr = Utils.fetch_robot_config_width_arr(self.supabase_helper)
            if not self.global_width_arr:
                self.global_width_arr = DEFAULT_WIDTH_VALUES  
                log.info("使用默认的全局width_arr配置。")

            # 生成全局宽度范围和映射
            self.global_width_ranges, self.global_sheet_mapping = self.generate_width_mappings(self.global_width_arr)

            # 构建基础材质的宽度映射
            self.base_texture_mappings = {}
            for base, details in self.texture_categories.items():
                width_arr = details['width_arr']
                if width_arr:
                    width_ranges, sheet_mapping = self.generate_width_mappings(width_arr)
                else:
                    width_ranges = self.global_width_ranges
                    sheet_mapping = self.global_sheet_mapping
                self.base_texture_mappings[base] = {
                    'width_ranges': width_ranges,
                    'sheet_mapping': sheet_mapping
                }

            # 设置默认宽度值（用于向后兼容）
            self.width_values = self.global_width_arr

            log.info(f"成功初始化材质宽幅映射: {len(self.texture_categories)} 个材质")
            self.update_log(f"已从云端获取材质宽幅配置: {len(self.texture_categories)} 个材质")
            self.update_log(f"全局宽幅参数: {', '.join(map(str, self.global_width_arr))}cm")

            # 更新相关配置
            self.on_width_changed_internal(self.width_values)

        except Exception as e:
            log.error(f"初始化宽度参数时出错: {str(e)}")
            # 使用默认配置
            self.global_width_arr = DEFAULT_WIDTH_VALUES
            self.width_values = self.global_width_arr
            self.texture_categories = {}
            self.sub_to_base_texture = {}
            self.base_texture_mappings = {}
            self.global_width_ranges, self.global_sheet_mapping = self.generate_width_mappings(self.global_width_arr)
            self.update_log(f"宽度参数初始化失败，使用默认值: {', '.join(map(str, self.width_values))}cm")

    def generate_width_mappings(self, width_arr: List[float]) -> Tuple[List[Tuple[float, float, str]], Dict[int, str]]:
        """
        根据width_arr生成width_ranges和sheet_mapping
        
        参照robot_bot_0.0.12.py的逻辑实现
        
        Args:
            width_arr: 宽度数组
            
        Returns:
            Tuple[List[Tuple[float, float, str]], Dict[int, str]]: (width_ranges, sheet_mapping)
        """
        width_ranges = []
        sheet_mapping = {}
        previous = 0
        for width in width_arr:
            width_ranges.append((previous, width, str(width)))
            sheet_mapping[width] = str(width)
            previous = width
        width_ranges.append((previous, float('inf'), 'Other'))
        log.info(f"生成的width_ranges: {width_ranges} 和 sheet_mapping: {sheet_mapping}")
        return width_ranges, sheet_mapping

    def _get_base_texture_from_material(self, material: str) -> Optional[str]:
        """
        根据材质名称获取基础材质
        
        参照robot_bot_0.0.12.py的逻辑实现
        
        Args:
            material: 材质名称
            
        Returns:
            Optional[str]: 基础材质名称，如果未找到则返回None
        """
        if not material or not hasattr(self, 'sub_to_base_texture'):
            return None

        # 直接查找
        if material in self.sub_to_base_texture:
            return self.sub_to_base_texture[material]



        log.debug(f"材质 {material} 未找到对应的基础材质")
        return None

    def update_window_title(self) -> None:
        """
        更新窗口标题，显示登录用户名
        """
        title = f"{ROBOT_SMART_NAME} v{ROBOT_CURRENT_VERSION}"

        # 添加用户信息
        if self.supabase_helper and self.supabase_helper.is_connected():
            try:
                email = self.supabase_helper.get_user_email()
                if email:
                    title += f" - 用户: {email}"
            except Exception as e:
                logging.error(f"获取用户信息时出错: {e}")

        self.setWindowTitle(title)

    def closeEvent(self, event: QCloseEvent) -> None:
        """
        处理窗口关闭事件，优雅退出程序

        Args:
            event: 关闭事件对象（暂未使用，保留用于向后兼容）
        """
        try:
            logging.info("开始关闭程序，正在终止所有后台任务...")
            
            # 保存列配置
            try:
                self.save_column_config()
                logging.info("配置已保存")
            except Exception as e:
                logging.error(f"保存配置时出错: {e}")

            # 设置超时时间（秒）
            timeout_seconds = 5
            
            # 1. 终止所有Worker线程
            workers_to_terminate = []
            
            # 主Worker线程
            if hasattr(self, 'worker') and self.worker and self.worker.isRunning():
                workers_to_terminate.append(('worker', self.worker))
                logging.info("发现运行中的Worker线程，准备终止")
            
            # TXT文件处理线程
            if hasattr(self, 'txt_worker') and self.txt_worker and self.txt_worker.isRunning():
                workers_to_terminate.append(('txt_worker', self.txt_worker))
                logging.info("发现运行中的TxtFileProcessor线程，准备终止")
            
            # 图片检索线程
            if hasattr(self, 'retrieve_worker') and self.retrieve_worker and self.retrieve_worker.isRunning():
                workers_to_terminate.append(('retrieve_worker', self.retrieve_worker))
                logging.info("发现运行中的ProcessedTableImageRetrieveWorker线程，准备终止")
            
            # 图片检索工作线程（如果存在）
            if hasattr(self, 'image_retrieval_worker') and self.image_retrieval_worker and self.image_retrieval_worker.isRunning():
                workers_to_terminate.append(('image_retrieval_worker', self.image_retrieval_worker))
                logging.info("发现运行中的image_retrieval_worker线程，准备终止")
            
            # 终止所有发现的线程
            for worker_name, worker in workers_to_terminate:
                try:
                    logging.info(f"正在终止 {worker_name} 线程...")
                    worker.terminate()
                    if worker.wait(timeout_seconds * 1000):  # 转换为毫秒
                        logging.info(f"{worker_name} 线程已成功终止")
                    else:
                        logging.warning(f"{worker_name} 线程终止超时，强制结束")
                        worker.kill() if hasattr(worker, 'kill') else None
                except Exception as e:
                    logging.error(f"终止 {worker_name} 线程时出错: {e}")
            
            # 2. 停止OpenAI工作线程池
            if hasattr(self, 'openai_mgr') and self.openai_mgr:
                try:
                    logging.info("正在停止OpenAI客户端管理器...")
                    
                    # 停止工作线程池
                    if hasattr(self.openai_mgr, '_workers_running'):
                        self.openai_mgr._workers_running = False
                        logging.info("已设置OpenAI工作线程停止标志")
                    
                    # 终止工作线程池中的所有线程
                    if hasattr(self.openai_mgr, '_worker_pool'):
                        for i, worker_thread in enumerate(self.openai_mgr._worker_pool):
                            try:
                                if worker_thread.is_alive():
                                    logging.info(f"正在终止OpenAI工作线程 {i+1}")
                                    # 由于这些是普通Thread，我们只能设置停止标志
                                    # 线程会在下次检查时自动退出
                            except Exception as e:
                                logging.error(f"检查OpenAI工作线程 {i+1} 状态时出错: {e}")
                    
                    # 停止初始化线程
                    if hasattr(self.openai_mgr, '_init_thread') and self.openai_mgr._init_thread and self.openai_mgr._init_thread.isRunning():
                        logging.info("正在停止OpenAI初始化线程...")
                        self.openai_mgr._init_thread.quit()
                        if self.openai_mgr._init_thread.wait(timeout_seconds * 1000):
                            logging.info("OpenAI初始化线程已成功停止")
                        else:
                            logging.warning("OpenAI初始化线程停止超时")
                    
                    logging.info("OpenAI客户端管理器已停止")
                except Exception as e:
                    logging.error(f"停止OpenAI客户端管理器时出错: {e}")
            
            # 3. 取消所有网络请求（通过清空请求队列）
            try:
                if hasattr(self, 'openai_mgr') and self.openai_mgr and hasattr(self.openai_mgr, '_request_queue'):
                    # 清空请求队列
                    while not self.openai_mgr._request_queue.empty():
                        try:
                            self.openai_mgr._request_queue.get_nowait()
                        except:
                            break
                    logging.info("已清空OpenAI请求队列")
            except Exception as e:
                logging.error(f"清空请求队列时出错: {e}")
            
            # 4. 登出Supabase
            try:
                if self.supabase_helper and self.supabase_helper.is_connected():
                    self.supabase_helper.logout()
                    logging.info("已成功登出Supabase")
            except Exception as e:
                logging.error(f"登出Supabase时出错: {e}")
            
            # 5. 清理日志处理器
            try:
                for handler in log.handlers[:]:
                    handler.close()
                    log.removeHandler(handler)
                logging.info("日志处理器已清理")
            except Exception as e:
                logging.error(f"清理日志处理器时出错: {e}")
            
            logging.info("所有后台任务终止完成，程序即将退出")
            
            # 6. 退出应用
            QApplication.quit()
            
        except Exception as e:
            logging.error(f"关闭程序时发生严重错误: {e}")
            logging.error(f"错误详情: {traceback.format_exc()}")
            
            # 如果优雅退出失败，强制退出
            try:
                # 最后的强制清理尝试
                if hasattr(self, 'worker') and self.worker:
                    self.worker.terminate()
                if hasattr(self, 'txt_worker') and self.txt_worker:
                    self.txt_worker.terminate()
                if hasattr(self, 'retrieve_worker') and self.retrieve_worker:
                    self.retrieve_worker.terminate()
                if hasattr(self, 'image_retrieval_worker') and self.image_retrieval_worker:
                    self.image_retrieval_worker.terminate()
                    
                logging.info("执行强制清理后退出")
            except:
                pass
            
            # 强制退出
            sys.exit(1)

    def save_column_config(self) -> None:
        """
        保存配置到数据库和文件，包括路径记忆
        """
        try:
            # 优先使用新的配置管理器
            if self.image_finder_config:
                # 保存到数据库（宽度参数从supabase获取，不需要保存）
                self.image_finder_config.set('default_dpi', int(self.dpi_input.text()) if self.dpi_input.text() else DEFAULT_DPI)
                # 保存热键配置
                self.image_finder_config.set('hotkey_enabled', self.hotkey_enabled_checkbox.isChecked())
                self.image_finder_config.set('hotkey_key', self.hotkey_combo.currentText())
                self.image_finder_config.set('last_table_folder', self.folder_path_edit.text())

                # 保存边界值参数
                if hasattr(self, 'boundary1_input'):
                    self.image_finder_config.set('boundary_value_1', int(self.boundary1_input.text()) if self.boundary1_input.text() else 100)
                if hasattr(self, 'boundary2_input'):
                    self.image_finder_config.set('boundary_value_2', int(self.boundary2_input.text()) if self.boundary2_input.text() else 160)
                if hasattr(self, 'increment1_input'):
                    self.image_finder_config.set('increment_value_1', float(self.increment1_input.text()) if self.increment1_input.text() else 0)
                if hasattr(self, 'increment2_input'):
                    self.image_finder_config.set('increment_value_2', float(self.increment2_input.text()) if self.increment2_input.text() else 1.0)
                if hasattr(self, 'increment3_input'):
                    self.image_finder_config.set('increment_value_3', float(self.increment3_input.text()) if self.increment3_input.text() else 1.5)
                if hasattr(self, 'equal_boundary1_input'):
                    self.image_finder_config.set('equal_boundary_increment_1', float(self.equal_boundary1_input.text()) if self.equal_boundary1_input.text() else 0)
                if hasattr(self, 'equal_boundary2_input'):
                    self.image_finder_config.set('equal_boundary_increment_2', float(self.equal_boundary2_input.text()) if self.equal_boundary2_input.text() else 1.5)

                # 保存图库路径（如果存在）
                if HAS_IMAGE_INDEXER and hasattr(self, 'library_path_edit'):
                    self.image_finder_config.set('last_library_folder', self.library_path_edit.text())

                log.info("配置已保存到数据库")

            # 同时保存到JSON文件作为备份（兼容性）
            config = {
                'common': {
                    'dpi': self.dpi_input.text(),
                    'hotkey_enabled': self.hotkey_enabled_checkbox.isChecked(),
                    'hotkey_key': self.hotkey_combo.currentText()
                },
                'paths': {
                    'table_folder': self.folder_path_edit.text(),
                    'library_folder': ''
                }
            }

            # 保存图库路径（如果存在）
            if HAS_IMAGE_INDEXER and hasattr(self, 'library_path_edit'):
                config['common']['library_path'] = self.library_path_edit.text()
                config['paths']['library_folder'] = self.library_path_edit.text()

            # 创建配置目录（如果不存在）
            config_dir = os.path.join(os.path.expanduser('~'), '.deai-image-finder')
            os.makedirs(config_dir, exist_ok=True)

            # 保存配置
            config_file = os.path.join(config_dir, 'column_config.json')
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            log.info(f"配置备份已保存到 {config_file}")
        except Exception as e:
            log.error(f"保存配置失败: {e}")

    def load_column_config(self) -> None:
        """
        从数据库和文件加载配置，包括路径记忆
        """
        try:
            # 优先从新的配置管理器加载
            if self.image_finder_config:
                # 从数据库加载配置（宽度参数从supabase获取，不从配置加载）
                default_dpi = self.image_finder_config.get('default_dpi', DEFAULT_DPI)
                self.dpi_input.setText(str(default_dpi))

                # 加载热键配置
                hotkey_enabled = self.image_finder_config.get('hotkey_enabled', True)
                self.hotkey_enabled_checkbox.setChecked(hotkey_enabled)

                hotkey_key = self.image_finder_config.get('hotkey_key', 'P')
                self.hotkey_combo.setCurrentText(str(hotkey_key))

                # 加载边界值参数
                if hasattr(self, 'boundary1_input'):
                    boundary1 = self.image_finder_config.get('boundary_value_1', 100)
                    self.boundary1_input.setText(str(boundary1))
                if hasattr(self, 'boundary2_input'):
                    boundary2 = self.image_finder_config.get('boundary_value_2', 240)  # 修正默认值为240
                    self.boundary2_input.setText(str(boundary2))
                if hasattr(self, 'increment1_input'):
                    increment1 = self.image_finder_config.get('increment_value_1', 0)
                    self.increment1_input.setText(str(increment1))
                if hasattr(self, 'increment2_input'):
                    increment2 = self.image_finder_config.get('increment_value_2', 1)  # 修正默认值为1
                    self.increment2_input.setText(str(increment2))
                if hasattr(self, 'increment3_input'):
                    increment3 = self.image_finder_config.get('increment_value_3', 1.5)
                    self.increment3_input.setText(str(increment3))
                if hasattr(self, 'equal_boundary1_input'):
                    equal_boundary1 = self.image_finder_config.get('equal_boundary_increment_1', 0)
                    self.equal_boundary1_input.setText(str(equal_boundary1))
                if hasattr(self, 'equal_boundary2_input'):
                    equal_boundary2 = self.image_finder_config.get('equal_boundary_increment_2', 1.5)
                    self.equal_boundary2_input.setText(str(equal_boundary2))

                # 加载路径记忆
                last_table_folder = self.image_finder_config.get('last_table_folder', '')
                if last_table_folder and os.path.exists(last_table_folder):
                    self.folder_path_edit.setText(last_table_folder)
                    self.update_log(f"已恢复表格文件夹路径: {last_table_folder}")

                # 加载图库路径（如果存在）
                if HAS_IMAGE_INDEXER and hasattr(self, 'library_path_edit'):
                    last_library_folder = self.image_finder_config.get('last_library_folder', '')
                    if last_library_folder and os.path.exists(last_library_folder):
                        self.library_path_edit.setText(last_library_folder)
                        self.update_log(f"已恢复图库文件夹路径: {last_library_folder}")

                log.info("已从数据库加载配置")
                return

            # 回退到JSON文件加载（兼容性）
            config_file = os.path.join(os.path.expanduser('~'), '.deai-image-finder', 'column_config.json')
            if not os.path.exists(config_file):
                log.info("未找到配置文件，使用默认配置")
                return

            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 加载通用配置
            if 'common' in config:
                common_config = config['common']
                if 'dpi' in common_config:
                    self.dpi_input.setText(str(common_config['dpi']))
                if 'hotkey_enabled' in common_config:
                    self.hotkey_enabled_checkbox.setChecked(common_config['hotkey_enabled'])
                if 'hotkey_key' in common_config:
                    self.hotkey_combo.setCurrentText(str(common_config['hotkey_key']))
                # 加载图库路径（如果存在）
                if HAS_IMAGE_INDEXER and hasattr(self, 'library_path_edit') and 'library_path' in common_config:
                    library_path = common_config['library_path']
                    if library_path and os.path.exists(library_path):
                        self.library_path_edit.setText(library_path)

            # 加载路径记忆
            if 'paths' in config:
                paths_config = config['paths']
                # 加载表格文件夹路径
                if 'table_folder' in paths_config:
                    table_folder = paths_config['table_folder']
                    if table_folder and os.path.exists(table_folder):
                        self.folder_path_edit.setText(table_folder)
                        self.update_log(f"已恢复表格文件夹路径: {table_folder}")

                # 加载图库文件夹路径
                if HAS_IMAGE_INDEXER and hasattr(self, 'library_path_edit') and 'library_folder' in paths_config:
                    library_folder = paths_config['library_folder']
                    if library_folder and os.path.exists(library_folder):
                        self.library_path_edit.setText(library_folder)
                        self.update_log(f"已恢复图库文件夹路径: {library_folder}")

            log.info("已从JSON配置文件加载配置")
        except Exception as e:
            log.error(f"加载配置文件失败: {e}")

    def handle_openai_init(self, success, msg):
        """处理OpenAI初始化结果"""
        if not success:
            self.update_log("OpenAI初始化失败: " + msg)
            QMessageBox.critical(self, "初始化错误", msg)
        else:
            self.update_log("OpenAI初始化成功: " + msg)

    def init_ui(self) -> None:
        """
        初始化UI元素
        """
        # 设置窗口标题和大小
        self.setWindowTitle('图片检索工具')
        self.setGeometry(300, 300, WINDOW_WIDTH, WINDOW_HEIGHT)

        # 初始化配置管理器（用于宽度同步）
        self.config_manager = None
        self.rectpack_controller = None

        # 初始化图片查找器配置管理器
        if HAS_IMAGE_FINDER_CONFIG:
            try:
                self.image_finder_config = get_image_finder_config()
                log.info("图片查找器配置管理器初始化成功")
            except Exception as e:
                log.error(f"图片查找器配置管理器初始化失败: {str(e)}")
                self.image_finder_config = None
        else:
            self.image_finder_config = None

        # 创建顶层容器
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)

        # ----- 顶部说明和操作区域 -----
        top_group = QGroupBox("操作区域")
        top_layout = QVBoxLayout()

        # 文件夹选择区域
        folder_layout = QHBoxLayout()
        folder_label = QLabel("表格文件夹:")
        self.folder_path_edit = QLineEdit()
        self.folder_path_edit.setReadOnly(True)
        self.folder_path_edit.setPlaceholderText("请选择包含表格文件的文件夹")
        browse_button = QPushButton("选择文件夹")
        browse_button.clicked.connect(self.select_folder)

        folder_layout.addWidget(folder_label)
        folder_layout.addWidget(self.folder_path_edit)
        folder_layout.addWidget(browse_button)
        top_layout.addLayout(folder_layout)

        # 图库路径选择区域（如果图片检索功能可用）
        if HAS_IMAGE_INDEXER:
            library_layout = QHBoxLayout()
            library_label = QLabel("图库文件夹:")
            self.library_path_edit = QLineEdit()
            self.library_path_edit.setReadOnly(True)
            self.library_path_edit.setPlaceholderText("请选择图库文件夹（可选，用于图片检索）")
            library_browse_button = QPushButton("选择图库")
            library_browse_button.clicked.connect(self.select_library_folder)

            library_layout.addWidget(library_label)
            library_layout.addWidget(self.library_path_edit)
            library_layout.addWidget(library_browse_button)
            top_layout.addLayout(library_layout)

        # 主要操作按钮区域
        main_action_layout = QVBoxLayout()

        # 第一行：TXT文件处理按钮
        txt_action_layout = QHBoxLayout()
        self.process_txt_button = QPushButton("一键排版（txt智能解析+图片极速检索+自动加抽排版）")
        self.process_txt_button.clicked.connect(self.start_txt_processing)
        self.process_txt_button.setEnabled(False)  # 初始状态为禁用
        self.process_txt_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 10px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        txt_action_layout.addWidget(self.process_txt_button)
        main_action_layout.addLayout(txt_action_layout)



        top_layout.addLayout(main_action_layout)

        # 状态显示区域
        status_layout = QHBoxLayout()
        self.status_label = QLabel("状态: 请选择表格文件夹开始处理")
        self.status_label.setStyleSheet("color: #666; font-style: italic;")
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        top_layout.addLayout(status_layout)

        # 参数设置区域（可折叠）
        params_group = QGroupBox("参数设置")
        params_group.setCheckable(True)
        params_group.setChecked(True)  # 默认选中（展开）
        params_layout = QVBoxLayout()

        # 基础参数行
        basic_params_layout = QHBoxLayout()

        # DPI输入
        dpi_label = QLabel("默认DPI:")
        self.dpi_input = QLineEdit("72")  # 默认DPI值
        self.dpi_input.setMaximumWidth(60)
        self.dpi_input.setPlaceholderText("72")

        # 热键设置
        hotkey_label = QLabel("完成热键:")
        self.hotkey_enabled_checkbox = QCheckBox("启用")
        self.hotkey_enabled_checkbox.setChecked(True)  # 默认启用

        hotkey_combo_label = QLabel("Ctrl+Shift+")
        self.hotkey_combo = QComboBox()
        # 添加字母A-Z和数字0-9
        hotkey_options = [chr(i) for i in range(ord('A'), ord('Z')+1)] + [str(i) for i in range(10)]
        self.hotkey_combo.addItems(hotkey_options)
        self.hotkey_combo.setCurrentText("P")  # 默认选择P
        self.hotkey_combo.setMaximumWidth(50)

        basic_params_layout.addWidget(dpi_label)
        basic_params_layout.addWidget(self.dpi_input)
        basic_params_layout.addWidget(QLabel("    "))  # 间距
        basic_params_layout.addWidget(hotkey_label)
        basic_params_layout.addWidget(self.hotkey_enabled_checkbox)
        basic_params_layout.addWidget(hotkey_combo_label)
        basic_params_layout.addWidget(self.hotkey_combo)
        basic_params_layout.addStretch()

        params_layout.addLayout(basic_params_layout)

        # 边长增加参数设置（紧凑布局）
        edge_layout = QHBoxLayout()

        # 边界值设置
        boundary_label = QLabel("边界值:")
        self.boundary1_input = QLineEdit("100")  # 第一个边界值
        self.boundary1_input.setMaximumWidth(50)
        self.boundary2_input = QLineEdit("240")  # 第二个边界值，修正为240
        self.boundary2_input.setMaximumWidth(50)

        # 增加值设置
        increment_label = QLabel("增加值:")
        self.increment1_input = QLineEdit("0")  # <第一边界值时增加的值
        self.increment1_input.setMaximumWidth(40)
        self.increment2_input = QLineEdit("1")  # 第一边界值<边长<第二边界值时增加的值，修正为1
        self.increment2_input.setMaximumWidth(40)
        self.increment3_input = QLineEdit("1.5")  # >第二边界值时增加的值
        self.increment3_input.setMaximumWidth(40)

        # 等于边界值时的增加值设置
        equal_boundary_label = QLabel("等于边界值增加值:")
        self.equal_boundary1_input = QLineEdit("0")  # =第一边界值时增加的值
        self.equal_boundary1_input.setMaximumWidth(40)
        self.equal_boundary2_input = QLineEdit("1.5")  # =第二边界值时增加的值
        self.equal_boundary2_input.setMaximumWidth(40)

        edge_layout.addWidget(boundary_label)
        edge_layout.addWidget(self.boundary1_input)
        edge_layout.addWidget(QLabel("和"))
        edge_layout.addWidget(self.boundary2_input)
        edge_layout.addWidget(QLabel("    "))  # 间距
        edge_layout.addWidget(increment_label)
        edge_layout.addWidget(self.increment1_input)
        edge_layout.addWidget(QLabel(","))
        edge_layout.addWidget(self.increment2_input)
        edge_layout.addWidget(QLabel(","))
        edge_layout.addWidget(self.increment3_input)
        edge_layout.addWidget(QLabel("    "))  # 间距
        edge_layout.addWidget(equal_boundary_label)
        edge_layout.addWidget(self.equal_boundary1_input)
        edge_layout.addWidget(QLabel(","))
        edge_layout.addWidget(self.equal_boundary2_input)
        edge_layout.addStretch()

        params_layout.addLayout(edge_layout)

        # 说明标签
        edge_explanation = QLabel("边长<第一边界值时增加第一个值；边长=第一边界值时增加等于边界值增加值的第一个值；第一边界值<边长<第二边界值时增加第二个值；边长=第二边界值时增加等于边界值增加值的第二个值；边长>第二边界值时增加第三个值")
        edge_explanation.setWordWrap(True)
        edge_explanation.setStyleSheet("color: gray; font-size: 9px;")
        params_layout.addWidget(edge_explanation)

        params_group.setLayout(params_layout)
        top_layout.addWidget(params_group)

        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        top_layout.addWidget(separator)

        # 添加说明标签
        explanation = QLabel("智能处理模式：\n"
                           "• TXT文件解析：将TXT文件解析为总操作表格，支持多种格式的自动识别和AI解析\n"
                           "• 图片处理：解析完成后可选择进行图片检索，自动完成表格解析和图片检索，所有图片将使用默认DPI进行厘米到像素的转换")
        explanation.setWordWrap(True)
        explanation.setStyleSheet("color: #555; font-size: 11px; padding: 5px;")
        top_layout.addWidget(explanation)

        top_group.setLayout(top_layout)

        # ----- 日志和状态区域 -----
        log_group = QGroupBox("处理日志")
        log_layout = QVBoxLayout()

        # 进度条 - 移到日志区域的顶部
        progress_layout = QHBoxLayout()
        progress_label = QLabel("总进度:")
        self.progress_bar = QProgressBar()
        progress_layout.addWidget(progress_label)
        progress_layout.addWidget(self.progress_bar)
        log_layout.addLayout(progress_layout)

        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)

        log_group.setLayout(log_layout)

        # ----- 组装主界面 -----
        main_layout.addWidget(top_group)
        main_layout.addWidget(log_group)

        self.setCentralWidget(main_widget)

        # 更新日志
        self.update_log("程序已启动，请选择要处理的文件夹")
        self.update_log("支持功能:")
        self.update_log("• TXT文件解析：将TXT文件解析为总操作表格")
        self.update_log("• Excel表格处理：自动检测A-M列有效数据，3列视为结构化数据(图案、尺寸、数量)，2列视为非结构化数据(规格名称、数量)")
        self.update_log("• 图片检索和处理：从图库中检索图片并进行尺寸修改")

        # 显示图片索引功能状态
        if HAS_IMAGE_INDEXER:
            self.update_log("图片索引功能已启用，可以使用图库管理和图片检索功能")
        else:
            self.update_log("图片索引功能不可用，请检查是否安装了相关模块")

        # 高阶排列算法功能已移除

    def process_finished(self, success: bool, message: str) -> None:
        """
        处理完成回调函数
        
        Args:
            success: 处理是否成功
            message: 处理结果消息
        """
        if success:
            # 设置已处理表格数据（从 Worker 中获取）
            if hasattr(self.worker, 'processed_files_data'):
                self.processed_tables = self.worker.processed_files_data
                table_count = len(self.processed_tables)
                self.update_log("=" * 60)
                self.update_log(f"🎉 多表格解析完成！")
                self.update_log(f"📊 成功处理 {table_count} 个表格文件:")
                for i, (file_path, file_data) in enumerate(self.processed_tables.items(), 1):
                    filename = os.path.basename(file_path)
                    row_count = file_data.get('row_count', 0)
                    self.update_log(f"  {i}. {filename} ({row_count} 行数据)")
            else:
                self.processed_tables = {}
                self.update_log("⚠️ 未获取到已处理的表格数据")

            # 检查是否需要继续进行图片检索
            if (hasattr(self, 'complete_processing_mode') and
                self.complete_processing_mode and
                hasattr(self, 'library_path_for_processing') and
                self.library_path_for_processing and
                HAS_IMAGE_INDEXER):

                self.update_log("=" * 60)
                self.update_log("🔍 表格解析完成，开始图片检索...")
                self.update_status("正在进行图片检索...")
                self.start_image_retrieval_automatically()
                return  # 不要在这里重新启用按钮，等图片检索完成后再启用

            # 如果不需要图片检索，直接完成
            self.complete_all_processing(success, message)
        else:
            self.complete_all_processing(success, message)

    def complete_all_processing(self, success, message, skip_image_retrieval=False):
        """完成所有处理流程

        Args:
            success: 处理是否成功
            message: 处理消息
            skip_image_retrieval: 是否跳过图片检索（避免递归调用）
        """
        if success:
            self.statusBar().showMessage('多表格处理完成')
            self.update_status("多表格处理完成")

            # 根据处理模式显示不同的完成消息
            if (hasattr(self, 'complete_processing_mode') and self.complete_processing_mode and
                not skip_image_retrieval):
                if hasattr(self, 'library_path_for_processing') and self.library_path_for_processing:
                    # 表格解析完成后自动执行图片检索和尺寸修改
                    self.update_log("=" * 60)
                    self.update_log("🔍 多表格解析完成，开始自动执行图片检索和尺寸修改...")
                    self.start_image_retrieval_automatically()
                    # 不在这里显示弹窗，等图片检索完成后再显示
                    return
                else:
                    final_message = "多表格解析已完成（未进行图片检索）"
            else:
                final_message = message if skip_image_retrieval else "多表格解析已完成"

            self.update_log("=" * 60)
            self.update_log(final_message)

            # 任务完成后立即触发热键（如果启用），实现全自动化
            self.trigger_completion_hotkey()

            # 然后显示完成弹窗
            QMessageBox.information(self, '完成', final_message)
        else:
            self.statusBar().showMessage('多表格处理失败')
            self.update_status("多表格处理失败")
            self.update_log("=" * 60)
            self.update_log(f"❌ {message}")
            QMessageBox.warning(self, '错误', message)

        # 重新启用按钮和输入框
        self.enable_buttons_based_on_state()
        self.enable_all_inputs()

    def enable_all_inputs(self):
        """启用所有输入框"""
        self.dpi_input.setEnabled(True)
        if hasattr(self, 'boundary1_input'):
            self.boundary1_input.setEnabled(True)
        if hasattr(self, 'boundary2_input'):
            self.boundary2_input.setEnabled(True)
        if hasattr(self, 'increment1_input'):
            self.increment1_input.setEnabled(True)
        if hasattr(self, 'increment2_input'):
            self.increment2_input.setEnabled(True)
        if hasattr(self, 'increment3_input'):
            self.increment3_input.setEnabled(True)

    def check_version(self):
        """检查应用版本并更新窗口标题"""
        # 设置默认标题
        app_title = ROBOT_SMART_NAME

        if self.supabase_helper and self.supabase_helper.is_connected():
            try:
                # 尝试获取配置，先检查用户是否已登录
                if self.supabase_helper.is_authenticated():
                    log.info("用户已登录，使用已认证客户端获取配置")
                else:
                    log.info("用户未登录，将尝试使用匿名客户端获取配置")

                config = self.supabase_helper.fetch_config(IMAGE_FINDER_BOT_TAG)
                if not config:
                    log.error("未能获取云端配置信息")
                    # 即使获取失败也设置默认标题
                    self.setWindowTitle(app_title)

                    # 如果用户已登录但仍然获取失败，可能是权限问题
                    if self.supabase_helper.is_authenticated():
                        QMessageBox.warning(
                            self,
                            "配置获取警告",
                            "已登录但无法获取云端配置，可能是权限问题，将使用默认配置"
                        )
                    else:
                        QMessageBox.warning(
                            self,
                            "配置获取警告",
                            "无法获取云端配置，将使用默认配置"
                        )
                    return  # 使用默认配置继续运行，而不是退出

                # 成功获取配置
                latest_version = config.get('image_finder_ver')
                if not latest_version:
                    log.warning("云端配置中没有版本信息，将使用当前版本")
                    latest_version = ROBOT_CURRENT_VERSION

                log.info(f"当前应用版本: {ROBOT_CURRENT_VERSION}, 云端版本: {latest_version}")
                if ROBOT_CURRENT_VERSION != latest_version:
                    msg = QMessageBox()
                    msg.setIcon(QMessageBox.Icon.Critical)  # 使用错误图标
                    msg.setText("版本不匹配")
                    msg.setInformativeText(f"当前版本 {ROBOT_CURRENT_VERSION} 与云端版本 {latest_version} 不匹配，请升级到最新版本后再使用")
                    msg.setWindowTitle("版本错误")
                    msg.setStandardButtons(QMessageBox.StandardButton.Ok)
                    msg.exec()
                    # 强制退出应用
                    log.error(f"版本不匹配，强制退出应用。当前版本: {ROBOT_CURRENT_VERSION}, 云端版本: {latest_version}")
                    sys.exit(1)

                # 更新应用标题
                latest_title = config.get('app_title')
                if latest_title:
                    app_title = latest_title + '-v' + latest_version

                # 添加用户邮箱到标题
                if self.supabase_helper.is_authenticated():
                    user_email = self.supabase_helper.get_user_email()
                    if user_email:
                        app_title = f"{app_title} - {user_email}"
                        log.info(f"已将用户邮箱 {user_email} 添加到窗口标题")

                self.setWindowTitle(app_title)

            except Exception as e:
                log.error(f"云端配置检查失败: {type(e).__name__}: {str(e)}")
                # 异常情况下也设置默认标题
                self.setWindowTitle(ROBOT_SMART_NAME)
                QMessageBox.warning(
                    self,
                    "配置检查警告",
                    f"获取云端配置时出现异常，将使用默认配置"
                )
                # 不强制退出，允许用户继续使用
        else:
            # 如果没有连接到Supabase，使用默认标题
            log.warning("Supabase未连接，使用默认标题")
            self.setWindowTitle(app_title)

    # auto_start_find_images方法已被移除，其功能已整合到ProcessedTableImageRetrieveWorker中
    
    def start_image_retrieval_automatically(self):
        """自动开始图片检索（在表格处理完成后调用）"""
        if not HAS_IMAGE_INDEXER:
            self.update_log("图片索引功能不可用，跳过图片检索")
            self._finish_processing_with_message("表格解析完成（图片检索功能不可用）")
            return

        library_folder = getattr(self, 'library_path_for_processing', '')
        if not library_folder:
            self.update_log("未设置图库路径，跳过图片检索")
            self._finish_processing_with_message("表格解析完成（未设置图库路径）")
            return

        # 检查是否有已处理的表格
        if not hasattr(self, 'processed_tables') or not self.processed_tables:
            self.update_log("没有已处理的表格数据，跳过图片检索")
            self._finish_processing_with_message("表格解析完成（没有可检索的数据）")
            return

        # 创建图片索引器
        try:
            indexer = ImageIndexerDuckDB(fast_mode=True)
            # 设置图库路径
            if not indexer.set_library_path(library_folder):
                self.update_log("无法连接到图库，跳过图片检索")
                self._finish_processing_with_message("表格解析完成（图库连接失败）")
                return
        except Exception as e:
            self.update_log(f"初始化图片索引器时出错: {str(e)}，跳过图片检索")
            self._finish_processing_with_message(f"表格解析完成（图片索引器初始化失败: {str(e)}）")
            return

        # 检查并建立图库索引
        try:
            self.update_log("🔍 检查图库索引状态...")
            if not indexer.is_indexed(library_folder):
                self.update_log("📚 图库尚未建立索引，开始建立索引...")
                self.update_status("正在建立图库索引...")

                # 建立索引
                success, message, indexed_count = indexer.scan_library(library_folder, fast_mode=True)
                if success:
                    self.update_log(f"✅ 图库索引建立完成，共索引 {indexed_count} 个图片文件")
                else:
                    self.update_log(f"❌ 图库索引建立失败: {message}")
                    self._finish_processing_with_message(f"表格解析完成（图库索引建立失败: {message}）")
                    return
            else:
                self.update_log("✅ 图库索引已存在，检查是否需要更新...")
                # 可以在这里添加增量更新逻辑
                self.update_log("📚 使用现有图库索引")
        except Exception as e:
            self.update_log(f"检查图库索引时出错: {str(e)}，跳过图片检索")
            self._finish_processing_with_message(f"表格解析完成（图库索引检查失败: {str(e)}）")
            return

        self.update_log("开始自动图片检索...")

        # 获取DPI设置
        try:
            default_dpi = int(self.dpi_input.text()) if self.dpi_input.text() else DEFAULT_DPI
            if default_dpi <= 0:
                default_dpi = DEFAULT_DPI
        except ValueError:
            default_dpi = DEFAULT_DPI

        self.update_log(f"使用默认DPI: {default_dpi} (所有图片将使用此DPI进行厘米到像素转换)")

        # 启动图片检索工作线程
        self.retrieve_worker = ProcessedTableImageRetrieveWorker(
            self.processed_tables,
            indexer,
            self.folder_path_edit.text(),  # 原始文件夹路径
            default_dpi  # 传递DPI参数
        )
        self.retrieve_worker.progress_signal.connect(self.progress_bar.setValue)
        self.retrieve_worker.log_signal.connect(self.update_log)
        self.retrieve_worker.status_signal.connect(lambda msg: self.statusBar().showMessage(msg))
        self.retrieve_worker.finished_signal.connect(self.auto_retrieve_images_finished)
        self.retrieve_worker.start()

    def _finish_processing_with_message(self, message):
        """完成处理并显示消息（避免递归调用）"""
        self.update_log(message)

        # 任务完成后立即触发热键（如果启用），实现全自动化
        self.trigger_completion_hotkey()

        # 然后显示完成弹窗
        QMessageBox.information(self, '完成', message)

        # 重新启用按钮和输入框
        self.enable_buttons_based_on_state()
        self.enable_all_inputs()

    def auto_retrieve_images_finished(self, success, message, results):
        """多表格处理的自动图片检索完成"""
        # 强制设置进度为100%，确保UI正确显示
        self.progress_bar.setValue(100)
        self.statusBar().showMessage("总进度：100%")
        QApplication.processEvents()  # 强制更新UI

        self.update_log("=" * 60)
        if success:
            self.update_log(f"🎉 多表格图片检索成功!")
            self.update_log(f"📊 检索结果: {message}")
            if results:
                self.update_log("📁 生成的文件:")
                for i, result in enumerate(results, 1):
                    output_path = result.get('output_path', '')
                    if output_path:
                        filename = os.path.basename(output_path)
                        self.update_log(f"  {i}. {filename}")
            # 使用skip_image_retrieval=True避免递归调用
            self.complete_all_processing(True, "多表格解析和图片处理已完成", skip_image_retrieval=True)
        else:
            self.update_log(f"❌ 多表格图片检索失败: {message}")
            # 使用skip_image_retrieval=True避免递归调用
            self.complete_all_processing(True, f"多表格解析完成，但图片检索失败: {message}", skip_image_retrieval=True)

    def select_folder(self) -> None:
        """
        选择文件夹对话框
        """
        # 获取上次选择的路径作为默认路径
        last_path = self.folder_path_edit.text() if self.folder_path_edit.text() else os.path.expanduser('~')

        folder = QFileDialog.getExistingDirectory(self, '选择表格文件夹', last_path)
        if folder:
            self.folder_path_edit.setText(folder)
            self.log_text.clear()
            self.progress_bar.setValue(0)
            self.log_text.append(f"已选择表格文件夹: {folder}")
            self.statusBar().showMessage('文件夹已选择')

            # 更新按钮状态
            self.enable_buttons_based_on_state()
            self.update_log("文件夹已选择，可以开始处理")

            # 自动保存路径
            self.save_column_config()

    def start_txt_processing(self):
        """开始TXT文件处理"""
        folder_path = self.folder_path_edit.text()
        if not folder_path:
            QMessageBox.warning(self, "错误", "请先选择包含TXT文件的文件夹")
            return

        # 在开始处理前，先清理旧的已处理文件和 .cp5 文件
        self.update_log("=" * 60)
        self.update_log("🚀 开始TXT文件处理任务")

        # 清理 .cp5 文件
        self.clean_cp5_files(folder_path)

        # 清理已处理文件
        clean_success = self.clean_processed_files(folder_path)

        # 如果清理失败（文件被占用），停止处理
        if not clean_success:
            return

        # 检查文件夹中是否有txt文件
        txt_files = []
        for root, _, files in os.walk(folder_path):
            for file in files:
                if file.lower().endswith('.txt'):
                    txt_files.append(os.path.join(root, file))

        if not txt_files:
            QMessageBox.warning(self, "错误", "选择的文件夹中没有找到TXT文件")
            return

        # 更新状态和UI
        self.update_status("正在处理TXT文件...")
        self.disable_all_buttons()
        self.statusBar().showMessage('开始TXT文件处理...')

        # 清空日志并显示开始信息
        self.log_text.clear()
        self.update_log("🚀 开始TXT文件处理（解析为总操作表格）")
        self.update_log(f"📁 处理文件夹: {folder_path}")
        self.update_log(f"📄 找到 {len(txt_files)} 个TXT文件")

        # 创建TXT文件处理器
        configs = {
            'material_keywords': self.material_keywords,
            'remove_words': self.remove_words,
            'texture_categories': self.texture_categories,
            'sub_to_base_texture': self.sub_to_base_texture,
            'base_texture_mappings': self.base_texture_mappings,
            'global_width_ranges': self.global_width_ranges,
            'global_sheet_mapping': self.global_sheet_mapping,
            'global_width_arr': self.global_width_arr
        }
        self.txt_worker = TxtFileProcessor(folder_path, self.openai_mgr, configs)
        self.txt_worker.progress.connect(self.update_progress)
        self.txt_worker.log.connect(self.update_log)
        self.txt_worker.finished.connect(self.txt_processing_finished)
        self.txt_worker.start()

    def txt_processing_finished(self, success, message):
        """TXT文件处理完成"""
        self.progress_bar.setValue(100)
        self.statusBar().showMessage("TXT处理完成")

        if success:
            self.update_log("=" * 60)
            self.update_log("🎉 TXT文件处理完成！")
            self.update_log(message)

            # 检查生成的总操作表格文件数量
            generated_files = []
            folder_path = self.folder_path_edit.text()
            for root, _, files in os.walk(folder_path):
                for file in files:
                    if file.lower().endswith('.xlsx') and '_总操作表格' in file:
                        generated_files.append(os.path.join(root, file))

            if generated_files:
                # 开始尺寸调整流程，保持按钮禁用状态
                self.update_log("=" * 60)
                self.update_log("🔧 开始尺寸调整流程...")
                self.start_dimension_adjustment(generated_files)
                return
            else:
                self.update_log("⚠️ 未找到生成的总操作表格文件")

            # 检查是否有图库路径
            has_library = False
            if hasattr(self, 'library_path_edit'):
                library_path = self.library_path_edit.text()
                has_library = bool(library_path)

            if has_library:
                # 继续进行图片检索（直接检索，不重新解析表格），保持按钮禁用状态
                self.start_image_retrieval_after_txt()
                return
            else:
                self.update_log("⚠️ 未设置图库路径，仅完成表格解析")
                self.update_log("TXT文件处理完成")

                # 任务完成后立即触发热键（如果启用），实现全自动化
                self.trigger_completion_hotkey()

                # 只有在没有后续流程时才重新启用按钮
                self.enable_buttons_based_on_state()
                self.enable_all_inputs()
        else:
            self.update_log("=" * 60)
            self.update_log(f"❌ TXT文件处理失败: {message}")
            QMessageBox.warning(self, '错误', f"TXT文件处理失败: {message}")

            # 处理失败时重新启用按钮
            self.enable_buttons_based_on_state()
            self.enable_all_inputs()

    def start_dimension_adjustment(self, excel_files):
        """开始尺寸调整流程"""
        try:
            # 获取边界值和增量值参数
            boundary1 = 100  # 默认值
            boundary2 = 240  # 默认值
            increment1 = 0   # 默认值
            increment2 = 1   # 默认值
            increment3 = 1.5 # 默认值

            # 尝试从UI获取参数值
            try:
                if hasattr(self, 'boundary1_input') and self.boundary1_input.text():
                    boundary1 = float(self.boundary1_input.text())
                if hasattr(self, 'boundary2_input') and self.boundary2_input.text():
                    boundary2 = float(self.boundary2_input.text())
                if hasattr(self, 'increment1_input') and self.increment1_input.text():
                    increment1 = float(self.increment1_input.text())
                if hasattr(self, 'increment2_input') and self.increment2_input.text():
                    increment2 = float(self.increment2_input.text())
                if hasattr(self, 'increment3_input') and self.increment3_input.text():
                    increment3 = float(self.increment3_input.text())
            except ValueError:
                self.update_log("⚠️ 参数格式错误，使用默认值")

            self.update_log(f"📐 尺寸调整参数: 边界值({boundary1}, {boundary2}), 增量值({increment1}, {increment2}, {increment3})")

            # 创建尺寸调整处理器
            processor = DimensionAdjustmentProcessor(
                boundary1=boundary1,
                boundary2=boundary2,
                increment1=increment1,
                increment2=increment2,
                increment3=increment3
            )

            # 处理每个Excel文件
            success_count = 0
            for excel_file in excel_files:
                filename = os.path.basename(excel_file)
                self.update_log(f"🔧 正在调整: {filename}")

                if processor.process_excel_file(excel_file, log_callback=self.update_log):
                    success_count += 1
                else:
                    self.update_log(f"❌ 调整失败: {filename}")

            self.update_log("=" * 60)
            if success_count > 0:
                self.update_log(f"✅ 尺寸调整完成! 成功处理 {success_count}/{len(excel_files)} 个文件")

                # 检查是否有图库路径，继续图片检索流程
                has_library = False
                if hasattr(self, 'library_path_edit'):
                    library_path = self.library_path_edit.text()
                    has_library = bool(library_path)

                if has_library:
                    self.update_log("🚀 尺寸调整完成，继续图片检索流程...")
                    # 继续图片检索流程，保持按钮禁用状态
                    self.start_image_retrieval_after_txt()
                    return
                else:
                    self.update_log("⚠️ 未设置图库路径，尺寸调整流程完成")

                    # 任务完成后立即触发热键（如果启用），实现全自动化
                    self.trigger_completion_hotkey()

                    # 只有在没有图片检索流程时才重新启用按钮
                    self.enable_buttons_based_on_state()
                    self.enable_all_inputs()
            else:
                self.update_log(f"❌ 尺寸调整失败! 没有成功处理任何文件")
                # 处理失败时重新启用按钮
                self.enable_buttons_based_on_state()
                self.enable_all_inputs()

        except Exception as e:
            self.update_log(f"❌ 尺寸调整流程出错: {str(e)}")
            # 异常时重新启用按钮
            self.enable_buttons_based_on_state()
            self.enable_all_inputs()

    def start_image_retrieval_after_txt(self):
        """TXT处理完成后直接开始图片检索"""
        try:
            folder_path = self.folder_path_edit.text()
            if not folder_path:
                QMessageBox.warning(self, "错误", "文件夹路径无效")
                # 路径无效时重新启用按钮
                self.enable_buttons_based_on_state()
                self.enable_all_inputs()
                return

            # 检查图库路径
            library_path = ""
            if HAS_IMAGE_INDEXER and hasattr(self, 'library_path_edit'):
                library_path = self.library_path_edit.text()
                if not library_path:
                    QMessageBox.warning(self, "错误", "请先设置图库路径")
                    # 图库路径未设置时重新启用按钮
                    self.enable_buttons_based_on_state()
                    self.enable_all_inputs()
                    return
            else:
                QMessageBox.warning(self, "错误", "图片检索功能不可用")
                # 功能不可用时重新启用按钮
                self.enable_buttons_based_on_state()
                self.enable_all_inputs()
                return

            # 扫描生成的总操作表格文件
            self.update_log("🔍 扫描TXT生成的总操作表格文件...")
            processed_tables = {}

            for root, _, files in os.walk(folder_path):
                for file in files:
                    if file.lower().endswith('.xlsx') and '_总操作表格' in file:
                        file_path = os.path.join(root, file)
                        self.update_log(f"✅ 找到总操作表格: {file}")

                        # 直接使用总操作表格进行图片检索
                        # 生成最终的已检索文件路径
                        base_name = file.replace('_总操作表格.xlsx', '')
                        output_file_path = os.path.join(root, f"{base_name}_已检索.xlsx")

                        # 构建处理数据结构，直接使用总操作表格
                        processed_tables[file_path] = {
                            'output_file': output_file_path,  # 直接指向最终输出文件
                            'source_file': file_path,  # 源文件是总操作表格
                            'sheet_data': {},
                            'row_count': 0,
                            'column_mapping': {}
                        }

            if not processed_tables:
                QMessageBox.warning(self, "错误", "没有找到TXT生成的总操作表格文件")
                # 没有找到文件时重新启用按钮
                self.enable_buttons_based_on_state()
                self.enable_all_inputs()
                return

            self.update_log(f"📊 找到 {len(processed_tables)} 个总操作表格文件")

            # 设置已处理的表格数据
            self.processed_tables = processed_tables

            # 设置图库路径用于处理
            self.library_path_for_processing = library_path

            # 直接开始图片检索（按钮保持禁用状态，直到检索完成）
            self.start_image_retrieval_automatically()

        except Exception as e:
            self.update_log(f"启动图片检索时出错: {str(e)}")
            QMessageBox.warning(self, "错误", f"启动图片检索时出错: {str(e)}")
            # 异常时重新启用按钮
            self.enable_buttons_based_on_state()
            self.enable_all_inputs()

    def start_complete_processing(self):
        """开始完整处理（表格解析 + 图片检索）"""
        folder_path = self.folder_path_edit.text()
        if not folder_path:
            QMessageBox.warning(self, "错误", "请先选择表格文件夹")
            return

        # 检查图库路径
        library_path = ""
        if HAS_IMAGE_INDEXER and hasattr(self, 'library_path_edit'):
            library_path = self.library_path_edit.text()
            if not library_path:
                self.update_log("⚠️ 未选择图库文件夹，将只进行表格解析，不进行图片检索")
                # 直接继续，不弹窗确认

        # 设置处理模式标志
        self.complete_processing_mode = True
        self.library_path_for_processing = library_path

        # 更新状态和UI
        self.update_status("正在进行多表格解析...")
        self.disable_all_buttons()
        self.statusBar().showMessage('开始多表格完整处理...')

        # 清空日志并显示开始信息
        self.log_text.clear()
        self.update_log("=" * 60)
        self.update_log("🚀 开始多表格完整处理（表格解析 + 图片检索 + 图片尺寸修改）")
        if library_path:
            self.update_log(f"📚 图库路径: {library_path}")
        else:
            self.update_log("⚠️ 未设置图库路径，将只进行表格解析")

        # 在开始处理前，先清理旧的已处理文件和 .cp5 文件
        # 清理 .cp5 文件
        self.clean_cp5_files(folder_path)

        # 清理已处理文件
        clean_success = self.clean_processed_files(folder_path)

        # 如果清理失败（文件被占用），停止处理
        if not clean_success:
            return

        # 开始表格处理（跳过清理，因为已经清理过了）
        self.process_files(folder_path, skip_cleanup=True)

    def start_table_processing_only(self):
        """仅开始表格处理"""
        folder_path = self.folder_path_edit.text()
        if not folder_path:
            QMessageBox.warning(self, "错误", "请先选择表格文件夹")
            return

        # 在开始处理前，先清理旧的已处理文件和 .cp5 文件
        self.update_log("=" * 60)
        self.update_log("🚀 开始表格处理任务")

        # 清理 .cp5 文件
        self.clean_cp5_files(folder_path)

        # 清理已处理文件
        clean_success = self.clean_processed_files(folder_path)

        # 如果清理失败（文件被占用），停止处理
        if not clean_success:
            return

        # 设置处理模式标志
        self.complete_processing_mode = False
        self.library_path_for_processing = ""

        # 更新状态和UI
        self.update_status("正在进行表格解析...")
        self.disable_all_buttons()
        self.statusBar().showMessage('开始表格处理...')
        self.update_log("开始表格解析...")

        # 开始表格处理（跳过清理，因为已经清理过了）
        self.process_files(folder_path, skip_cleanup=True)

    def update_status(self, message):
        """更新状态标签"""
        if hasattr(self, 'status_label'):
            self.status_label.setText(f"状态: {message}")

    def disable_all_buttons(self):
        """禁用所有操作按钮"""
        if hasattr(self, 'process_txt_button'):
            self.process_txt_button.setEnabled(False)

    def enable_buttons_based_on_state(self):
        """根据当前状态启用相应的按钮"""
        folder_path = self.folder_path_edit.text()
        has_folder = bool(folder_path)

        # 调试信息
        self.update_log(f"🔄 更新按钮状态 - 文件夹: {folder_path if has_folder else '未选择'}")

        # 检查是否有图库路径和已处理的表格数据
        has_library = False
        has_processed_data = False

        if HAS_IMAGE_INDEXER and hasattr(self, 'library_path_edit'):
            library_path = self.library_path_edit.text()
            has_library = bool(library_path)

        if hasattr(self, 'processed_tables'):
            has_processed_data = bool(self.processed_tables)

        # 检查文件夹中是否有txt文件
        has_txt_files = False
        if has_folder:
            try:
                for _, _, files in os.walk(folder_path):
                    for file in files:
                        if file.lower().endswith('.txt'):
                            has_txt_files = True
                            break
                    if has_txt_files:
                        break
            except Exception:
                pass

        # 检查文件夹中是否有Excel文件（包括子目录中的总操作表格文件）
        has_excel_files = False
        if has_folder:
            try:
                for _, _, files in os.walk(folder_path):
                    for file in files:
                        file_lower = file.lower()
                        if file_lower.endswith(('.xlsx', '.xls', '.csv')):
                            # 排除已处理的结果文件，但保留原始文件和TXT生成的总操作表格
                            if "_已检索" not in file_lower:
                                has_excel_files = True
                                break
                    if has_excel_files:
                        break
            except Exception:
                pass

        # 调试信息
        self.update_log(f"📊 文件检测结果: TXT={has_txt_files}, Excel={has_excel_files}, 图库={has_library}, 已处理数据={has_processed_data}")

        # 启用按钮
        if hasattr(self, 'process_txt_button'):
            enabled = has_folder and has_txt_files
            self.process_txt_button.setEnabled(enabled)
            self.update_log(f"🔘 TXT处理按钮: {'启用' if enabled else '禁用'}")

        # 更新状态显示
        if has_folder:
            status_parts = []
            action_hints = []

            if has_txt_files:
                status_parts.append("TXT文件")
                action_hints.append("点击'TXT文件解析'开始处理")

            if has_excel_files:
                status_parts.append("Excel表格")
                if has_library:
                    action_hints.append("可进行图片检索")
                else:
                    action_hints.append("设置图库路径后可进行图片检索")

            if status_parts:
                status_text = f"就绪，发现: {' 和 '.join(status_parts)}"
                if action_hints:
                    status_text += f" | {action_hints[0]}"
                self.update_status(status_text)
            else:
                self.update_status("文件夹中没有找到可处理的文件")
        else:
            self.update_status("请选择文件夹开始处理")

    def update_log(self, message: str) -> None:
        """
        更新日志显示
        
        Args:
            message: 日志消息
        """
        self.log_text.append(message)
        # 滚动到底部以显示最新日志
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)

    def update_progress(self, value: int) -> None:
        """
        更新总进度条
        
        Args:
            value: 进度值
        """
        self.progress_bar.setValue(value)
        # 更新状态栏显示总进度
        self.statusBar().showMessage(f"总进度：{value}%")
        # 强制立即更新UI
        QApplication.processEvents()

    def clean_cp5_files(self, folder_path):
        """删除目录下所有 .cp5 文件"""
        try:
            if not os.path.exists(folder_path):
                return True

            self.update_log("🗑️ 开始清理目录下的 .cp5 文件...")

            cp5_files_deleted = 0

            # 递归遍历文件夹，查找并删除所有 .cp5 文件
            for root, _, files in os.walk(folder_path):
                for file in files:
                    if file.lower().endswith('.cp5'):
                        file_path = os.path.join(root, file)
                        try:
                            os.remove(file_path)
                            cp5_files_deleted += 1
                            self.update_log(f"  ✅ 删除 .cp5 文件: {os.path.relpath(file_path, folder_path)}")
                        except Exception as e:
                            self.update_log(f"  ❌ 删除 .cp5 文件失败: {os.path.relpath(file_path, folder_path)} - {str(e)}")

            if cp5_files_deleted > 0:
                self.update_log(f"✅ .cp5 文件清理完成，共删除 {cp5_files_deleted} 个文件")
            else:
                self.update_log("✨ 未发现 .cp5 文件，无需清理")

            return True

        except Exception as e:
            self.update_log(f"❌ 清理 .cp5 文件时出错: {str(e)}")
            return True  # 即使清理失败也继续处理，不阻塞主任务

    def clean_processed_files(self, folder_path):
        """清理目录下所有已处理的表格文件和文件夹，支持多表格处理"""
        try:
            if not os.path.exists(folder_path):
                return True  # 返回成功状态

            self.update_log("🧹 开始清理目录下的已处理文件...")

            # 定义需要清理的文件后缀模式
            # 注意：不清理 _总操作表格.xlsx，因为它是源文件
            patterns_to_clean = [
                '_总操作表格.xlsx',  # 清理废弃的中间文件
                '_已检索.xlsx',
                '_排列算法RPA操作.xlsx'
            ]

            cleaned_files = []
            cleaned_folders = []
            file_in_use_errors = []  # 记录文件占用错误

            # 遍历文件夹中的所有项目
            items_to_process = os.listdir(folder_path)
            self.update_log(f"📂 扫描到 {len(items_to_process)} 个项目需要检查")

            for item_name in items_to_process:
                item_path = os.path.join(folder_path, item_name)

                # 检查是否为需要清理的文件
                if os.path.isfile(item_path):
                    for pattern in patterns_to_clean:
                        if item_name.endswith(pattern):
                            try:
                                os.remove(item_path)
                                cleaned_files.append(item_name)
                                self.update_log(f"  ✅ 已删除旧文件: {item_name}")
                            except Exception as e:
                                error_msg = str(e)
                                self.update_log(f"  ❌ 删除文件失败 {item_name}: {error_msg}")

                                # 检查是否为文件被占用错误（WinError 32）
                                if "WinError 32" in error_msg or "另一个程序正在使用此文件" in error_msg:
                                    file_in_use_errors.append(item_name)
                            break

                # 检查是否为文件夹，删除所有文件夹（这些可能是之前处理生成的图片文件夹）
                elif os.path.isdir(item_path):
                    try:
                        shutil.rmtree(item_path)
                        cleaned_folders.append(item_name)
                        self.update_log(f"  ✅ 已删除文件夹: {item_name}")
                    except Exception as e:
                        error_msg = str(e)
                        self.update_log(f"  ❌ 删除文件夹失败 {item_name}: {error_msg}")

                        # 检查是否为文件被占用错误
                        if "WinError 32" in error_msg or "另一个程序正在使用此文件" in error_msg:
                            file_in_use_errors.append(item_name)

            # 如果有文件被占用，弹窗提醒并重置任务
            if file_in_use_errors:
                file_list = "\n".join(file_in_use_errors)
                QMessageBox.warning(
                    self,
                    "文件被占用",
                    f"以下文件正被其他程序使用，无法删除：\n\n{file_list}\n\n"
                    f"请关闭相关程序后重试。\n任务已重置，请重新开始处理。"
                )

                # 重置全部任务
                self.reset_all_tasks()
                return False  # 返回失败状态

            # 显示清理统计
            total_cleaned = len(cleaned_files) + len(cleaned_folders)
            if total_cleaned > 0:
                self.update_log(f"📊 清理统计:")
                self.update_log(f"  • 删除文件: {len(cleaned_files)} 个")
                self.update_log(f"  • 删除文件夹: {len(cleaned_folders)} 个")
                self.update_log(f"  • 总计清理: {total_cleaned} 个项目")
            else:
                self.update_log("✨ 目录已经是干净的，无需清理")

            self.update_log("✅ 文件清理完成，可以开始多表格处理")
            return True  # 返回成功状态

        except Exception as e:
            self.update_log(f"❌ 清理文件时出错: {str(e)}")
            return False  # 返回失败状态

    def reset_all_tasks(self):
        """重置全部任务状态"""
        try:
            # 停止正在运行的Worker线程
            if hasattr(self, 'worker') and self.worker and self.worker.isRunning():
                self.worker.terminate()
                self.worker.wait(3000)  # 等待最多3秒
                self.worker = None

            # 停止正在运行的图片检索线程
            if hasattr(self, 'image_retrieval_worker') and self.image_retrieval_worker and self.image_retrieval_worker.isRunning():
                self.image_retrieval_worker.terminate()
                self.image_retrieval_worker.wait(3000)
                self.image_retrieval_worker = None

            # 重置处理状态标志
            if hasattr(self, 'complete_processing_mode'):
                self.complete_processing_mode = False
            if hasattr(self, 'library_path_for_processing'):
                self.library_path_for_processing = None

            # 清空已处理的表格数据
            if hasattr(self, 'processed_tables'):
                self.processed_tables = {}

            # 重置进度条
            if hasattr(self, 'progress_bar'):
                self.progress_bar.setValue(0)

            # 重新启用所有按钮和输入框
            self.enable_buttons_based_on_state()
            self.enable_all_inputs()

            # 更新状态显示
            self.update_status("任务已重置")
            self.statusBar().showMessage('任务已重置，可以重新开始处理')
            self.update_log("=== 任务已重置，可以重新开始处理 ===")

        except Exception as e:
            self.update_log(f"重置任务时出错: {str(e)}")

    def process_files(self, folder_path=None, skip_cleanup=False):
        """处理文件夹中的数据"""
        # 如果未指定文件夹，使用输入框中的值
        if not folder_path:
            folder_path = self.folder_path_edit.text()

        if not folder_path:
            QMessageBox.warning(self, "错误", "请先选择一个文件夹")
            return

        # 在开始处理前，先清理旧的已处理文件和 .cp5 文件（除非跳过清理）
        if not skip_cleanup:
            self.update_log("=" * 60)
            self.update_log("🚀 开始多表格处理任务")

            # 清理 .cp5 文件
            self.clean_cp5_files(folder_path)

            # 清理已处理文件
            clean_success = self.clean_processed_files(folder_path)

            # 如果清理失败（文件被占用），停止处理
            if not clean_success:
                return

        # 获取多宽度参数（从实例变量获取，已在初始化时从supabase获取）
        width_values = getattr(self, 'width_values', DEFAULT_WIDTH_VALUES)

        # 使用多宽度参数和材质宽幅配置
        column_mapping = {
            'width_values': width_values,
            'width': width_values[0],  # 保持向后兼容性
            'no_image_keywords': [],  # 不再使用无图识别关键字
            # 传递材质宽幅配置
            'texture_categories': getattr(self, 'texture_categories', {}),
            'sub_to_base_texture': getattr(self, 'sub_to_base_texture', {}),
            'base_texture_mappings': getattr(self, 'base_texture_mappings', {}),
            'global_width_ranges': getattr(self, 'global_width_ranges', []),
            'global_sheet_mapping': getattr(self, 'global_sheet_mapping', {}),
            'global_width_arr': getattr(self, 'global_width_arr', width_values)
        }

        # 显示处理配置
        self.update_log("⚙️ 处理配置:")
        self.update_log(f"  • 目标文件夹: {folder_path}")
        self.update_log(f"  • 全局宽度参数: {', '.join(map(str, width_values))}cm")

        # 显示材质宽幅配置信息
        texture_categories = getattr(self, 'texture_categories', {})
        if texture_categories:
            self.update_log(f"  • 材质宽幅配置: 已加载 {len(texture_categories)} 个材质的专用配置")
            for base_texture, details in list(texture_categories.items())[:3]:  # 只显示前3个
                width_arr = details.get('width_arr')
                if width_arr:
                    self.update_log(f"    - {base_texture}: {', '.join(map(str, width_arr))}cm")
            if len(texture_categories) > 3:
                self.update_log(f"    - ... 还有 {len(texture_categories) - 3} 个材质配置")
        else:
            self.update_log(f"  • 材质宽幅配置: 使用全局配置（未连接到云端数据库）")

        self.update_log(f"  • DPI设置: {self.dpi_input.text() or '72'}")

        # 显示热键配置
        if self.hotkey_enabled_checkbox.isChecked():
            hotkey_key = self.hotkey_combo.currentText()
            self.update_log(f"  • 完成热键: Ctrl+Shift+{hotkey_key} (已启用)")
        else:
            self.update_log(f"  • 完成热键: 已禁用")

        self.update_log("=" * 60)

        # 禁用所有按钮，防止重复操作
        for button in self.findChildren(QPushButton):
            button.setEnabled(False)

        # 禁用所有输入框
        self.dpi_input.setEnabled(False)
        if hasattr(self, 'boundary1_input'):
            self.boundary1_input.setEnabled(False)
        if hasattr(self, 'boundary2_input'):
            self.boundary2_input.setEnabled(False)
        if hasattr(self, 'increment1_input'):
            self.increment1_input.setEnabled(False)
        if hasattr(self, 'increment2_input'):
            self.increment2_input.setEnabled(False)
        if hasattr(self, 'increment3_input'):
            self.increment3_input.setEnabled(False)

        # 创建Worker线程处理文件
        self.worker = Worker(
            folder_path=folder_path,
            openai_mgr=self.openai_mgr,
            is_structured_data=False,  # 自动检测模式下统一设为False
            column_mapping=column_mapping,
            image_finder_config=self.image_finder_config if hasattr(self, 'image_finder_config') else None
        )

        # 连接信号槽
        self.worker.progress.connect(self.update_progress)
        self.worker.log.connect(self.update_log)
        self.worker.finished.connect(self.process_finished)

        # 启动Worker线程
        self.worker.start()

    def select_library_folder(self):
        """选择图库文件夹"""
        if not HAS_IMAGE_INDEXER:
            QMessageBox.warning(self, "错误", "图库索引器模块未找到，无法使用图库功能")
            return

        # 获取上次选择的路径作为默认路径
        last_path = self.library_path_edit.text() if self.library_path_edit.text() else os.path.expanduser('~')

        folder = QFileDialog.getExistingDirectory(self, '选择图库文件夹', last_path)
        if folder:
            self.library_path_edit.setText(folder)
            self.update_log(f"已选择图库文件夹: {folder}")

            # 更新按钮状态
            self.enable_buttons_based_on_state()

            # 自动保存路径
            self.save_column_config()

    def on_width_changed_internal(self, width_values):
        """内部宽度变化处理函数"""
        try:
            # 更新实例变量
            self.width_values = width_values

            # 更新日志显示解析结果
            if len(width_values) == 1:
                self.update_log(f"宽度参数已更新为: {width_values[0]}cm")
            else:
                self.update_log(f"多宽度参数已更新为: {', '.join(map(str, width_values))}cm")
                self.update_log(f"将创建 {len(width_values)} 个宽度sheet和1个超宽幅sheet")

        except Exception as e:
            log.error(f"处理宽度参数时出错: {str(e)}")
            self.update_log("宽度参数处理失败，将使用默认值")

if __name__ == '__main__':
    app = QApplication(sys.argv)

    # 初始化SupabaseHelper
    supabase_helper = SupabaseHelper()

    # 显示登录对话框
    login_dialog = LoginDialog(supabase_helper, ROBOT_SMART_NAME, ROBOT_CURRENT_VERSION)
    if login_dialog.exec() == 1:  # 1 表示 QDialog.Accepted
        # 登录成功，创建主界面
        window = TxtToExcelApp()

        # 设置SupabaseHelper引用
        window.supabase_helper = supabase_helper

        # 检查版本（必须在设置supabase_helper之后）
        window.check_version()

        # 初始化高级设置授权管理器
        window.settings_auth = AdvancedSettingsAuth(supabase_helper)

        # 高级设置授权管理器初始化完成

        # 初始化OpenAIClientManager以使用SupabaseHelper
        window.openai_mgr = OpenAIClientManager(supabase_helper)
        window.openai_mgr.initialized.connect(window.handle_openai_init)
        window.openai_mgr.status_update.connect(window.update_log)

        # 在主界面日志区域显示登录成功信息
        if supabase_helper.is_connected():
            try:
                email = supabase_helper.get_user_email()
                if email:
                    window.update_log(f"用户 {email} 登录成功")
                    window.update_log("正在初始化应用...")
                else:
                    window.update_log("用户登录成功")
            except Exception as e:
                window.update_log(f"获取用户信息时出错: {e}")

        # 显示主界面
        window.show()
        sys.exit(app.exec())
    else:
        # 用户取消登录，退出程序
        sys.exit(0)